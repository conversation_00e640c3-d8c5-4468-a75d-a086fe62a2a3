import { AppConfigSettingItem } from 'src/api/config/setting-contracts';
import { expect, test } from '../base-test';

const settingsToCleanUp = [];

test.describe.configure({ mode: 'serial' });
test.use({ useUserWithRole: 'ct_configurator' });
test.describe('Config api', async () => {
  test.describe('Health Check Endpoints', async () => {
    test('[@C3684716] - GET /config/healthcheck - should return basic health status', async ({ api }) => {
      const result = await api.get('config/healthcheck');
      const data = await result.json();

      expect(result.status()).toEqual(200);
      expect(data).toHaveProperty('sha');
      expect(data).toHaveProperty('status');
      expect(['ONLINE', 'OFFLINE', 'NOT_IN_USE', 'UNHEALTHY', 'UNKOWN']).toContain(data.status);
    });

    test('[@C3684717] - GET /config/healthcheck/full - should return detailed health status', async ({ api }) => {
      const result = await api.get('config/healthcheck/full');
      const data = await result.json();

      expect(result.status()).toEqual(200);
      expect(data).toHaveProperty('sha');
      expect(data).toHaveProperty('status');
      expect(data).toHaveProperty('subSystems');
      expect(data.subSystems).toHaveProperty('redis');
      expect(data.subSystems).toHaveProperty('bigQuery');
      expect(data.subSystems).toHaveProperty('auth0');
    });
  });

  test.describe('User Writable Settings', async () => {
    test('[@C3684723] - PUT /config/user-writable - should create new user setting', async ({ api }) => {
      const userSetting = {
        name: `user-favorites-${Date.now()}`,
        dataType: 'json',
        value: {
          favorites: ['route-1', 'route-2'],
        },
        levelToUpdate: 'user',
        group: 'user-writable',
      };

      const result = await api.put('config/user-writable', { data: userSetting });
      const setting = await result.json();

      expect(result.status()).toEqual(201);
      expect(setting).toHaveProperty('id');
      expect(setting.name).toEqual(userSetting.name);
      expect(setting.source).toEqual('user');

      settingsToCleanUp.push(setting.id);
    });
  });

  test.describe('Setting - GET', async () => {
    test(`[@C3684724] - verify it returns the expected data structure.`, async ({ api }) => {
      const result = await api.get('config/settings');
      const data: AppConfigSettingItem[] = await result.json();

      expect(data).toBeDefined();

      for (let i = 0; i < data.length; i++) {
        expect(data[i].dataType).toBeDefined();
        expect(data[i].id).toBeDefined();
        expect(data[i].name).toBeDefined();
        expect(data[i].source).toBeDefined();
        expect(data[i].value).toBeDefined();
      }
    });

    test(`[@C3684726] - verify we can get just the feature flags.`, async ({ api }) => {
      const result = await api.get('config/settings', { params: { group: 'feature-flags' } });
      const data: AppConfigSettingItem[] = await result.json();

      expect(data).toBeDefined();

      for (let i = 0; i < data.length; i++) {
        expect(data[i].dataType).toBeDefined();
        expect(data[i].id).toBeDefined();
        expect(data[i].name).toBeDefined();
        expect(data[i].source).toBeDefined();
        expect(data[i].value).toBeDefined();
        expect(typeof data[i].value === 'boolean').toBeTruthy();
        expect(data[i].group).toContain('feature-flags');
      }
    });
  });

  test.describe('Setting - PUT', async () => {
    test(`[@C3684727] - Verify create able to create DefaultSetting.`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-default-config-setting-${Date.now()}`,
        description: 'Sample default setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'default',
        group: 'test-group',
      };

      // Act...
      const result = await api.put('config/settings', { data: facilitySetting });
      const settingId = await result.json();

      // Assert...
      expect(result.status()).toEqual(201);

      settingsToCleanUp.push(settingId.id);
    });

    test(`[@C3684728] - Verify update able to create DefaultSetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-default-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'default',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Update the setting...
      facilitySetting.value = 'test value 04';
      facilitySetting.id = setting.id;
      const result = await api.put('config/settings', { data: facilitySetting });

      // Assert...
      expect(result.status()).toEqual(200);
      settingsToCleanUp.push(facilitySetting.id);
    });

    test(`[@C3684729] - Verify delete able to create DefaultSetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-default-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'default',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Act...
      const result = await api.delete(`config/settings/${setting.id}`);

      // Assert...
      expect(result.status()).toEqual(200);
    });

    test(`[@C3684730] - Verify create able to create TenantSetting.`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-tenant-config-setting-${Date.now()}`,
        description: 'Sample default setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'tenant',
        group: 'test-group',
      };

      // Act...
      const result = await api.put('config/settings', { data: facilitySetting });
      const settingId = await result.json();

      // Assert...
      expect(result.status()).toEqual(201);

      settingsToCleanUp.push(settingId.id);
    });

    test(`[@C3684731] - Verify update able to create TenantSetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-tenant-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'tenant',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Update the setting...
      facilitySetting.value = 'test value 04';
      facilitySetting.id = setting.id;
      const result = await api.put('config/settings', { data: facilitySetting });

      // Assert...
      expect(result.status()).toEqual(200);
      settingsToCleanUp.push(facilitySetting.id);
    });

    test(`[@C3684732] - Verify delete able to create TenantSetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-tenant-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'tenant',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Act...
      const result = await api.delete(`config/settings/${setting.id}`);

      // Assert...
      expect(result.status()).toEqual(200);
    });

    test(`[@C3684733] - Verify create able to create UserSetting.`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-user-config-setting-${Date.now()}`,
        description: 'Sample default setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'user',
        group: 'test-group',
      };

      // Act...
      const result = await api.put('config/settings', { data: facilitySetting });
      const settingId = await result.json();

      // Assert...
      expect(result.status()).toEqual(201);

      settingsToCleanUp.push(settingId.id);
    });

    test(`[@C3684734] - Verify update able to create UserSetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-user-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'user',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Update the setting...
      facilitySetting.value = 'test value 04';
      facilitySetting.id = setting.id;
      const result = await api.put('config/settings', { data: facilitySetting });

      // Assert...
      expect(result.status()).toEqual(200);
      settingsToCleanUp.push(facilitySetting.id);
    });

    test(`[@C3684735] - Verify delete able to create UserSetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-user-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'user',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Act...
      const result = await api.delete(`config/settings/${setting.id}`);

      // Assert...
      expect(result.status()).toEqual(200);
    });

    test(`[@C3684736] - Verify not create able to create a Setting with an unsupported level.`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-user-config-setting-${Date.now()}`,
        description: 'Sample default setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'not-a-valid-level',
        group: 'test-group',
      };

      // Act...
      const result = await api.put('config/settings', { data: facilitySetting });

      // Assert...
      expect(result.status()).toEqual(422);
    });

    // test.use({ selectedFacilityId: Defaults.FACILITY_ID });
    test(`[@C3684737] - Verify create able to create FacilitySetting.`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-facility-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'facility',
        group: 'test-group',
      };

      // Act...
      const result = await api.put('config/settings', { data: facilitySetting });
      const settingId = await result.json();

      // Assert...
      expect(result.status()).toEqual(201);

      settingsToCleanUp.push(settingId.id);
    });

    test(`[@C3684738] - Verify update able to create FacilitySetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-facility-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'facility',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Update the setting...
      facilitySetting.value = 'test value 04';
      facilitySetting.id = setting.id;
      const result = await api.put('config/settings', { data: facilitySetting });

      // Assert...
      expect(result.status()).toEqual(200);
      settingsToCleanUp.push(facilitySetting.id);
    });

    test(`[@C3684739] - Verify delete able to create FacilitySetting`, async ({ api }) => {
      const facilitySetting = {
        id: undefined,
        name: `new-facility-config-setting-${Date.now()}`,
        description: 'Sample facility setting',
        dataType: 'string',
        value: 'test value 03',
        levelToUpdate: 'facility',
        group: 'test-group',
      };
      // Create a setting...
      const createResult = await api.put('config/settings', { data: facilitySetting });
      expect(createResult.status()).toEqual(201);
      const setting = await createResult.json();

      // Act...
      const result = await api.delete(`config/settings/${setting.id}`);

      // Assert...
      expect(result.status()).toEqual(200);
    });
  });

  test.describe('GET - facility-config', async () => {
    test('[@C3530861] - GET /config/facility-config - should return a correct response when a facility id is requested', async ({
      api,
    }) => {
      const result = await api.get('config/facility-config');
      expect(result.status()).toEqual(200);
      const data: AppConfigSettingItem[] = await result.json();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBeTruthy();

      for (let i = 0; i < data.length; i++) {
        expect(data[i].id).toBeDefined();
        expect(data[i].name).toBeDefined();
        expect(data[i].dataType).toBeDefined();
        expect(data[i].source).toBeDefined();
        expect(data[i].value).toBeDefined();
      }
    });

    test('[@C3669214] - GET /config/facility-config - should return a valid error response if the user sends an invalid facility id', async ({
      api,
    }) => {
      const result = await api.get('config/facility-config', {
        headers: {
          'ict-facility-id': 'invalid_facility',
        },
      });
      expect(result.status()).toEqual(401);
    });

    test('[@C3669214] - GET /config/facility-config - should return a valid unauthorized response if the bearer token is not available', async ({
      api,
    }) => {
      const result = await api.get('config/facility-config', {
        headers: {
          Authorization: 'invalid_bearer_token',
        },
      });
      expect(result.status()).toEqual(401);
    });

    test.use({ selectedFacilityId: undefined });
    test(`[@C3684709] - GET /config/facility-config - should return a correct response when no facility id is provided.`, async ({
      api,
    }) => {
      const result = await api.get('config/facility-config');
      expect(result.status()).toEqual(200);
      const data = await result.json();
      expect(data).toBeInstanceOf(Array);
      expect(data.length).toBeGreaterThan(0);
    });
  });

  test.describe('Setting Logs', async () => {
    test('[@C3684750] - GET /config/setting-logs - should return setting change history', async ({ api }) => {
      // First create a setting to have some logs
      const testSetting = {
        id: undefined,
        name: `test-setting-${Date.now()}`,
        dataType: 'string',
        value: 'initial value',
        levelToUpdate: 'tenant',
        group: 'test-group',
      };

      const createResult = await api.put('config/settings', { data: testSetting });
      const setting = await createResult.json();
      settingsToCleanUp.push(setting.id);

      // Update the setting to generate a log
      testSetting.id = setting.id;
      testSetting.value = 'updated value';
      await api.put('config/settings', { data: testSetting });

      // Get the logs
      const result = await api.get(`config/setting-logs?setting_id=${setting.id}`);
      const logs = await result.json();

      expect(result.status()).toEqual(200);
      expect(logs).toHaveProperty('data');
      expect(Array.isArray(logs.data)).toBeTruthy();
      expect(logs.data.length).toBeGreaterThan(0);
      expect(logs.data[0]).toHaveProperty('changedBy');
      expect(logs.data[0]).toHaveProperty('timestamp');
      expect(logs.data[0]).toHaveProperty('source');
      expect(logs.data[0]).toHaveProperty('newValue');
    });
  });

  test.describe('Curated Data', async () => {
    test('[@C3684749] - GET /config/curated-tables/list - should return list of available tables', async ({ api }) => {
      const result = await api.get('config/curated-tables/list');
      expect(result.status()).toEqual(200);

      const tables = await result.json();
      expect(Array.isArray(tables)).toBeTruthy();
    });

    test.use({ useUserWithRole: 'ct_configurator' });
    test('[@C3684748] - GET /config/v2/curated-data - should return table data', async ({ api }) => {
      // First get the list of available tables
      const listResult = await api.get('config/curated-tables/list');
      const tables = await listResult.json();

      if (tables.length > 0) {
        const result = await api.get(`config/v2/curated-data?table=${tables[0]}`);

        if (result.status() === 200) {
          expect(result.status()).toEqual(200);
          const data = await result.json();
          expect(Array.isArray(data)).toBeTruthy();
          if (data.length > 0) {
            expect(data[0]).toHaveProperty('columns');
            expect(Array.isArray(data[0].columns)).toBeTruthy();
          }
        } else {
          expect(result.status()).toEqual(204);
        }
      }
    });
  });

  // Clean up any settings created during tests
  test.afterAll('clean up', async ({ api }) => {
    for (const settingId of settingsToCleanUp) {
      await api.delete(`config/settings/${settingId}`);
    }
  });
});
