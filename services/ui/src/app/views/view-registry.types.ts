import type { ComponentType, LazyExoticComponent } from "react";
import type { AppConfigSetting } from "../config/hooks/use-config";
/**
 * The base view props that all Views will use.
 */
export interface BaseViewProps {
  id: string;
  setting?: AppConfigSetting;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any;
}

/**
 * The view definition to map a widget id to React lazily loaded components.
 */
export interface ViewDefinition {
  id: string;
  component: LazyExoticComponent<ComponentType<BaseViewProps>>;
}

/**
 * The base view options props that all Views will use.
 */
export interface BaseViewOptionsProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange: (options: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any;
  onClose: () => void;
  onSave: () => void;
}
