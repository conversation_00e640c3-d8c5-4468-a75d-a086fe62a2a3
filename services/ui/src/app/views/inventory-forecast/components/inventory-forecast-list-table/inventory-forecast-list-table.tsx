import { Link } from "@carbon/react";
import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback, useMemo, useState } from "react";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import { InventoryForecastItem } from "./types";
import { InventoryForecastListDrawer } from "../inventory-forecast-list-drawer/inventory-forecast-list-drawer";
import { UnitUtil } from "../../../../utils/unit-util";
import { PickListExportDialog } from "../pick-list-export-dialog/pick-list-export-dialog";
import { ExportModal } from "../../../../components/export-modal/export-modal";
import { transformFilters } from "../../../../api/util/filter-transform-util";
import { ictApi } from "../../../../api/ict-api";

interface InventoryForecastListTableProps {
  data: InventoryForecastItem[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error?: Error;
  rowCount: number;
  onRefresh?: () => void;
  inventoryUpdatedAt?: string;
  forecastPerformedAt?: string;
  globalFilter: string;
  setGlobalFilter: (globalFilter: string) => void;
}
export function InventoryForecastListTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  columnFilters,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onRefresh,
  inventoryUpdatedAt,
  forecastPerformedAt,
  globalFilter,
  setGlobalFilter,
}: InventoryForecastListTableProps) {
  // Add state for row selection
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  // New state for drawer
  const [selectedSku, setSelectedSku] = useState<string | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState<boolean>(false);
  const [isExportExcelModalOpen, setIsExportExcelModalOpen] =
    useState<boolean>(false);
  // Handle clearing row selection
  const handleClearSelection = useCallback(() => {
    setRowSelection({});
  }, []);
  // Updated SKU click handler to open the drawer
  const handleSkuClick = useCallback((sku: string) => {
    setSelectedSku(sku);
    setIsDrawerOpen(true);
  }, []);

  const selectedRows = useMemo(
    () => Object.keys(rowSelection).map((index) => data[parseInt(index)]),
    [data, rowSelection],
  );

  const exportOptions = useMemo(
    () => [
      {
        id: "pickList",
        text: "Pick List",
        disabled: selectedRows.length === 0,
      },
      {
        id: "excel",
        text: "Excel",
      },
    ],
    [selectedRows.length],
  );

  const handleExportAction = useCallback(
    (action: string) => {
      if (action === "pickList" && selectedRows.length > 0) {
        setIsExportDialogOpen(true);
      }
      if (action === "excel") {
        setIsExportExcelModalOpen(true);
      }
    },
    [selectedRows.length],
  );

  const getColumnDefinitions = () => {
    return [
      { id: "sku", header: "SKU" },
      { id: "current.reserveStorage", header: "Reserve Storage" },
      { id: "current.forwardPick", header: "Forward Pick" },
      { id: "projected.pendingReplenishment", header: "Pending Replenishment" },
      { id: "projected.pendingPicks", header: "Pending Picks" },
      { id: "projected.allocatedOrders", header: "Allocated Orders" },
      { id: "projected.projectedForwardPick", header: "Forward Pick" },
      { id: "forecast.averageReplenishment", header: "Average Replenishment" },
      { id: "forecast.averageDemand", header: "Average Demand" },
      { id: "forecast.demandTomorrow", header: "Demand Tomorrow" },
      { id: "forecast.knownDemand", header: "Known Demand" },
      { id: "forecast.forwardPickTomorrow", header: "Forward Pick Tomorrow" },
      { id: "forecast.twoDayDemand", header: "Two-Day Demand" },
      { id: "forecast.twoDayForwardPick", header: "Two-Day Forward Pick" },
    ];
  };

  const handleExportExcelModalClose = () => {
    setIsExportExcelModalOpen(false);
  };

  const handleExportExcel = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");

    const { error, data: blob } = await ictApi.fetchClient.POST(
      "/inventory/forecast/list/export",
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: transformFilters(columnFilters),
          sortFields: sorting.map((sort) => ({
            columnName: sort.id,
            isDescending: sort.desc,
          })),
          columns: selectedColumns,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
        parseAs: "blob",
        signal,
      },
    );

    if (error) {
      throw new Error("Export failed:", error);
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Define columns using createColumnHelper
  const columnHelper = createColumnHelper<InventoryForecastItem>();
  const columns = [
    // SKU column (not grouped)
    columnHelper.accessor("sku", {
      header: "SKU",
      size: 150,
      cell: (info) => (
        <Link
          onClick={(e) => {
            e.stopPropagation();
            handleSkuClick(info.getValue());
          }}
          style={{ cursor: "pointer" }}
        >
          {info.getValue()}
        </Link>
      ),
    }),
    // Current group
    columnHelper.group({
      header: "Current",
      columns: [
        columnHelper.accessor((row) => row.current.reserveStorage, {
          id: "reserveStorage",
          header: "Reserve Storage",
          size: 140,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.current.forwardPick, {
          id: "forwardPick",
          header: "Forward Pick",
          size: 140,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
      ],
    }),
    // Projected group
    columnHelper.group({
      header: "Projected",
      columns: [
        columnHelper.accessor((row) => row.projected.pendingReplenishment, {
          id: "pendingReplenishment",
          header: "Pending Replenishment",
          size: 180,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.projected.pendingPicks, {
          id: "pendingPicks",
          header: "Pending Orders",
          size: 140,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.projected.allocatedOrders, {
          id: "allocatedOrders",
          header: "Allocated Orders",
          size: 150,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.projected.projectedForwardPick, {
          id: "projectedForwardPick",
          header: "Forward Pick",
          size: 140,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
      ],
    }),
    // Forecast group
    columnHelper.group({
      header: "Forecast",
      columns: [
        columnHelper.accessor((row) => row.forecast.averageReplenishment, {
          id: "averageReplenishment",
          header: "Average Replenishment",
          size: 180,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.forecast.averageDemand, {
          id: "averageDemand",
          header: "Average Demand",
          size: 160,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.forecast.demandTomorrow, {
          id: "demandTomorrow",
          header: "Demand Tomorrow",
          size: 160,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.forecast.knownDemand, {
          id: "knownDemand",
          header: "Known Demand",
          size: 140,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.forecast.forwardPickTomorrow, {
          id: "forwardPickTomorrow",
          header: "Forward Pick Tomorrow",
          size: 180,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.forecast.twoDayDemand, {
          id: "twoDayDemand",
          header: "Two-Day Demand",
          size: 140,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
        columnHelper.accessor((row) => row.forecast.twoDayForwardPick, {
          id: "twoDayForwardPick",
          header: "Two-Day Forward Pick",
          size: 160,
          cell: (info) => UnitUtil.formatNumericValue(info.getValue()),
        }),
      ],
    }),
  ];
  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
      handleClearSelection();
    },
    [setPagination, handleClearSelection],
  );
  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
      handleClearSelection();
    },
    [setSorting, handleClearSelection],
  );
  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => {
        // Extract just the property name if it contains a dot
        const columnName = id.includes(".") ? id.split(".").pop()! : id;
        return {
          id: columnName,
          value,
        };
      });
      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
      handleClearSelection();
    },
    [setColumnFilters, setGlobalFilter, handleClearSelection],
  );

  const handleExportComplete = useCallback(() => {
    setIsExportDialogOpen(false);
    handleClearSelection();
  }, [handleClearSelection]);

  return (
    <>
      <Datagrid
        columns={columns}
        data={data}
        mode="server"
        totalRows={rowCount}
        isLoading={isLoading || isFetching}
        error={error?.message}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onFilter={handleFilter}
        initialPagination={pagination}
        initialSorting={sorting}
        enableSelection={true}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        onClearSelection={handleClearSelection}
        exportOptions={exportOptions}
        onExportAction={handleExportAction}
        showExportButton={true}
        onRefreshClick={onRefresh}
        showRefreshButton={!!onRefresh}
      />
      {/* Render the SKU details drawer */}
      {selectedSku && (
        <InventoryForecastListDrawer
          sku={selectedSku}
          open={isDrawerOpen}
          onClose={() => {
            setIsDrawerOpen(false);
            setSelectedSku(null);
          }}
        />
      )}

      <PickListExportDialog
        open={isExportDialogOpen}
        onClose={handleExportComplete}
        selectedRows={selectedRows}
        inventoryUpdatedAt={inventoryUpdatedAt}
        forecastPerformedAt={forecastPerformedAt}
      />

      <ExportModal
        open={isExportExcelModalOpen}
        onClose={handleExportExcelModalClose}
        onExport={handleExportExcel}
        filters={columnFilters}
        columnDefs={getColumnDefinitions()}
        baseFileName="Inventory_Forecast"
      />
    </>
  );
}
