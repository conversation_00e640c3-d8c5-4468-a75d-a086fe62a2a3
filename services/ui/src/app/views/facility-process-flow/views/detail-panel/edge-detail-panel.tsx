import { FC, useMemo } from "react";
import { ictApi } from "../../../../api/ict-api";
import "./facility-process-flow-detail-panel.css";
import { MetricGroup } from "../../types/facility-process-flow-types";
import { LoadingView } from "../../../../components/loading-view/loading-view";
import styles from "../../../../components/datagrid/datagrid.module.scss";
import { Accordion, AccordionItem, IconButton, Tooltip } from "@carbon/react";
import { Close } from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { useApiErrorState } from "../../../../hooks/use-api-error-state";
import { WidgetNotification } from "../../../../components/widget-container/widget-notification";
import { formatMetricValue } from "../../utils";

interface CollapsibleMetricGroup extends MetricGroup {
  isVisible?: boolean;
}

interface EdgeDetailPanelProps {
  edgeId: string;
  view: string;
  onClearSelection?: () => void;
}

export const EdgeDetailPanel: FC<EdgeDetailPanelProps> = ({
  edgeId,
  view,
  onClearSelection,
}) => {
  const { t } = useTranslation();

  const {
    data: detailPanelData,
    isLoading,
    error,
  } = ictApi.client.useQuery(
    "get",
    "/inventory/process-flow/details/{type}/{elementId}",
    {
      params: {
        path: {
          type: "edge",
          elementId: edgeId,
        },
        query: {
          view,
        },
      },
      enabled: edgeId !== undefined,
    },
  );

  const isNoDataAvailable = useApiErrorState(error);

  interface EdgeNodes {
    inboundNode: string;
    outboundNode: string;
  }

  const parseNodesFromEdge = (metricId: string): EdgeNodes => {
    /*
      Parses the inbound and outbound node names from the edge metric id.
    */
    const metricParts = metricId.split(":");
    const outboundNodeName =
      metricParts[2].charAt(0).toUpperCase() + metricParts[2].slice(1);
    const inboundNodeName =
      metricParts[3].charAt(0).toUpperCase() + metricParts[3].slice(1);
    return {
      inboundNode: inboundNodeName,
      outboundNode: outboundNodeName,
    };
  };

  const metricGroups = useMemo(() => {
    /*
      Builds the metric group title from the metric id.
    */
    if (!detailPanelData?.metricGroups) {
      return [];
    }
    return detailPanelData.metricGroups.map(
      (mg: MetricGroup): CollapsibleMetricGroup => {
        const edgeMetric = mg.metrics[0];
        const edgeNodes = parseNodesFromEdge(edgeMetric.id);
        const edgeTitle = `${edgeNodes.outboundNode} to ${edgeNodes.inboundNode}`;
        return {
          ...mg,
          isVisible: true,
          title: edgeTitle,
        };
      },
    );
  }, [detailPanelData]);

  const closeDetailPanel = () => {
    if (onClearSelection) {
      onClearSelection();
    }
  };

  /**
   * Render error state
   */
  const renderError = () => {
    return (
      <div className={styles.errorContainer}>
        <WidgetNotification title={"Error loading data"} kind="error" />
      </div>
    );
  };

  /**
   * Render empty state
   */
  const renderNoData = () => {
    return (
      <div className={styles.emptyState}>
        <WidgetNotification title={"No data available"} kind="info" />
      </div>
    );
  };

  return (
    <div className="detail-panel">
      <div className="edge-detail-panel-header">
        <IconButton
          data-testid="close-button"
          label="Close"
          size="sm"
          onClick={closeDetailPanel}
        >
          <Close />
        </IconButton>
      </div>
      {isLoading ? (
        <LoadingView />
      ) : isNoDataAvailable ? (
        renderNoData()
      ) : error ? (
        renderError()
      ) : !detailPanelData ||
        !detailPanelData.metricGroups ||
        detailPanelData.metricGroups.length === 0 ? (
        <div className="empty-state">
          {t(
            "facilityProcessFlowDetailPanel.noMetricsAvailable",
            "No metrics available for this element",
          )}
        </div>
      ) : (
        <div className={"metrics-container"}>
          {metricGroups.map((group: CollapsibleMetricGroup, groupIndex) => (
            <Accordion key={groupIndex} className="metric-group">
              <AccordionItem
                open={true}
                key={groupIndex}
                title={
                  group.title === "default" || !group.title
                    ? "Metrics"
                    : group.title
                }
              >
                {group.metrics.map((metric, metricIndex) => (
                  <div key={metricIndex} className="metric">
                    <div className="metric-type">
                      {metric.description ? (
                        <Tooltip
                          label={metric.description}
                          autoAlign
                          enterDelayMs={0}
                          leaveDelayMs={0}
                        >
                          <span>{t("Units per Hour")}:</span>
                        </Tooltip>
                      ) : (
                        <span>{t("Units per Hour")}:</span>
                      )}
                    </div>
                    <div className="metric-value">
                      {formatMetricValue(metric.value)}
                    </div>
                  </div>
                ))}
              </AccordionItem>
            </Accordion>
          ))}
        </div>
      )}
    </div>
  );
};
