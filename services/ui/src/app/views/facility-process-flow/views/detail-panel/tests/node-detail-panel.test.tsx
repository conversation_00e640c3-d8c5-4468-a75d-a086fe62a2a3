import { vi } from "vitest";
import { render, screen } from "../../../../../../test-utils";
import { NodeDetailPanel } from "../node-detail-panel";
import { ictApi } from "../../../../../api/ict-api";
import { useApiErrorState } from "../../../../../hooks/use-api-error-state";
import { useConfigSetting } from "../../../../../config/hooks/use-config";

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    Accordion: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="accordion">{children}</div>
    ),
    AccordionItem: ({
      children,
      title,
      open,
    }: {
      children: React.ReactNode;
      title: string;
      open: boolean;
    }) => (
      <div data-testid="accordion-item" data-title={title} data-open={open}>
        {children}
      </div>
    ),
  };
});

// Mock the ICT API
vi.mock("../../../../../api/ict-api", () => {
  return {
    ictApi: {
      client: {
        useQuery: vi.fn(),
      },
    },
  };
});

// Mock the useApiErrorState hook
vi.mock("../../../../../hooks/use-api-error-state", () => ({
  useApiErrorState: vi.fn(),
}));

// Mock the useConfigSetting hook
vi.mock("../../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(),
}));

// Mock the translation function
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    t: (key: string, fallback: string) => fallback,
  }),
  initReactI18next: {
    type: "3rdParty",
    init: () => {},
  },
  I18nextProvider: ({ children }: { children: React.ReactNode }) => children,
}));

describe("NodeDetailPanel", () => {
  const onClearSelectionMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup the mocks for each test
    const mockUseQuery = ictApi.client.useQuery as any;
    mockUseQuery.mockReturnValue({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "foo:bar:node:throughput",
                value: 120,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    (useApiErrorState as any).mockReturnValue(false);

    // Mock the config setting
    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: {
          "Test Node": [
            {
              name: "Performance",
              metrics: ["throughput"],
            },
          ],
        },
      },
      isLoading: false,
    });
  });

  it("renders loading state correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByTestId("loading-view")).toBeInTheDocument();
  });

  it("renders error state correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: undefined,
      isLoading: false,
      error: new Error("Failed to fetch data"),
    });

    (useApiErrorState as any).mockReturnValueOnce(true);

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("No data available")).toBeInTheDocument();
  });

  it("renders empty state when no metrics are available", () => {
    // Override the mock for this test
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: { metricGroups: [] },
      isLoading: false,
      error: null,
    });

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(
      screen.getByText("No metrics available for this element"),
    ).toBeInTheDocument();
  });

  it("renders metrics correctly with config", () => {
    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    // Check that the node title is rendered correctly
    expect(screen.getByText("Test Node")).toBeInTheDocument();

    // Check that metrics are rendered
    expect(screen.getByTestId("accordion")).toBeInTheDocument();
    expect(screen.getByText("throughput:")).toBeInTheDocument();
    expect(screen.getByText("120")).toBeInTheDocument();
  });

  it("renders metrics correctly without config", () => {
    // Mock no config setting
    (useConfigSetting as any).mockReturnValueOnce({
      setting: null,
      isLoading: false,
    });

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    // Check that the node title is rendered correctly
    expect(screen.getByText("Test Node")).toBeInTheDocument();

    // Check that metrics are rendered
    expect(screen.getByTestId("accordion")).toBeInTheDocument();
    expect(screen.getByText("throughput:")).toBeInTheDocument();
    expect(screen.getByText("120")).toBeInTheDocument();
  });

  it("formats metric values correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "foo:bar:node:throughput",
                value: 120.5,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("120.5")).toBeInTheDocument();
  });

  it("handles null metric values correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "foo:bar:node:throughput",
                value: null,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("N/A")).toBeInTheDocument();
  });

  it("calls onClearSelection when close button is clicked", () => {
    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    const closeButton = screen.getByTestId("close-button");
    closeButton.click();

    expect(onClearSelectionMock).toHaveBeenCalledTimes(1);
  });

  it("handles workstation naming pattern correctly", () => {
    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="MGTP-01"
        onClearSelection={onClearSelectionMock}
      />,
    );

    // The component should recognize this as a workstation and use the workstation config
    expect(screen.getByText("MGTP-01")).toBeInTheDocument();

    // Verify that useConfigSetting was called with the correct lookup
    expect(useConfigSetting).toHaveBeenCalledWith(
      "ict-facility-process-flow-detail-panel-layout",
    );
  });

  it("processes metrics correctly with config groups", () => {
    // Mock a more complex config setting
    (useConfigSetting as any).mockReturnValueOnce({
      setting: {
        value: {
          "Test Node": [
            {
              name: "Performance",
              metrics: ["throughput"],
            },
            {
              name: "Status",
              metrics: ["state"],
            },
          ],
        },
      },
      isLoading: false,
    });

    // Mock more complex metrics data
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "foo:bar:node:throughput",
                value: 120,
                units: "units/hr",
              },
              {
                id: "foo:bar:node:state",
                value: "Running",
                units: "",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    render(
      <NodeDetailPanel
        nodeId="node-1"
        elementTitle="Test Node"
        onClearSelection={onClearSelectionMock}
      />,
    );

    // Check that both metric groups are rendered
    const accordionItems = screen.getAllByTestId("accordion-item");
    expect(accordionItems.length).toBe(2);

    // Check that metrics are in the correct groups
    expect(screen.getByText("throughput:")).toBeInTheDocument();
    expect(screen.getByText("state:")).toBeInTheDocument();
  });
});
