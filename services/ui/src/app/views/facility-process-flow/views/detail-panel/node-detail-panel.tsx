import { FC, useMemo } from "react";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import { ictApi } from "../../../../api/ict-api";
import "./facility-process-flow-detail-panel.css";
import { Metric, MetricGroup } from "../../types/facility-process-flow-types";
import { LoadingView } from "../../../../components/loading-view/loading-view";
import { Accordion, AccordionItem, IconButton } from "@carbon/react";
import { resolveMetricLabel } from "../../locale/process-flow-locale-util";
import { Close } from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { useApiErrorState } from "../../../../hooks/use-api-error-state";
import { WidgetNotification } from "../../../../components/widget-container/widget-notification";
import styles from "../../../../components/datagrid/datagrid.module.scss";
import { formatMetricValue } from "../../utils";

interface CollapsibleMetricGroup extends MetricGroup {
  isVisible?: boolean;
}

interface MetricGroupConfig {
  name: string;
  metrics: string[];
}

interface DetailPanelConfig {
  [key: string]: MetricGroupConfig[];
}

interface DetailPanelProps {
  nodeId: string;
  elementTitle: string;
  onClearSelection?: () => void;
}

export const NodeDetailPanel: FC<DetailPanelProps> = ({
  nodeId,
  elementTitle,
  onClearSelection,
}) => {
  const { t } = useTranslation();
  const { setting: detailPanelConfigSetting } = useConfigSetting(
    "ict-facility-process-flow-detail-panel-layout",
  );

  const {
    data: detailPanelData,
    isLoading,
    error,
  } = ictApi.client.useQuery(
    "get",
    "/inventory/process-flow/details/{type}/{elementId}",
    {
      params: {
        path: {
          type: "node",
          elementId: nodeId,
        },
      },
      enabled: nodeId !== undefined,
    },
  );

  const isNoDataAvailable = useApiErrorState(error);

  const detailPanelConfig = useMemo(() => {
    // Use the element title to retrieve the correct value from the config.
    // If on app config setting exists then fall back to showing all metrics in one default group with no title.
    // Dynamic element titles (aisles, workstations, etc.) must be handled manually.
    // These are temporary workarounds and should be removed once the graph configuration is completed.
    let configLookup = elementTitle;
    if (elementTitle.match("^M.*GTP-[0-9]{2}$")) {
      configLookup = "Workstation";
    } else if (elementTitle.match("^M.*GTP-[0-9]{2}D1$")) {
      configLookup = "SourceLocation";
    } else if (elementTitle.match("^M.*GTP-[0-9]{2}(B[1-5]|F[1-6])$")) {
      configLookup = "DestinationLocation";
    }

    if (
      detailPanelConfigSetting &&
      typeof detailPanelConfigSetting.value === "object" &&
      detailPanelConfigSetting.value &&
      (detailPanelConfigSetting.value as DetailPanelConfig)[configLookup]
    ) {
      // Check if there is a config setting for this node and return it if it exists
      return (detailPanelConfigSetting.value as DetailPanelConfig)[
        configLookup
      ];
    }
    return [];
  }, [detailPanelConfigSetting, elementTitle]);

  const metricGroups = useMemo(() => {
    if (!detailPanelData?.metricGroups) {
      return [];
    }

    const serverMetricGroups = detailPanelData.metricGroups;

    if (detailPanelConfig.length > 0) {
      return processMetricsForConfig(serverMetricGroups, detailPanelConfig);
    } else {
      return processMetricsDefault(serverMetricGroups);
    }
  }, [detailPanelData, detailPanelConfig]);

  const closeDetailPanel = () => {
    if (onClearSelection) {
      onClearSelection();
    }
  };

  const renderError = () => {
    return (
      <div className={styles.errorContainer}>
        <WidgetNotification title={"Error loading data"} kind="error" />
      </div>
    );
  };

  const renderNoData = () => {
    return (
      <div className={styles.emptyState}>
        <WidgetNotification title={"No data available"} kind="info" />
      </div>
    );
  };

  return (
    <div className="detail-panel">
      <div className="node-detail-panel-header">
        <h4>{elementTitle}</h4>
        <IconButton
          data-testid="close-button"
          label="Close"
          size="sm"
          onClick={closeDetailPanel}
        >
          <Close />
        </IconButton>
      </div>
      {isLoading ? (
        <LoadingView />
      ) : isNoDataAvailable ? (
        renderNoData()
      ) : error ? (
        renderError()
      ) : !detailPanelData ||
        !detailPanelData.metricGroups ||
        detailPanelData.metricGroups.length === 0 ? (
        <div className="empty-state">
          {t(
            "facilityProcessFlowDetailPanel.noMetricsAvailable",
            "No metrics available for this element",
          )}
        </div>
      ) : (
        <div className={"metrics-container"}>
          {metricGroups.map((group, groupIndex) => (
            <Accordion key={groupIndex} className="metric-group">
              <AccordionItem
                open={true}
                key={groupIndex}
                title={group.title === "default" ? "" : group.title}
              >
                {group.metrics.map((metric, metricIndex) => (
                  <div key={metricIndex} className="metric">
                    <div className="metric-type">
                      {resolveMetricLabel(metric.id)}:
                    </div>
                    <div className="metric-value">
                      {formatMetricValue(metric.value)}
                    </div>
                    <div className="metric-units">{metric.units}</div>
                  </div>
                ))}
              </AccordionItem>
            </Accordion>
          ))}
        </div>
      )}
    </div>
  );
};

function processMetricsDefault(
  metricGroups: MetricGroup[],
): CollapsibleMetricGroup[] {
  // Default processing for metrics
  return metricGroups.map((mg: MetricGroup) => {
    return {
      ...mg,
      isVisible: true,
    };
  });
}

function processMetricsForConfig(
  serverMetricGroups: MetricGroup[],
  detailPanelConfig: MetricGroupConfig[],
): CollapsibleMetricGroup[] {
  // Combines the order and structure of the layout config setting with the payload of metrics from API
  const orderedMetricGroups: CollapsibleMetricGroup[] = [];

  // Reduce metric payload to a flat list of Metrics
  let allServerMetrics: Metric[] = [];
  serverMetricGroups.forEach((group: MetricGroup) => {
    allServerMetrics = allServerMetrics.concat(group.metrics);
  });

  // Loop through the Detail Panel Config and build a layout of metrics based on the config's structure
  detailPanelConfig.forEach((groupConfig: MetricGroupConfig) => {
    const metricGroup: CollapsibleMetricGroup = {
      title: groupConfig.name,
      metrics: [],
      isVisible: true,
    };

    groupConfig.metrics.forEach((configMetricRegex: string) => {
      // Use the regex from the config setting to find matching metric names in the API payload
      const metricMatches = allServerMetrics.filter((metric) =>
        new RegExp(configMetricRegex).test(metric.id),
      );

      // Sort metrics alphabetically if multiple matches are found
      metricMatches.sort((a, b) => (a.id > b.id ? 1 : b.id > a.id ? -1 : 0));

      // Add all matching metrics into the array of metrics in same order as in groupConfig
      metricGroup.metrics.push(...metricMatches);
    });

    // Add the group to the array of ordered metric groups (if not empty)
    if (metricGroup.metrics.length > 0) {
      orderedMetricGroups.push(metricGroup);
    }
  });

  return orderedMetricGroups;
}
