.detail-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 25px 35px;
  overflow: hidden;
  position: relative;
  height: 100%;
  text-transform: capitalize;
}

.edge-detail-panel-header {
  display: flex;
  justify-content: end;
}

.node-detail-panel-header {
  display: flex;
  justify-content: space-between;
}

.metrics-container {
  display: grid;
  gap: 16px;
  overflow: auto;
  padding-right: 10px;
}

.metric-group {
  border: 1px solid var(--cds-border-strong);
}

.metric {
  display: flex;
  padding: 8px 0;
}

.metric-type {
  flex: auto;
  text-transform: none;
  cursor: default;
}

.metric-units {
  margin-left: 4px;
  text-transform: none;
}
