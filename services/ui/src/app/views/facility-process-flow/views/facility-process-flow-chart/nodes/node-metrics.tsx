import { Tooltip } from "@carbon/react";
import { Metric } from "../../../types/facility-process-flow-types";
import { resolveMetricLabel } from "../../../locale/process-flow-locale-util";
import { formatMetricValue } from "../../../utils";

interface NodeMetricProps {
  metrics: Metric[];
}

const NodeMetrics = (props: NodeMetricProps) => {
  const { metrics } = props;

  function renderMetric(metric: Metric) {
    const label = resolveMetricLabel(metric.id);
    const value = metric.value;

    return (
      <div key={metric.id} className="node-metric">
        <div className={`value ${metric.type ?? ""}`}>
          {`${formatMetricValue(value)}`}
          <span className="units">{metric.units}</span>
        </div>
        <div className="label">
          {metric.description ? (
            <Tooltip
              label={metric.description}
              autoAlign
              enterDelayMs={0}
              leaveDelayMs={0}
            >
              <span>{label}</span>
            </Tooltip>
          ) : (
            <span>{label}</span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="node-details">
      {metrics.map((metric: Metric) => renderMetric(metric))}
    </div>
  );
};

export default NodeMetrics;
