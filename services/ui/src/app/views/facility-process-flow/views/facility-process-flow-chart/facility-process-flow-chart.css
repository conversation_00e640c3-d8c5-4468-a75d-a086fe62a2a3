.facility-process-flow-react-root {
  height: 100%;
}

.react-flow__node {
  &.react-flow__node-ict-node {
    background-color: var(--cds-chat-bubble-agent);
    border-color: var(--cds-border-strong);
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    border-radius: 8px;
    box-shadow: 0px 4px 4px 0px var(--cds-shadow);
    font-size: 14px;
    min-height: 62px;
    min-width: 298px;
    overflow: visible;
    text-transform: capitalize;

    text-align: left;

    &.selected {
      border-color: var(--Brand-70);
    }

    .ict-node-content {
      padding: 20px;

      &:hover {
        cursor: pointer;
      }
    }
    .node-header {
      display: flex;
      justify-content: space-between;

      .node-title {
        display: flex;
        flex-grow: 1;

        .node-title-text {
          max-width: 120px;
          margin-right: 8px;
          text-decoration: underline;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .node-alerts {
          background-color: var(--cds-background);
          border-radius: 16px;
          display: inline-flex;
          font-size: 10px;
          margin-right: 10px;
          margin-left: 10px;
          max-height: 40px;
          padding: 4px 8px;

          .node-alert {
            display: flex;
            flex: auto;

            .node-alert-circle {
              border-radius: 5px;
              height: 10px;
              margin-right: 2px;
              width: 10px;

              &.red {
                background-color: var(--cds-support-error);
              }
              &.yellow {
                background-color: var(--cds-support-caution-minor);
              }
              &.blue {
                background-color: var(--cds-support-info);
              }
            }

            .node-alert-label {
              color: var(--cds-text-primary);

              &:not(.last) {
                margin-right: 8px;
              }
            }
          }
        }
      }

      .node-button {
        display: flex;
        text-align: right;

        .arrow-button {
          display: flex;
          align-items: center;
          justify-content: center;
          background: none;
          color: var(--cds-text-secondary);
          border: none;
          font-size: 14px;
          font-family: "Noto Sans", sans-serif;
        }

        .arrow-button:hover {
          color: var(--cds-chat-button-text-hover);
          cursor: pointer;
          text-decoration: underline;
        }
      }
    }
    .node-details {
      display: flex;
      margin-top: 12px;

      & > div {
        flex: 1;
      }

      .node-metric {
        font-size: 12px;
        text-align: left;

        &:not(:last-of-type) {
          margin-right: 16px;
        }
        & .label {
          margin-top: 0px;
          cursor: default;
        }
        & .units {
          font-size: 14px;
          margin-left: 4px;
          text-transform: none;
        }
        & .value {
          font-size: 18px;
          line-height: 24px;

          &.bad {
            color: var(--cds-text-error);
          }
        }
      }
    }
  }

  /****** Custom Styling for Aisle Nodes *****/
  &.react-flow__node-ict-aisle-node {
    border-color: var(--cds-border-strong);
    border-style: dashed;
    border-width: 2px;
    cursor: default;
    padding: 40px;
    padding-bottom: 60px;
    min-height: 715px;

    .aisle-level {
      border-color: var(--cds-border-strong);
      border-radius: 3px;
      border-style: solid;
      border-width: 1px;
      border-radius: 8px;
      box-shadow: 0px 4px 4px 0px var(--cds-shadow);
      font-size: 14px;
      min-height: 62px;
      min-width: 298px;
      overflow: hidden;
      padding: 20px;
      text-align: left;

      &.selected {
        border-color: var(--Brand-70);
      }

      a {
        cursor: pointer;
        text-decoration: underline;
      }

      &:not(:last-child) {
        margin-bottom: 16px;
      }

      .shuttle {
        cursor: pointer;
        font-size: 10px;
        margin-top: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .arrow-button {
          align-items: center;
          background: none;
          border: none;
          color: var(--cds-text-secondary);
          cursor: pointer;
          display: flex;
          font-size: 10px;
          font-family: "Noto Sans", sans-serif;
        }
      }
    }
  }
}

.react-flow__edge.animated path {
  stroke: var(--cds-background-inverse) !important;
  stroke-width: 0px;
}

.react-flow__edge.selected path {
  stroke: var(--cds-support-info) !important;
}

.customEdgeLabelContainer {
  position: absolute;
  pointer-events: all;

  .edgeLabel {
    background: var(--cds-background);
    border-radius: 12px;
    display: block;
    font-size: 12px;
    font-weight: 400;
    padding: 4px 8px;

    &.bad {
      color: var(--cds-support-error);
    }

    .edgeLabelDirection {
      margin-right: 8px;
      margin-top: 2px;

      &.bad {
        color: var(--cds-support-error);
      }
    }

    .edgeLabelBadge {
      color: var(--cds-text-inverse);
      font-size: 12px;
      border-radius: 12px;
      background: var(--cds-background-inverse);
      margin-left: 8px;
      padding-left: 8px;
      padding-right: 8px;
    }
  }
}

.react-flow__controls.react-flow__panel {
  bottom: 16px;
  box-shadow: none;
  margin: 0 0 16px 16px;

  .customControlsContainer {
    width: 28px;

    .zoomDisplay {
      margin-top: 8px;
      font-size: 14px;
      color: var(--cds-text-primary);
      white-space: nowrap;
    }
  }
  .react-flow__controls-button {
    border: 1px solid var(--cds-border-subtle);
    box-shadow: 0 0 2px 1px var(--cds-shadow);
    background-color: var(--cds-background);
    fill: var(--cds-icon-primary);
  }
  .interactiveModeToggle {
    margin-bottom: 8px;
    margin-top: 8px;
  }
}

.adminControlsContainer {
  cursor: pointer;
  display: flex;
  float: right;
  font-size: 14px;
  gap: 8px;
  height: 20px;
  margin-top: -22px;

  .lastProcessedTime {
    color: var(--cds-text-secondary);
    float: right;
    font-size: 14px;
  }

  .adminMenu {
    margin-top: -16px;
  }

  .adminControlsModal {
    h3 {
      margin-bottom: 48px;
    }
  }

  .configManagementModal {
    h3 {
      margin-bottom: 48px;
    }
    .configManagementMainPanel {
      min-height: calc(100vh - 280px);
      width: 100%;

      .gridButton {
        padding: 4px 8px;
      }
    }
  }

  .adminControlsModalMessage {
    margin-top: 20px;
  }
}
