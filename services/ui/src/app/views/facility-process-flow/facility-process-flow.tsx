import { FacilityProcessFlowOptions } from "./facility-process-flow-options";
import "./facility-process-flow.css";
import { AppConfigSetting } from "../../config/menu/types";
import { BaseViewProps } from "../view-registry.types";
import { useViewOptions } from "../../hooks/use-view-options";
import { ViewBar } from "../../components/view-bar/view-bar";
import { useRoles } from "../../auth/hooks/use-roles";
import { useConfigSetting } from "../../config/hooks/use-config";
import { useEffect, useState } from "react";
import { NodeDrilldownEvent } from "./events/node-drilldown-event";
import { ProcessFlowBreadcrumbs } from "./views/facility-process-flow-chart/components/process-flow-breadcrumbs";
import { resolveNodeBreadcrumbName } from "./locale/process-flow-locale-util";
import { FacilityProcessFlowBreadcrumb } from "./types/facility-process-flow-breadcrumb-type";
import { DmsLevelView } from "./views/dms-level-view/dms-level-view";
import { NodeDetailPanel } from "./views/detail-panel/node-detail-panel";
import { NodeSelectEvent } from "./events/node-select-event";
import { SelectedChartObject } from "./types/facility-process-flow-types";
import FacilityProcessFlowChart from "./views/facility-process-flow-chart/facility-process-flow-chart";
import { AdminControls } from "./views/admin/admin-controls";
import { ReactFlowProvider } from "reactflow";
import { EdgeDetailPanel } from "./views/detail-panel/edge-detail-panel";

type FacilityProcessFlowProps = BaseViewProps & {
  id: string;
  setting: AppConfigSetting;
};

// it would be ideal to actually use the menu.json link, or have the menu json use some constant.
export const FacilityProcessFlowViewName = "ict-facility-process-flow";

/**
 * @param setting - The view setting
 * @returns the FacilityProcessFlowView component
 */
export function FacilityProcessFlowView({ setting }: FacilityProcessFlowProps) {
  const { hasConfiguratorAccess } = useRoles();
  const [isInteractive, setIsInteractive] = useState(false);
  const [lastProcessedTime, setLastProcessedTime] = useState<string>("");
  const [nodeBreadcrumbs, setNodeBreadcrumbs] = useState<
    FacilityProcessFlowBreadcrumb[]
  >([]);
  const [selectedChartObject, setSelectedChartObject] = useState<
    SelectedChartObject | undefined
  >();
  const [parentNode, setParentNode] = useState<
    FacilityProcessFlowBreadcrumb | undefined
  >();
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);

  const { setting: pollingInterval } = useConfigSetting(
    "ict-facility-process-flow-polling-interval",
  );

  /**
   * The "useViewOptions" hook, which manages the view options including
   */
  const { handleShowSettings, handleSaveOptions, isDirty } =
    useViewOptions<FacilityProcessFlowOptions>({
      setting,
      defaultOptions: {
        pollingInterval: pollingInterval?.value as number, // need to update this with the config setting
      },
      optionsComponent: FacilityProcessFlowOptions,
    });

  useEffect(() => {
    window.addEventListener("nodeDrilldown", (event: Event) =>
      handleNodeDrilldown(event as NodeDrilldownEvent),
    );

    return () => {
      window.removeEventListener("nodeDrilldown", (event: Event) =>
        handleNodeDrilldown(event as NodeDrilldownEvent),
      );
    };
  }, []);

  const handleNodeDrilldown = (event: NodeDrilldownEvent) => {
    const newParentNode: FacilityProcessFlowBreadcrumb = {
      id: event.detail.nodeId,
      label: resolveNodeBreadcrumbName(event.detail.nodeId),
      nodeType: event.detail.nodeType,
    };
    setParentNode(newParentNode);
    // if the breadcrumb already exists, don't add it again
    if (
      nodeBreadcrumbs.find((breadcrumb) => breadcrumb.id === newParentNode.id)
    ) {
      return;
    }
    // Update breadcrumbs when drilling down
    setNodeBreadcrumbs((prev) => [...prev, newParentNode]);
  };

  // Handle a node selection event named onNodeSelect
  const handleNodeSelect = (nodeId: string, nodeLabel: string) => {
    // TODO check if the nodeId is already selected in the detail panel
    setSelectedChartObject({ id: nodeId, label: nodeLabel, type: "node" });
    setIsDetailPanelOpen(true);
    const event = new CustomEvent(NodeSelectEvent.EVENT_NAME, {
      bubbles: true,
      composed: true,
    });
    window.dispatchEvent(event);
  };

  // Handle an edge selection event named onEdgeSelect
  const handleEdgeSelect = (
    edgeId: string,
    edgeLabel: string,
    isBiDirectional: boolean,
  ) => {
    console.log("edge selected", edgeLabel, isBiDirectional);
    setSelectedChartObject({ id: edgeId, label: edgeLabel, type: "edge" });
    setIsDetailPanelOpen(true);
  };

  // handle a clear selection event named onClearSelection
  const handleClearSelection = () => {
    setSelectedChartObject(undefined);
    setIsDetailPanelOpen(false);
    const event = new CustomEvent("selectionCleared", {
      bubbles: true,
      composed: true,
    });
    window.dispatchEvent(event);
  };

  const handleBreadcrumbClick = (crumbNodeId?: string) => {
    if (!crumbNodeId) {
      setNodeBreadcrumbs([]);
      setParentNode(undefined);
      return;
    }

    const existingBreadcrumbIndex = nodeBreadcrumbs.findIndex(
      (breadcrumb) => breadcrumb.id === crumbNodeId,
    );
    const breadcrumbClicked =
      existingBreadcrumbIndex > -1
        ? nodeBreadcrumbs[existingBreadcrumbIndex]
        : undefined;
    if (existingBreadcrumbIndex > -1) {
      setNodeBreadcrumbs((prev) => prev.slice(0, existingBreadcrumbIndex + 1));
    }
    setParentNode(breadcrumbClicked);
    setIsDetailPanelOpen(false);
  };

  const handleCloseDetailPanel = () => {
    setIsDetailPanelOpen(false);
    setSelectedChartObject(undefined);
  };

  return (
    <ReactFlowProvider>
      <div data-testid="facility-process-flow" className={"container"}>
        <ViewBar
          title="Facility Process Flow"
          showDatePeriodRange={false}
          showSettings={false}
          hasConfiguratorAccess={hasConfiguratorAccess}
          onSettingsClick={handleShowSettings}
          saveEnabled={isDirty}
          showSave={true}
          onSaveClick={handleSaveOptions}
        />

        <div
          data-testid="process-flow-chart-container"
          className={"contentWrapper"}
        >
          <div className={"mainPanel"}>
            <ProcessFlowBreadcrumbs
              nodeBreadcrumbs={nodeBreadcrumbs}
              onCloseDetailPanel={handleCloseDetailPanel}
              onBreadcrumbClick={handleBreadcrumbClick}
            />
            {hasConfiguratorAccess && (
              <AdminControls
                areaId={parentNode?.id ?? ""}
                isInteractive={isInteractive}
                lastProcessedTime={lastProcessedTime}
              />
            )}
            {parentNode?.nodeType === "level" ? (
              <DmsLevelView dmsAisleLevelId={parentNode?.id} />
            ) : (
              <FacilityProcessFlowChart
                data-testid="process-flow-chart"
                isInteractive={isInteractive}
                parentNodeId={parentNode?.id}
                onNodeSelect={handleNodeSelect}
                onEdgeSelect={handleEdgeSelect}
                onClearSelection={handleClearSelection}
                setIsInteractive={setIsInteractive}
                setLastProcessedTime={setLastProcessedTime}
              />
            )}
          </div>
          <div
            data-testid="react-flow-detail-panel"
            className={`details ${isDetailPanelOpen ? "" : "hidden"}`}
          >
            {selectedChartObject && selectedChartObject.type === "node" && (
              <NodeDetailPanel
                nodeId={selectedChartObject.id}
                elementTitle={selectedChartObject.label}
                onClearSelection={handleClearSelection}
              />
            )}
            {selectedChartObject && selectedChartObject.type === "edge" && (
              <EdgeDetailPanel
                edgeId={selectedChartObject.id}
                onClearSelection={handleClearSelection}
              />
            )}
          </div>
        </div>
      </div>
    </ReactFlowProvider>
  );
}

export default FacilityProcessFlowView;
