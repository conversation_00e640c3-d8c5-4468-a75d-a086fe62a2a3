import { <PERSON>ly<PERSON>, Timer } from "@carbon/icons-react";
import {
  ComboBox,
  TextInput,
  Toggle,
  FilterableMultiSelect,
  NumberInput,
} from "@carbon/react";
import { OptionsAccordionGroup } from "../../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../../components/options/options-accordion/options-accordion";
import { OptionsContainer } from "../../../components/options/options-container/options-container";
import { DatePeriod, DatePeriodOption } from "../../../types";
import type { BaseViewOptionsProps } from "../../view-registry.types";
import type { DashboardOptions } from "../dashboard";
import { TFunction } from "i18next";
import { useTranslation } from "react-i18next";

interface DashboardSettingsProps extends BaseViewOptionsProps {
  options: DashboardOptions;
}

// Date period options for the ComboBox
export const datePeriodOptions: (t: TFunction) => DatePeriodOption[] = (t) => [
  { id: DatePeriod.today, text: "Today", label: t("Today", "Today") },
  {
    id: DatePeriod.yesterday,
    text: "Yesterday",
    label: t("Yesterday", "Yesterday"),
  },
  {
    id: DatePeriod.thisWeek,
    text: "This Week",
    label: t("This Week", "This Week"),
  },
  {
    id: DatePeriod.lastWeek,
    text: "Last Week",
    label: t("Last Week", "Last Week"),
  },
  {
    id: DatePeriod.thisMonth,
    text: "This Month",
    label: t("This Month", "This Month"),
  },
  {
    id: DatePeriod.lastMonth,
    text: "Last Month",
    label: t("Last Month", "Last Month"),
  },
  {
    id: DatePeriod.last7days,
    text: "Last 7 Days",
    label: t("Last 7 Days", "Last 7 Days"),
  },
  {
    id: DatePeriod.last14days,
    text: "Last 14 Days",
    label: t("Last 14 Days", "Last 14 Days"),
  },
  {
    id: DatePeriod.last30days,
    text: "Last 30 Days",
    label: t("Last 30 Days", "Last 30 Days"),
  },
];

export const DashboardSettings = ({
  options,
  onChange,
  onClose,
  onSave,
}: DashboardSettingsProps) => {
  const { t } = useTranslation();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDefaultDateRangeChange = (data: any) => {
    if (data.selectedItem) {
      const newOptions = {
        ...options,
        defaultDatePeriodRange: data.selectedItem.id,
      };
      onChange(newOptions);
    }
  };

  const handleShowDateRangeChange = (checked: boolean) => {
    const newOptions = {
      ...options,
      showDateRange: checked,
    };
    onChange(newOptions);
  };

  const handleAvailableDateRangesChange = ({
    selectedItems,
  }: {
    selectedItems: DatePeriodOption[];
  }) => {
    const selectedRanges = selectedItems.map((item) => item.id);

    const isCurrentDefaultInNewRanges =
      selectedRanges.length === 0 ||
      (typeof options.defaultDatePeriodRange === "string" &&
        selectedRanges.includes(options.defaultDatePeriodRange as DatePeriod));

    const updatedDefaultRange = isCurrentDefaultInNewRanges
      ? options.defaultDatePeriodRange
      : selectedRanges[0];

    const newOptions = {
      ...options,
      availableDateRanges: selectedRanges,
      defaultDatePeriodRange: updatedDefaultRange,
    };
    onChange(newOptions);
  };

  const selectedAvailableDateRanges = options.availableDateRanges
    ? datePeriodOptions(t).filter((option) =>
        options.availableDateRanges!.includes(option.id),
      )
    : [];

  const defaultDateRangeOptions =
    options.availableDateRanges && options.availableDateRanges.length > 0
      ? datePeriodOptions(t).filter((option) =>
          options.availableDateRanges!.includes(option.id),
        )
      : datePeriodOptions(t);

  const selectedDatePeriod = defaultDateRangeOptions.find(
    (option) => option.id === options.defaultDatePeriodRange,
  );

  const handleAutoRefreshToggle = (checked: boolean) => {
    const newOptions = {
      ...options,
      autoRefreshEnabled: checked,
    };
    onChange(newOptions);
  };

  const handleAutoRefreshIntervalChange = (value: number) => {
    const newOptions = {
      ...options,
      autoRefreshInterval: value,
    };
    onChange(newOptions);
  };

  const MIN_INTERVAL = 30; // 30 seconds
  const MAX_INTERVAL = 86400; // 24 hours in seconds

  return (
    <OptionsContainer onClose={onClose} onSave={onSave}>
      <OptionsAccordion>
        <OptionsAccordionGroup
          id="display"
          title="Display"
          icon={<Analytics size={20} />}
        >
          <TextInput
            id="dashboard-name"
            labelText="Dashboard Name"
            value={options.title}
            onChange={(e) => onChange({ ...options, title: e.target.value })}
          />
          <ComboBox
            id="default-date-range"
            titleText="Default Date Range"
            helperText="The default date range to use for the dashboard"
            items={defaultDateRangeOptions}
            initialSelectedItem={selectedDatePeriod}
            onChange={handleDefaultDateRangeChange}
            placeholder="Select a default date range"
          />
          <Toggle
            id="show-date-range-toggle"
            labelText="Show Date Range Picker"
            toggled={options.showDateRange}
            onToggle={handleShowDateRangeChange}
            labelA=""
            labelB=""
          />
          {options.showDateRange && (
            <FilterableMultiSelect
              id="available-date-ranges"
              titleText="Available Date Ranges"
              helperText="Select which date ranges are available in the dropdown (leave empty for all)"
              items={datePeriodOptions(t)}
              initialSelectedItems={selectedAvailableDateRanges}
              onChange={handleAvailableDateRangesChange}
              placeholder="Select available date ranges"
              itemToString={(item) => (item ? item.label : "")}
            />
          )}
        </OptionsAccordionGroup>

        <OptionsAccordionGroup
          id="auto-refresh"
          title="Auto Refresh"
          icon={<Timer size={20} />}
        >
          <Toggle
            id="auto-refresh-toggle"
            labelText="Enable Auto Refresh"
            toggled={options.autoRefreshEnabled || false}
            onToggle={handleAutoRefreshToggle}
            labelA=""
            labelB=""
          />
          {options.autoRefreshEnabled && (
            <NumberInput
              id="auto-refresh-interval"
              label="Refresh Interval (seconds)"
              helperText={`Min: ${MIN_INTERVAL}s, Max: ${MAX_INTERVAL}s (24 hours)`}
              value={options.autoRefreshInterval || 60}
              min={MIN_INTERVAL}
              max={MAX_INTERVAL}
              step={1}
              onChange={(_e, { value }) => {
                const numValue = parseInt(value.toString(), 10);
                if (!isNaN(numValue)) {
                  handleAutoRefreshIntervalChange(numValue);
                }
              }}
            />
          )}
        </OptionsAccordionGroup>
      </OptionsAccordion>
    </OptionsContainer>
  );
};
