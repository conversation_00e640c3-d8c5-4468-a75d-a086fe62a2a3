// eslint-disable-next-line @typescript-eslint/no-unused-vars
const metricKpiTypes = [
  // orders..
  "orders-shipped",
  "facility-orders-shipped",
  "orders-progress",
  "facility-orders-progress",
  "orders-throughput-rate",
  "orders-cycle-time",
  "orders-units-remaining",
  "orders-lines-progress",
  "orders-outstanding",
  "orders-estimated-completion",
  "orders-facility-estimated-completion",
  "orders-projected-fulfillment",
  "orders-pick-cycle-counts",
  "orders-performance-fulfillment-rate",
  // inventory..
  "inventory-storage-utilization",
  "inventory-accuracy",
  "inventory-advices-cycle-time",
  "inventory-advices-finished",
  "inventory-advices-outstanding",
  "inventory-advices-in-progress",
  // equipment..
  "equipment-overall-outbound-rate",
  // operators..
  "operators-active",
  "orders-customer-line-throughput-rate",
  "orders-customer-cycle-time",
  "orders-customer-line-progress",
] as const;

export type MetricKpiType = (typeof metricKpiTypes)[number];
