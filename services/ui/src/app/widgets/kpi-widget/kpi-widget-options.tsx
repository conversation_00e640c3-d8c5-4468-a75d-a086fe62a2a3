import {
  Analytics,
  DataBase,
  WatsonHealthCdArchive,
} from "@carbon/icons-react";
import { ComboBox, NumberInput, TextInput, Toggle } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { metricKpiResolver } from "../../api/resolver/kpi-resolver/kpi-resolver";
import type { MetricKpiType } from "../../api/resolver/kpi-resolver/kpi-types";
import type { MetricKpiInfo } from "../../api/resolver/kpi-resolver/types";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { KpiWidgetOptions } from "./kpi-widget";

interface KpiWidgetOptionsProps extends BaseWidgetOptionsProps {
  options: KpiWidgetOptions;
}

export const KpiWidgetOptionsForm = ({
  options,
  onChange,
}: KpiWidgetOptionsProps) => {
  const [selectedMetric, setSelectedMetric] = useState<MetricKpiInfo | null>(
    null,
  );

  const { data: metricInfo } = useQuery({
    queryKey: ["metricInfo"],
    queryFn: () => metricKpiResolver.getMetricInfo(),
  });

  useEffect(() => {
    if (metricInfo) {
      const selectedMetric = metricInfo?.find(
        (info) => info.id === options.type,
      );
      setSelectedMetric(selectedMetric || null);
    }
  }, [metricInfo, options.type]);

  const handleMetricTypeChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const selectedMetric = metricInfo?.find((info) => info.id === selectedId);
    if (selectedMetric) {
      onChange({
        ...options,
        type: selectedId as MetricKpiType,
        title: selectedMetric?.label || options.title,
      });
    }
  };

  // Ensure default values are set for target options
  useEffect(() => {
    const defaultsToSet: Partial<KpiWidgetOptions> = {};
    let shouldUpdate = false;

    if (options.showTarget && options.targetDisplayStyle === undefined) {
      defaultsToSet.targetDisplayStyle = "arrow";
      shouldUpdate = true;
    }

    if (options.showTarget && options.targetDirection === undefined) {
      defaultsToSet.targetDirection = "above";
      shouldUpdate = true;
    }

    if (options.showTarget && options.showDelta === undefined) {
      defaultsToSet.showDelta = true;
      shouldUpdate = true;
    }

    if (shouldUpdate) {
      onChange({
        ...options,
        ...defaultsToSet,
      });
    }
  }, [
    options.showTarget,
    options.targetDisplayStyle,
    options.targetDirection,
    options.showDelta,
    onChange,
    options,
  ]);

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title="Display"
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText="Metric Title"
          id="metric-title"
          value={options.title}
          onChange={(e) => onChange({ ...options, title: e.target.value })}
        />
        <NumberInput
          label="Precision"
          id="precision"
          value={options.precision}
          onChange={(_e, { value }) => {
            onChange({ ...options, precision: Number(value) });
          }}
          min={0}
          max={10}
        />
        <Toggle
          labelText="Show Unit"
          id="show-unit"
          toggled={options.unit}
          onToggle={(toggled) => onChange({ ...options, unit: toggled })}
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="data"
        title="Data"
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText="Metric Type"
          id="metric-type"
          items={
            metricInfo
              ? [...metricInfo]
                  .sort((a, b) => a.label.localeCompare(b.label))
                  .map((info) => ({
                    id: info.id,
                    text: info.label,
                  }))
              : []
          }
          selectedItem={
            options.type
              ? {
                  id: options.type,
                  text:
                    metricInfo?.find((info) => info.id === options.type)
                      ?.label || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleMetricTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        {selectedMetric?.filters?.map((filter) => (
          <ComboBox
            key={filter}
            titleText="Area"
            id={`area-${filter}`}
            items={[
              { id: "shipping", text: "shipping" },
              { id: "picking", text: "picking" },
            ]}
            selectedItem={
              options.filters?.[filter]
                ? {
                    id: options.filters[filter],
                    text: options.filters[filter],
                  }
                : null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                onChange({
                  ...options,
                  filters: {
                    ...options.filters,
                    [filter]: selectedItem.id,
                  },
                });
              }
            }}
            itemToString={(item) => (item ? String(item.text) : "")}
          />
        ))}
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="target"
        title="Target"
        icon={<WatsonHealthCdArchive size="24" />}
      >
        <Toggle
          labelText="Show Target"
          id="show-target"
          toggled={options.showTarget ?? false}
          onToggle={(toggled) => {
            onChange({
              ...options,
              showTarget: toggled,
              // Initialize default values when enabling target
              ...(toggled && !options.targetDisplayStyle
                ? { targetDisplayStyle: "arrow" }
                : {}),
              ...(toggled && !options.targetDirection
                ? { targetDirection: "above" }
                : {}),
              ...(toggled && options.showDelta === undefined
                ? { showDelta: true }
                : {}),
            });
          }}
        />

        {options.showTarget && (
          <>
            <NumberInput
              label="Target Value"
              id="target-value"
              value={options.targetValue ?? 0}
              onChange={(_e, { value }) => {
                onChange({ ...options, targetValue: Number(value) });
              }}
              helperText="The target value to compare against"
            />

            <ComboBox
              titleText="Target Direction"
              id="target-direction"
              helperText="Is it better to be above or below the target?"
              items={[
                { id: "above", text: "Above target is better" },
                { id: "below", text: "Below target is better" },
              ]}
              selectedItem={
                options.targetDirection
                  ? {
                      id: options.targetDirection,
                      text:
                        options.targetDirection === "above"
                          ? "Above target is better"
                          : "Below target is better",
                    }
                  : null
              }
              onChange={({ selectedItem }) => {
                if (selectedItem) {
                  onChange({ ...options, targetDirection: selectedItem.id });
                }
              }}
              itemToString={(item) => (item ? item.text : "")}
            />

            <ComboBox
              titleText="Display Style"
              id="display-style"
              helperText="How to visualize the target comparison"
              items={[
                { id: "arrow", text: "Show arrow indicator" },
                { id: "color", text: "Colorize value only" },
              ]}
              selectedItem={
                options.targetDisplayStyle
                  ? {
                      id: options.targetDisplayStyle,
                      text:
                        options.targetDisplayStyle === "arrow"
                          ? "Show arrow indicator"
                          : "Colorize value only",
                    }
                  : null
              }
              onChange={({ selectedItem }) => {
                if (selectedItem) {
                  onChange({ ...options, targetDisplayStyle: selectedItem.id });
                }
              }}
              itemToString={(item) => (item ? item.text : "")}
            />

            <NumberInput
              label="Acceptable Threshold"
              id="acceptable-threshold"
              value={options.acceptableThreshold ?? 5}
              onChange={(_e, { value }) => {
                onChange({ ...options, acceptableThreshold: Number(value) });
              }}
              helperText="Numerical difference from target considered acceptable (no warning)"
              min={0}
            />

            <NumberInput
              label="Warning Threshold"
              id="warning-threshold"
              value={options.warningThreshold ?? 10}
              onChange={(_e, { value }) => {
                onChange({ ...options, warningThreshold: Number(value) });
              }}
              helperText="Numerical difference from target to show warning state (yellow)"
              min={0}
            />

            <Toggle
              labelText="Show Delta"
              id="show-delta"
              toggled={options.showDelta ?? true}
              onToggle={(toggled) =>
                onChange({ ...options, showDelta: toggled })
              }
            />
          </>
        )}
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default KpiWidgetOptionsForm;
