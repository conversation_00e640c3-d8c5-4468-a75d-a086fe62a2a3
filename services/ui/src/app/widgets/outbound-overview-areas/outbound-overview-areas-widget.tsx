import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tabs } from "@carbon/react";
import { useState, useEffect } from "react";
import type { MetricKpiType } from "../../api/resolver/kpi-resolver/kpi-types";
import type { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { ComboChartWidgetOptions } from "../combo-chart-widget/types";
import type { KpiWidgetOptions } from "../kpi-widget/kpi-widget";
import { Widget } from "../widget";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./outbound-overview-areas-widget.module.scss";
import type { OutboundOverviewAreasWidgetOptions } from "./types";

interface OutboundOverviewAreasWidgetProps extends BaseWidgetProps {
  options: OutboundOverviewAreasWidgetOptions;
}

/**
 * Outbound Overview Areas widget - This widget is a custom widget to accomplish the
 * "shipping" vs "area" tabs in ICT UI.
 *
 * Dashboard View does not support embedded tabs so this approach is to create a new widget
 * which has tabs, and embedded wigets.
 *
 * Ideally design is changed to utilize dashboards with an area selector at the top. This widget
 * also uses its own configuration of "areas". We really should have a global way to define
 * "filters" and allow them to be read from the API or configuration.
 */
export const OutboundOverviewAreasWidget = ({
  filters,
  options,
}: OutboundOverviewAreasWidgetProps) => {
  // Set up default areas if none provided
  const defaultAreas = [
    { key: "picking", label: "Picking Area" },
    { key: "shipping", label: "Shipping Area" },
  ];
  const areas = options.areas?.length ? options.areas : defaultAreas;

  const [selectedArea, setSelectedArea] = useState<string>(
    options.defaultArea || areas[0]?.key || "picking",
  );

  // Update selected area if areas change
  useEffect(() => {
    if (!areas.find((area) => area.key === selectedArea)) {
      setSelectedArea(areas[0]?.key || "picking");
    }
  }, [areas, selectedArea]);

  const createKpiWidgetOptions = (
    type: MetricKpiType,
    title: string,
    precision = 2,
  ): KpiWidgetOptions => ({
    type,
    precision,
    title,
    filters: { ...filters, area: selectedArea },
    unit: true,
  });

  const createChartWidgetOptions = (
    type: TimeChartType,
    title: string,
  ): ComboChartWidgetOptions => ({
    type,
    title,
    filters: { ...filters, area: selectedArea },
  });

  const kpiWidgetOptions = createKpiWidgetOptions(
    "orders-customer-line-throughput-rate",
    "Customer Line Throughput Rate",
  );

  const kpiWidgetOptions2 = createKpiWidgetOptions(
    "orders-customer-cycle-time",
    "Customer Cycle Time",
  );

  const kpiWidgetOptions3 = createKpiWidgetOptions(
    "orders-customer-line-progress",
    "Customer Line Progress",
  );

  const lineChartWidgetOptions = createChartWidgetOptions(
    "orders-customer-line-progress",
    "Customer Line Progress",
  );

  const lineChartWidgetOptions2 = createChartWidgetOptions(
    "orders-customer-line-throughput",
    "Customer Line Throughput",
  );

  const handleTabChange = (index: number) => {
    setSelectedArea(areas[index]?.key || areas[0]?.key);
  };

  if (!areas.length) {
    return <div>Please configure areas in widget options</div>;
  }

  return (
    <div className={styles.container}>
      <div
        onMouseDown={(e: React.MouseEvent<HTMLDivElement>) =>
          e.stopPropagation()
        }
        onTouchStart={(e: React.TouchEvent<HTMLDivElement>) =>
          e.stopPropagation()
        }
      >
        <Tabs onChange={(state) => handleTabChange(state.selectedIndex)}>
          <TabList aria-label="Outbound Areas">
            {areas.map((area) => (
              <Tab key={area.key}>{area.label}</Tab>
            ))}
          </TabList>
          <TabPanels>
            {areas.map((area) => (
              <TabPanel key={area.key} style={{ display: "none" }}>
                {/* Empty TabPanel to satisfy Carbon's requirements for aria attribute generation */}
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </div>
      <div className={styles.widgetGrid}>
        <div className={styles.kpiRow}>
          <Widget
            readonly
            id="orders-customer-line-throughput-rate"
            type="kpi"
            options={kpiWidgetOptions}
            filters={filters}
          />
          <Widget
            readonly
            id="orders-customer-cycle-time"
            type="kpi"
            options={kpiWidgetOptions2}
            filters={filters}
          />
          <Widget
            readonly
            id="orders-customer-line-progress"
            type="kpi"
            options={kpiWidgetOptions3}
            filters={filters}
          />
        </div>
        <div className={styles.chartRow}>
          <Widget
            readonly
            id="orders-customer-line-progress"
            type="combo-chart"
            options={lineChartWidgetOptions}
            filters={filters}
          />
          <Widget
            readonly
            id="orders-customer-line-throughput"
            type="combo-chart"
            options={lineChartWidgetOptions2}
            filters={filters}
          />
        </div>
      </div>
    </div>
  );
};

// Widgets must default export the component
export default OutboundOverviewAreasWidget;
