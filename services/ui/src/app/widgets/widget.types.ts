import type { ComponentType, LazyExoticComponent } from "react";
import type { ApiFilters } from "../api/api.types";

/**
 * The base widget props that all Widgets will use.
 */
export interface BaseWidgetProps {
  id: string;
  type: string;
  filters: WidgetFilters;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any;
}

/**
 * The base widget options props that all Widgets will use.
 */
export interface BaseWidgetOptionsProps {
  /**
   * The function to call when the options change
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange: (options: any) => void;
  /**
   * Widget-specific options object
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any;
}

/**
 * The widget definition to map a widget id to React lazily loaded components.
 */
export interface WidgetDefinition {
  id: string;
  name: string;
  description: string;
  tags: string[];
  icon: ComponentType<{ size?: number }>;
  component: LazyExoticComponent<ComponentType<BaseWidgetProps>>;
  optionsComponent?: LazyExoticComponent<ComponentType<BaseWidgetOptionsProps>>;
}

/**
 * Widget-specific filters that extend the base API filters
 */
export type WidgetFilters = ApiFilters & {
  autoRefresh?: {
    enabled: boolean;
    interval: number; // in seconds
  };
};
