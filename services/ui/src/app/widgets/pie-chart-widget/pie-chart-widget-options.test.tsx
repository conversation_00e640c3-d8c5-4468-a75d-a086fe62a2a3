import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { ChartInfo } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { DatePeriod } from "../../types/date-types";
import { PieChartWidgetOptionsForm } from "./pie-chart-widget-options";
import type { PieChartWidgetOptions } from "./types";

// Mock the chartResolver
vi.mock("../../api/resolver/time-chart-resolver/time-chart-resolver", () => ({
  chartResolver: {
    getCategoryChartInfo: vi.fn(),
  },
}));

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("PieChartWidgetOptionsForm", () => {
  const mockOnChange = vi.fn();
  const defaultOptions: Partial<PieChartWidgetOptions> = {
    title: "Test Pie Chart",
    type: "faults",
    pieType: "donut",
    showPercentage: false,
    filters: {
      groupBy: "test_group",
      datePeriodRange: DatePeriod.today,
    },
  };

  const mockChartInfo = [
    {
      id: "faults",
      title: "Test Chart",
      groupBy: ["test_group", "another_group"],
    },
    {
      id: "another_chart",
      title: "Another Chart",
      groupBy: ["option_1", "option_2"],
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(chartResolver.getCategoryChartInfo).mockResolvedValue(
      mockChartInfo as ChartInfo[],
    );
  });

  it("renders with default options", async () => {
    render(
      <PieChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Check if title input is rendered with correct value
    expect(
      await screen.findByRole("textbox", { name: "Chart Title" }),
    ).toHaveValue("Test Pie Chart");

    // Check if pie type is selected correctly
    expect(
      await screen.findByRole("combobox", { name: "Pie Type" }),
    ).toHaveValue("Donut");
    expect(
      await screen.findByRole("switch", { name: "Show Percentage" }),
    ).not.toBeChecked();

    // Wait for chart info to load
    await waitFor(() => {
      expect(chartResolver.getCategoryChartInfo).toHaveBeenCalledTimes(1);
    });
  });

  it("updates title when input changes", () => {
    render(
      <PieChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const titleInput = screen.getByLabelText("Chart Title");
    fireEvent.change(titleInput, { target: { value: "Updated Title" } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      title: "Updated Title",
    });
  });

  it("toggles show percentage option", async () => {
    render(
      <PieChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    const showPercentageToggle = await screen.findByText("Show Percentage");
    fireEvent.click(showPercentageToggle);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      showPercentage: true,
    });
  });

  it("changes pie type when selected", async () => {
    render(
      <PieChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Open the combobox
    const pieTypeCombobox = await screen.findByDisplayValue("Donut");
    fireEvent.click(pieTypeCombobox);

    // Wait for the dropdown to appear and select "Pie"
    await waitFor(() => {
      const pieOption = screen.getByText("Pie");
      fireEvent.click(pieOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      pieType: "pie",
    });
  });

  it("changes chart type when selected", async () => {
    render(
      <PieChartWidgetOptionsForm
        options={defaultOptions}
        onChange={mockOnChange}
      />,
    );

    // Wait for chart info to load
    await waitFor(() => {
      expect(chartResolver.getCategoryChartInfo).toHaveBeenCalledTimes(1);
    });

    // Find and click the chart type combobox
    const chartTypeField = await screen.findByDisplayValue("Test Chart");
    fireEvent.click(chartTypeField);

    // Select the other chart option
    await waitFor(async () => {
      const anotherChartOption = await screen.findByText("Another Chart");
      fireEvent.click(anotherChartOption);
    });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultOptions,
      type: "another_chart",
      groupBy: "",
    });
  });
});
