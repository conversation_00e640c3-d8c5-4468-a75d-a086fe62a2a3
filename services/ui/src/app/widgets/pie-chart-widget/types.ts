import type { ApiFilters } from "../../api/api.types";
import type { CategoryChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { PieType } from "../../components/pie-chart/pie-chart-component";
import type { BaseWidgetOptionsProps } from "../widget.types";

export interface PieChartWidgetOptions extends BaseWidgetOptionsProps {
  title: string;
  type: CategoryChartType;
  showPercentage?: boolean;
  showLegend?: boolean;
  pieType?: PieType;
  filters?: ApiFilters;
}
