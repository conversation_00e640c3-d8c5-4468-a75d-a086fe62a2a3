import { useQuery } from "@tanstack/react-query";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { CategoryChartData } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import { PieChartComponent } from "../../components/pie-chart/pie-chart-component";
import { WidgetContainer } from "../../components/widget-container/widget-container";
import type { BaseWidgetProps } from "../widget.types";
import styles from "./pie-chart-widget.module.css";
import type { PieChartWidgetOptions } from "./types";
import { useDates } from "../../hooks/use-dates";
interface PieChartWidgetProps extends BaseWidgetProps {
  options: PieChartWidgetOptions;
}

export const PieChartWidget = ({ options, filters }: PieChartWidgetProps) => {
  const mergedFilters = { ...options.filters, ...filters };
  const { startDate, endDate } = useDates(mergedFilters.datePeriodRange);

  const {
    data: response,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      `category-chart-${mergedFilters?.datePeriodRange}-${mergedFilters?.groupBy}`,
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      chartResolver.getCategoryChart(
        options.type,
        mergedFilters,
        startDate,
        endDate,
      ),
    enabled: !!options.type,
  });

  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }

  if (isLoading || !response) {
    return <WidgetContainer title={options.title} loading />;
  }

  if (error) {
    return <WidgetContainer title={options.title} error={error} />;
  }

  if (response?.success && !response.data) {
    return <WidgetContainer title={options.title} noData />;
  }

  if (response?.success && response.data) {
    const chartData = response.data as CategoryChartData;

    return (
      <WidgetContainer title={options.title}>
        <div className={styles.container}>
          <PieChartComponent
            chartData={chartData}
            showPercentage={options.showPercentage}
            showLegend={options.showLegend}
            pieType={options.pieType || "donut"}
          />
        </div>
      </WidgetContainer>
    );
  }

  return null;
};

export default PieChartWidget;
