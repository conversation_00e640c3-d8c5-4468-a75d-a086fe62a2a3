import type { ApiFilters } from "../../api/api.types";
import type { TimeChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { ChartStyle } from "../../components/combo-chart";

export interface ComboChartWidgetOptions {
  /**
   * Title for the chart
   */
  title?: string;
  /**
   * The type of chart data to fetch from the time-chart-resolver
   */
  type?: TimeChartType;
  /**
   * The style to display the data
   */
  chartStyle?: ChartStyle;
  /**
   * Filters to apply to the chart data
   */
  filters?: ApiFilters;
  colorId?: string;
  chartColor?: string;
  showAverageLine?: boolean;
  showTargetLine?: boolean;
  showLegend?: boolean;
  targetValue?: number;
  dateFormat?: Intl.DateTimeFormatOptions;
}
