import { Heading, Section, Stack } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { WidgetFilters } from "../../widget.types";
import styles from "./total-stations-widget.module.css";
import { useTranslation } from "react-i18next";
import { useDates } from "../../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
export interface TotalStationsWidgetProps {
  filters: WidgetFilters;
}

export const TotalStationsWidget = ({ filters }: TotalStationsWidgetProps) => {
  const { t } = useTranslation();
  const { startDate, endDate } = useDates(filters.datePeriodRange);

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/summary",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("totalStationsWidgetProps.title", "Total Stations")}
        loading
      />
    );

  if (error)
    return (
      <WidgetContainer
        title={t("totalStationsWidgetProps.title", "Total Stations")}
        noData={true}
      />
    );

  const activeNumber = data?.status.find((s) => s.type === "Active")?.count;
  const inactiveNumber = data?.status.find((s) => s.type === "Inactive")?.count;

  return (
    <WidgetContainer
      title={t("totalStationsWidgetProps.title", "Total Stations")}
    >
      <Stack gap={2} style={{ width: "100%", marginTop: "-10px" }}>
        <Section level={2}>
          <Heading data-testid="total-workstations">
            {data?.totalWorkstations}
          </Heading>
        </Section>
        <Section level={4}>
          <Heading>
            {t("totalStationsWidgetProps.statusHeading", "Status")}
          </Heading>
        </Section>
        <div className={styles.status}>
          <div data-testid="active-workstation-count">
            {t("totalStationsWidgetProps.activeCount", "Active: {{number}}", {
              number: activeNumber,
            })}
          </div>
          <div data-testid="inactive-workstation-count">
            {t(
              "totalStationsWidgetProps.inactiveCount",
              "Inactive: {{number}}",
              {
                number: inactiveNumber,
              },
            )}
          </div>
        </div>
        <Section level={4}>
          <Heading>{t("totalStationsWidgetProps.modeHeading", "Mode")}</Heading>
        </Section>
        <div className={styles.status}>
          {data?.workstationMode.map((m) => (
            <div data-testid="workstation-mode" key={m.type}>
              {m.type}: {m.count}
            </div>
          ))}
        </div>
      </Stack>
    </WidgetContainer>
  );
};

export default TotalStationsWidget;
