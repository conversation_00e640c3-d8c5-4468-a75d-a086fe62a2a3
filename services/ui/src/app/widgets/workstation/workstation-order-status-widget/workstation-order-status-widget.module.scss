@use "@carbon/styles/scss/theme";
@use "@carbon/colors";

.warningIconFill {
  fill: theme.$support-warning;
}

.checkmarkIconFill {
  fill: theme.$support-success;
}

.statusHeader {
  padding-bottom: .5rem;
  border-bottom: 1px solid theme.$border-strong-01;
}

.activeOrders {
  padding-bottom: 1rem;
  border-bottom: 1px solid theme.$border-strong-01;
}

.notification {
  background-color: colors.$yellow-10;
  color: black
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.drillDownArrow {
  display: flex;
  flex-direction: row-reverse;
}