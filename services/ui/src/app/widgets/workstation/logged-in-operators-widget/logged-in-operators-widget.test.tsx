import { DatePeriod } from "../../../types";

import { render, screen } from "../../../../test-utils";
import type { WidgetFilters } from "../../widget.types";
import { LoggedInOperatorsWidget } from "./logged-in-operators-widget";

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

describe("LoggedInOperatorsWidget", () => {
  // Mock the required WidgetFilters with datePeriodRange using DatePeriod enum
  const mockFilters: WidgetFilters = {
    datePeriodRange: DatePeriod.thisMonth,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render loading state when data is being fetched", () => {
    mockUseQuery.mockReturnValue({
      isLoading: true,
      error: null,
      data: null,
    });

    render(<LoggedInOperatorsWidget filters={mockFilters} />);

    // Check for LoadingOverlay
    expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
  });

  it("hould show no data state when there is an error", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: new Error("Test error"),
      data: null,
    });

    render(<LoggedInOperatorsWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-no-data")).toBeInTheDocument();
  });

  it("should render widget content with correct data", () => {
    const mockData = {
      totalOperators: 42,
      actualLinesPerHour: 80,
      targetLinesPerHour: 100,
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<LoggedInOperatorsWidget filters={mockFilters} />);

    // Check for title and operator count
    expect(screen.getByText("Logged in Operators")).toBeInTheDocument();
    expect(screen.getByText("42")).toBeInTheDocument();
    expect(screen.getByText("Total lines/hr")).toBeInTheDocument();

    // Check for metrics
    expect(screen.getByText("80")).toBeInTheDocument(); // Actual lines per hour
    expect(screen.getByText("100")).toBeInTheDocument(); // Target lines per hour
    expect(screen.getByText("Actual")).toBeInTheDocument();
    expect(screen.getByText("Expected")).toBeInTheDocument();

    // Check for progress percentage (80/100 * 100 = 80%)
    expect(screen.getByText("80%")).toBeInTheDocument();
  });

  it("should calculate progress percentage correctly", () => {
    const mockData = {
      totalOperators: 10,
      actualLinesPerHour: 75,
      targetLinesPerHour: 150,
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<LoggedInOperatorsWidget filters={mockFilters} />);

    // 75/150 * 100 = 50%
    expect(screen.getByText("50%")).toBeInTheDocument();
  });

  it("should handle zero targetLinesPerHour and display 0%", () => {
    const mockData = {
      totalOperators: 5,
      actualLinesPerHour: 60,
      targetLinesPerHour: 0,
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<LoggedInOperatorsWidget filters={mockFilters} />);

    // When targetLinesPerHour is 0, should show 0% instead of NaN%
    expect(screen.getByText("0%")).toBeInTheDocument();
  });

  it("should make API call with correct parameters", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: {
        totalOperators: 5,
        actualLinesPerHour: 60,
        targetLinesPerHour: 100,
      },
    });

    render(<LoggedInOperatorsWidget filters={mockFilters} />);

    // Verify the API call parameters
    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/workstation/metrics/operators",
      { params: {} },
      { enabled: true },
    );
  });
});
