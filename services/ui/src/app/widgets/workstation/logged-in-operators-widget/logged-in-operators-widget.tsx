import { Heading, ProgressBar, Section, Stack } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { WidgetFilters } from "../../widget.types";
import styles from "./logged-in-operators-widget.module.css";
import { useTranslation } from "react-i18next";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";

export interface LoggedInOperatorsWidgetProps {
  filters: WidgetFilters;
}

export const LoggedInOperatorsWidget = ({
  filters,
}: LoggedInOperatorsWidgetProps) => {
  const { t } = useTranslation();
  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/operators",
    {
      params: {},
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("loggedInWidgetOperators.title", "Logged in Operators")}
        loading
      />
    );
  if (error)
    return (
      <WidgetContainer
        title={t("loggedInWidgetOperators.title", "Logged in Operators")}
        noData={true}
      />
    );

  // Calculate progress percentage
  const progressPercentage =
    data && data.targetLinesPerHour > 0
      ? Math.round((data.actualLinesPerHour / data.targetLinesPerHour) * 100)
      : 0;

  return (
    <WidgetContainer
      title={t("loggedInWidgetOperators.title", "Logged in Operators")}
    >
      <Stack gap={2} style={{ width: "100%" }}>
        <Section level={2}>
          <Heading
            data-testid="total-operators"
            className={styles.operatorCount}
          >
            {data?.totalOperators}
          </Heading>
          <p className={styles.label}>
            {t("loggedInWidgetOperators.totalLinesPerHour", "Total lines/hr")}
          </p>
        </Section>
        <div>
          <ProgressBar
            data-testid="progress-percentage"
            value={progressPercentage}
            max={100}
            label={`${progressPercentage}%`}
            helperText=""
            size="big"
          />
        </div>
        <div
          className={`${styles.comparisonText} ${styles.comparisonTextSpacing}`}
        >
          <p data-testid="lines-per-hour-actual">{data?.actualLinesPerHour}</p>
          <p data-testid="lines-per-hour-target">{data?.targetLinesPerHour}</p>
        </div>
        <div className={styles.comparisonText}>
          <p className={styles.label}>
            {t("loggedInWidgetOperators.actualLabel", "Actual")}
          </p>
          <p className={styles.label}>
            {t("loggedInWidgetOperators.expectedLabel", "Expected")}
          </p>
        </div>
      </Stack>
    </WidgetContainer>
  );
};

export default LoggedInOperatorsWidget;
