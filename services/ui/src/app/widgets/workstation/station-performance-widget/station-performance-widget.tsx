import { Heading, Section, Stack } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import { formatMinutes } from "../../../utils";
import type { WidgetFilters } from "../../widget.types";
import styles from "./station-performance-widget.module.css";
import { useDates } from "../../../hooks/use-dates";
import { useTranslation } from "react-i18next";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";

export interface StationPerformanceWidgetProps {
  filters: WidgetFilters;
}

export const StationPerformanceWidget = ({
  filters,
}: StationPerformanceWidgetProps) => {
  const { t } = useTranslation();
  const { startDate, endDate } = useDates(filters.datePeriodRange);

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/performance",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("stationPerformanceWidget.title", "Station Performance")}
        loading
      />
    );
  if (error)
    return (
      <WidgetContainer
        title={t("stationPerformanceWidget.title", "Station Performance")}
        noData={true}
      />
    );

  return (
    <WidgetContainer
      title={t("stationPerformanceWidget.title", "Station Performance")}
    >
      <Stack gap={4} style={{ width: "100%" }}>
        <div className={styles.performanceMetrics}>
          <Section level={4} className={styles.totalActiveTime}>
            <Heading>
              {t(
                "stationPerformanceWidget.totalActiveTime",
                "Total Active Time",
              )}
            </Heading>
            <p>{formatMinutes(data?.totalActiveTime || 0)}</p>
          </Section>
          <Section level={4} className={styles.totalStarvedTime}>
            <Heading>
              {t(
                "stationPerformanceWidget.totalStarvedTime",
                "Total Starved Time",
              )}
            </Heading>
            <p>{formatMinutes(data?.totalStarvationTime || 0)}</p>
          </Section>
        </div>
      </Stack>
    </WidgetContainer>
  );
};

export default StationPerformanceWidget;
