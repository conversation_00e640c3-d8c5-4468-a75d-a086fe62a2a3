import { DatePeriod } from "../../../types";

import { render, screen } from "../../../../test-utils";
import type { WidgetFilters } from "../../widget.types";
import { StationPerformanceWidget } from "./station-performance-widget";

// Mock formatMinutes utility function
vi.mock("../../../utils", () => ({
  formatMinutes: (minutes: number) => `${minutes} mins`,
}));

vi.mock("../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-31"),
  }),
}));

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

describe("StationPerformanceWidget", () => {
  // Mock the required WidgetFilters with datePeriodRange using DatePeriod enum
  const mockFilters: WidgetFilters = {
    datePeriodRange: DatePeriod.thisMonth,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render loading state when data is being fetched", () => {
    mockUseQuery.mockReturnValue({
      isLoading: true,
      error: null,
      data: null,
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
  });

  it("hould show no data state when there is an error", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: new Error("Test error"),
      data: null,
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-no-data")).toBeInTheDocument();
  });

  it("should render widget content with correct data", () => {
    const mockData = {
      totalActiveTime: 480, // 480 minutes
      totalStarvationTime: 120, // 120 minutes
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    // Check for title
    expect(screen.getByText("Station Performance")).toBeInTheDocument();

    // Check for active time section
    expect(screen.getByText("Total Active Time")).toBeInTheDocument();
    expect(screen.getByText("480 mins")).toBeInTheDocument();

    // Check for starved time section
    expect(screen.getByText("Total Starved Time")).toBeInTheDocument();
    expect(screen.getByText("120 mins")).toBeInTheDocument();
  });

  it("should display 0 mins when time values are not provided", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: {},
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    // Check for default values
    const zeroMinTexts = screen.getAllByText("0 mins");
    expect(zeroMinTexts).toHaveLength(2); // Both active and starved time should show 0 mins
  });

  it("should handle one missing time value", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: {
        totalActiveTime: 240,
        // totalStarvationTime is missing
      },
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    // Check that active time is displayed correctly
    expect(screen.getByText("240 mins")).toBeInTheDocument();

    // Check that starved time defaults to 0
    const texts = screen.getAllByText(/mins/);
    expect(texts).toHaveLength(2);
    expect(texts[1].textContent).toBe("0 mins");
  });

  it("should make API call with correct parameters", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: { totalActiveTime: 300, totalStarvationTime: 60 },
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    // Verify the API call parameters
    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/workstation/metrics/performance",
      {
        params: {
          query: {
            start_date: "2023-01-01T00:00:00.000Z",
            end_date: "2023-01-31T00:00:00.000Z",
          },
        },
      },
      { enabled: true },
    );
  });
});
