import { Heading, Stack } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import { formatMinutes } from "../../../utils";
import type { WidgetFilters } from "../../widget.types";
import styles from "./station-health-widget.module.css";
import { useTranslation } from "react-i18next";
import { useDates } from "../../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";

export interface StationHealthWidgetProps {
  filters: WidgetFilters;
}

export const StationHealthWidget = ({ filters }: StationHealthWidgetProps) => {
  const { startDate, endDate } = useDates(filters.datePeriodRange);

  const { t } = useTranslation();

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/health",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("stationHealthWidget.title", "Station Health")}
        loading
      />
    );
  if (error)
    return (
      <WidgetContainer
        title={t("stationHealthWidget.title", "Station Health")}
        noData={true}
      />
    );

  return (
    <WidgetContainer title={t("stationHealthWidget.title", "Station Health")}>
      <Stack gap={4} style={{ width: "100%" }}>
        <h4>{t("stationHealthWidget.totalDownTime", "Total Down Time")}</h4>
        <div className={styles.metricValue}>
          <Heading>{formatMinutes(data?.totalDowntime || 0)}</Heading>
        </div>
      </Stack>
    </WidgetContainer>
  );
};

export default StationHealthWidget;
