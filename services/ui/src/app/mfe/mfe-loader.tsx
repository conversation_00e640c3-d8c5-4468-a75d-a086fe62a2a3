import { useAuth0 } from "@auth0/auth0-react";
import { useNavigate } from "react-router";
import type { ParcelConfig } from "single-spa";
import { mountRootParcel } from "single-spa";
import Parcel from "single-spa-react/parcel";
import { findMfeConfig } from "./mfe-config";
import { moduleLoader } from "./module-loader";

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> is responsible for loading the main micro-frontend application
 */
export const MfeLoader = ({ path }: { path: string }) => {
  const navigate = useNavigate();

  // Get values from contexts
  const auth0Context = useAuth0();

  async function getConfig(): Promise<ParcelConfig> {
    const mfeConfig = findMfeConfig(path);
    if (!mfeConfig) {
      throw new Error(`No MFE configuration found for path: ${path}`);
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let module: any;
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      module = (await moduleLoader.loadModule(mfeConfig.url)) as any;
    } catch (error) {
      console.error("Error loading module:", error);
      navigate("/error");
    }

    return {
      bootstrap: module.bootstrap,
      mount: module.mount,
      unmount: module.unmount,
    };
  }

  const mfeProps = {
    auth0Context,
  };

  return (
    <Parcel
      key={path}
      mountParcel={mountRootParcel}
      // @ts-expect-error - mfe config is passed with a promise
      config={getConfig()}
      wrapWith="div"
      wrapClassName="mfe-container"
      {...mfeProps}
    />
  );
};
