import React from "react";
import { useParams } from "react-router";
import { LoadingView } from "../../components/loading-view/loading-view";
import { useConfigSetting } from "../../config/hooks/use-config";
import { useMenu } from "../../config/menu/use-menu";
import { NotFound } from "../../views/not-found/not-found";
import { getView } from "../../views/view-registry";
import type { BaseViewProps } from "../../views/view-registry.types";
import { findMenuItem } from "../router.util";
import { useTranslation } from "react-i18next";

/**
 * DynamicRouteView is a component that dynamically renders a view based on the current
 * path parameter from the URL.
 *
 * The component flow:
 * 1. Gets the current path from URL parameters
 * 2. Fetches menu items and finds the matching menu item for the path
 * 3. Loads any configuration settings associated with the menu item
 * 4. Dynamically loads and renders the appropriate view component
 *
 * If no matching menu item is found, it shows a loading state or NotFound component.
 * If no view is registered for the menu item's viewType, it shows a NotFound component.
 */
export const DynamicRouteView = React.memo(function DynamicRouteView() {
  const { path } = useParams();
  const { t } = useTranslation();
  const { menuItems, isLoading } = useMenu(t);
  const menuItem = findMenuItem(menuItems, `/${path}`);

  const { setting } = useConfigSetting(menuItem?.viewConfigId);

  // If we don't have a menu item, we need to show a loading view or not found view
  if (!menuItem) {
    if (isLoading) {
      return <LoadingView />;
    }
    return <NotFound />;
  }

  // Load our view dynamically based off of the id from the menu
  const view = getView(menuItem.viewType ?? "");

  if (!view) {
    return <NotFound />;
  }

  if (!path) return null;
  const ViewComponent = view.component as React.ComponentType<BaseViewProps>;

  //TODO: Will have to update this once we setup localization
  if (menuItem.label) {
    document.title = `${menuItem.label} - Dematic Control Tower`;
  } else {
    document.title = "Dematic - Control Tower";
  }

  return (
    <ViewComponent
      id={menuItem.viewConfigId ?? path}
      setting={setting}
      options={setting?.value}
    />
  );
});
