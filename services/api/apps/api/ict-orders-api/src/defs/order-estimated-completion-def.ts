export type CustomerOrderEstimatedCompletion = number;

export interface CustomerOrderEstimatedCompletionData {
  estimatedCompletionMinutes: CustomerOrderEstimatedCompletion;
}

export enum FacilityOrderEventCodes {
  Released = 'RELEASED',
  Active = 'ACTIVE',
  Building = 'BUILDING',
  Cancelled = 'CANCELLED',
  ReleaseFailed = 'RELEASE_FAILED',
  InventoryAllocated = 'INVENTORY_ALLOCATED',
  FailedAllocation = 'FAILED_ALLOCATION',
  AwaitingReleaseConfirmation = 'AWAITING_RELEASE_CONFIRMATION',
  Pending = 'PENDING',
  Complete = 'COMPLETE',
}
