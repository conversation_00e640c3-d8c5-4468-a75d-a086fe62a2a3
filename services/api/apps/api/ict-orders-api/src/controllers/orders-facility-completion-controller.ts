import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {CustomerOrderEstimatedCompletionData} from '../defs/order-estimated-completion-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class OrdersFacilityCompletionController extends Controller {
  public static readonly exampleResponse: CustomerOrderEstimatedCompletionData =
    {
      estimatedCompletionMinutes: 242,
    };

  /**
   *
   * @param {Date} startDate
   * @param {Date} endDate
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {CustomerOrderEstimatedCompletionData} contract
   */
  @Example(OrdersFacilityCompletionController.exampleResponse)
  @Get('/facility/completion')
  @OperationId('GetOrdersFacilityEstimatedCompletion')
  @Tags('orders')
  public async getFacilityOrdersEstimatedCompletion(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<CustomerOrderEstimatedCompletionData> {
    startEndDateValidation(startDate, endDate);
    const orderService = Container.get(OrderService);

    return await orderService.getFacilityOrdersEstimatedCompletion(
      startDate,
      endDate,
    );
  }
}
