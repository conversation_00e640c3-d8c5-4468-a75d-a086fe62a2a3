import {DiService, ContextService, WinstonLogger} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {SelectQueryBuilder} from 'typeorm';
import {MetricConfigFilters} from '../defs/metric-config-filters.ts';

@DiService()
export class MetricConfigStore {
  public static readonly type: string = 'metric-config-store';

  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
  ) {}

  get dbProvider() {
    return this.context.dbProvider;
  }

  async getMetricConfigs(
    filters: MetricConfigFilters,
  ): Promise<
    (DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity)[]
  > {
    this.logger.debug('Getting metric configurations from database', {filters});

    const entityManager =
      this.dbProvider.processFlowPostgres.datasource.manager;
    const defaultRepo = entityManager.getRepository(
      DefaultMetricConfigurationEntity,
    );
    const customRepo = entityManager.getRepository(
      CustomMetricConfigurationEntity,
    );

    // Build query for default configs
    const defaultQueryBuilder = defaultRepo.createQueryBuilder('defaultConfig');
    this.applyFilters(defaultQueryBuilder, filters, 'defaultConfig');

    // Build query for custom configs
    const customQueryBuilder = customRepo.createQueryBuilder('customConfig');
    this.applyFilters(customQueryBuilder, filters, 'customConfig');

    // Get results from both tables
    const [defaultConfigs, customConfigs] = await Promise.all([
      defaultQueryBuilder.getMany(),
      customQueryBuilder.getMany(),
    ]);

    // Create a set of metric names from custom configs
    const customMetricNames = new Set(
      customConfigs.map(config => config.metricConfigName),
    );

    // Filter out default configs that have a custom version
    const filteredDefaultConfigs = defaultConfigs.filter(
      config => !customMetricNames.has(config.metricConfigName),
    );

    // Combine results
    const results = [...filteredDefaultConfigs, ...customConfigs];

    // Transform HSTORE fields to booleans
    const transformedResults = results.map(config =>
      this.transformHstoreFields(config),
    );

    // Apply enabled and active filters in application layer
    const filteredResults = transformedResults.filter(config => {
      // Apply enabled filter (only for default configs)
      if (filters.enabled !== undefined && 'enabled' in config) {
        if (config.enabled !== filters.enabled) {
          return false;
        }
      }

      // Apply active filter
      if (filters.active !== undefined && 'active' in config) {
        if (config.active !== filters.active) {
          return false;
        }
      }

      return true;
    });

    this.logger.debug('Retrieved metric configurations from database', {
      count: filteredResults.length,
      defaultCount: defaultConfigs.length,
      customCount: customConfigs.length,
    });

    return filteredResults;
  }

  private applyFilters<
    T extends
      | DefaultMetricConfigurationEntity
      | CustomMetricConfigurationEntity,
  >(
    queryBuilder: SelectQueryBuilder<T>,
    filters: MetricConfigFilters,
    alias: string,
  ): void {
    if (filters.metricName) {
      queryBuilder.andWhere(`"${alias}".metric_config_name LIKE :metricName`, {
        metricName: `%${filters.metricName}%`,
      });
    }

    if (filters.metricId) {
      queryBuilder.andWhere(`"${alias}".id = :metricId`, {
        metricId: filters.metricId,
      });
    }

    if (filters.factType) {
      queryBuilder.andWhere(`"${alias}".fact_type = :factType`, {
        factType: filters.factType,
      });
    }

    if (filters.nodeName) {
      queryBuilder.andWhere(`"${alias}".node_name LIKE :nodeName`, {
        nodeName: `%${filters.nodeName}%`,
      });
    }

    if (filters.configType) {
      queryBuilder.andWhere(`"${alias}".config_type = :configType`, {
        configType: filters.configType,
      });
    }

    // Note: enabled and active filters are handled in application layer
    // to avoid HSTORE parsing issues in the database
  }

  private transformHstoreFields<
    T extends
      | DefaultMetricConfigurationEntity
      | CustomMetricConfigurationEntity,
  >(config: T): T {
    // Transform active field: null/empty HSTORE -> false, otherwise check if contains 'true'
    if ('active' in config) {
      const activeValue = config.active as unknown;
      config.active =
        activeValue === null ||
        activeValue === '{}' ||
        (typeof activeValue === 'object' &&
          activeValue !== null &&
          Object.keys(activeValue).length === 0)
          ? false
          : typeof activeValue === 'object' &&
            activeValue !== null &&
            'true' in activeValue &&
            (activeValue as Record<string, unknown>).true === true;
    }

    // Transform enabled field: null/empty HSTORE -> true, otherwise check if contains 'true'
    if ('enabled' in config) {
      const enabledValue = config.enabled as unknown;
      config.enabled =
        enabledValue === null ||
        enabledValue === '{}' ||
        (typeof enabledValue === 'object' &&
          enabledValue !== null &&
          Object.keys(enabledValue).length === 0)
          ? true
          : typeof enabledValue === 'object' &&
            enabledValue !== null &&
            'true' in enabledValue &&
            (enabledValue as Record<string, unknown>).true === true;
    }

    return config;
  }
}
