import sinon from 'sinon';
import {expect} from 'chai';
import {ContextService, WinstonLogger} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigStore} from '../../stores/metric-config-store.ts';

describe('MetricConfigStore', () => {
  describe('getMetricConfigs', () => {
    it('should return both default and custom metric configurations', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      const mockEntities = [mockDefaultConfig, mockCustomConfig];

      sinon.stub(metricConfigStore, 'getMetricConfigs').resolves(mockEntities);

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      const result = await metricConfigStore.getMetricConfigs(filters);

      expect(result).to.deep.equal(mockEntities);
    });

    it('should handle table not found error', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigs')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigs(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });

    it('should handle database authentication failure', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigs')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigs(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });

    it('should handle invalid column name error', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigs')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigs(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });
  });
});
