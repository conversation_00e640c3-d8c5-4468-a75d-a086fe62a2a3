import 'reflect-metadata';
import sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  ContextService,
  appSetup,
  ApiMiddleware,
  EnvironmentService,
  Environment,
  ConfigStore,
  AppConfigSettingSource,
  IctError,
  SecurityRoles,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {expect} from 'chai';
import {MetricConfigService} from '../../services/metric-config-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('MetricConfigController', () => {
  const app = appSetup(RegisterRoutes);
  let metricConfigServiceStub: sinon.SinonStubbedInstance<MetricConfigService>;
  let contextServiceRolesStub: sinon.SinonStub;

  before(async () => {
    // Mock environment service to return local environment
    const envService = Container.get(EnvironmentService);
    sinon.stub(envService, 'app').get(() => ({
      env: Environment.local,
      authDomain: 'test-domain',
      authAudience: 'test-audience',
      unitTest: 'true',
    }));

    // Mock ConfigStore to return facility maps
    const configStore = Container.get(ConfigStore);
    sinon.stub(configStore, 'findSpecificSettingValue').resolves({
      id: 'facility-maps',
      name: 'facility-maps',
      group: 'facility',
      dataType: 'json',
      value: [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ],
      source: AppConfigSettingSource.default,
    });

    // Apply all middlewares
    await ApiMiddleware.applyApiDefaultMiddlewares(app);
  });

  beforeEach(() => {
    metricConfigServiceStub = sinon.createStubInstance(MetricConfigService);
    Container.set(MetricConfigService, metricConfigServiceStub);

    // Mock context service roles
    const contextService = Container.get(ContextService);
    contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
    contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigs route', () => {
    const url = '/config/process-flow/metric-configs';
    const queryParams = {
      metricName: 'test-metric',
      configType: 'node',
      metricId: undefined,
      factType: undefined,
      nodeName: undefined,
      active: undefined,
      enabled: undefined,
    };

    it('should return 404 when no default facility exists', async () => {
      // Mock context service to return no default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => []);

      // Stub the metric config service to throw facility error
      metricConfigServiceStub.getMetricConfigs.rejects(
        IctError.notFound(
          'No default facility found in facility maps. Please ensure a default facility is configured.',
        ),
      );

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body).to.have.property(
        'detail',
        'No default facility found in facility maps. Please ensure a default facility is configured.',
      );
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigs);
    });

    it('should return 404 when no metric configs are found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigs.resolves([]);

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body)
        .to.have.property('detail')
        .that.includes('No metric configurations found with filters');
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigs);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigs,
        queryParams,
      );
    });

    it('should return metric configs when found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const mockConfig = {
        id: 'test-config',
        metricName: 'test-metric',
        configType: 'node',
        value: {some: 'value'},
        isCustom: false,
        facilityId: 'default',
      };
      metricConfigServiceStub.getMetricConfigs.resolves([mockConfig]);

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal([mockConfig]);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigs);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigs,
        queryParams,
      );
    });

    it('should handle service errors', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigs.rejects(
        IctError.internalServerError('Service error'),
      );

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigs);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigs,
        queryParams,
      );
    });
  });
});
