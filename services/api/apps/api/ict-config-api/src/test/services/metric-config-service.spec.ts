import sinon from 'sinon';
import {expect} from 'chai';
import {ContextService, WinstonLogger, ConfigStore} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigService} from '../../services/metric-config-service.ts';
import {MetricConfigStore} from '../../stores/metric-config-store.ts';
import {MetricConfigFilters} from '../../defs/metric-config-filters.ts';

describe('MetricConfigService', () => {
  let service: MetricConfigService;
  let metricConfigStore: MetricConfigStore;
  let context: ContextService;
  let logger: WinstonLogger;
  let configStore: ConfigStore;

  beforeEach(() => {
    context = new ContextService();
    logger = new WinstonLogger(context);
    configStore = new ConfigStore(context);
    metricConfigStore = new MetricConfigStore(context, logger);
    service = new MetricConfigService(
      configStore,
      logger,
      context,
      metricConfigStore
    );

    // Mock facility maps
    sinon.stub(context, 'facilityMaps').get(() => [
      {
        id: 'test-facility',
        name: 'Test Facility',
        dataset: 'test-dataset',
        default: true,
      },
    ]);

    // Mock store's getMetricConfigs method
    sinon.stub(metricConfigStore, 'getMetricConfigs').resolves([]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigs', () => {
    it('should return both default and custom metric configurations', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      const mockConfigs = [mockDefaultConfig, mockCustomConfig];

      (metricConfigStore.getMetricConfigs as sinon.SinonStub).resolves(
        mockConfigs
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigs(filters);

      expect(result).to.deep.equal(
        mockConfigs.map(config => ({
          id: config.id,
          metricName: config.metricConfigName,
          configType: config.configType,
          nodeName: config.nodeName,
          factType: config.factType,
          enabled:
            config instanceof DefaultMetricConfigurationEntity
              ? config.enabled
              : null,
          active:
            config instanceof CustomMetricConfigurationEntity
              ? config.active
              : config.active,
          isCustom: config instanceof CustomMetricConfigurationEntity,
          facilityId:
            config instanceof CustomMetricConfigurationEntity
              ? config.facilityId
              : 'default',
        }))
      );
    });

    it('should throw not found error when no facility ID is provided and no default facility exists', async () => {
      sinon.stub(context, 'facilityMaps').get(() => []);
      sinon.stub(context, 'selectedFacilityId').get(() => undefined);

      const filters: MetricConfigFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(service.getMetricConfigs(filters)).to.be.rejectedWith(
        'No default facility found in facility maps. Please ensure a default facility is configured.'
      );
    });

    it('should filter out default configs that have custom versions', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      (metricConfigStore.getMetricConfigs as sinon.SinonStub).resolves([
        mockCustomConfig,
      ]);
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigs(filters);

      expect(result).to.deep.equal([
        {
          id: mockCustomConfig.id,
          metricName: mockCustomConfig.metricConfigName,
          configType: mockCustomConfig.configType,
          nodeName: mockCustomConfig.nodeName,
          factType: mockCustomConfig.factType,
          enabled: null,
          active: mockCustomConfig.active,
          isCustom: true,
          facilityId: mockCustomConfig.facilityId,
        },
      ]);
    });

    it('should filter out disabled configs', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: false};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      (metricConfigStore.getMetricConfigs as sinon.SinonStub).resolves([]);
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(service.getMetricConfigs(filters)).to.be.rejectedWith(
        'No metric configurations found matching the criteria'
      );
    });

    it('should handle store errors', async () => {
      (metricConfigStore.getMetricConfigs as sinon.SinonStub).rejects(
        new Error('Store error')
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(service.getMetricConfigs(filters)).to.be.rejectedWith(
        'Store error'
      );
    });
  });
});
