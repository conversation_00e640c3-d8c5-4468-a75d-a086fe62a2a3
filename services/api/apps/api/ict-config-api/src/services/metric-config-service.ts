import {
  DiService,
  WinstonLogger,
  ConfigStore,
  ContextService,
  IctError,
} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfig} from '../defs/metric-config.ts';
import {MetricConfigStore} from '../stores/metric-config-store.ts';
import {MetricConfigFilters} from '../defs/metric-config-filters.ts';

@DiService()
export class MetricConfigService {
  public static type = 'metric-config-service';

  constructor(
    private configStore: ConfigStore,
    private logger: WinstonLogger,
    private context: ContextService,
    private metricConfigStore: MetricConfigStore
  ) {}

  /**
   * Retrieves metric configurations based on the provided filters.
   * @param filters The filter criteria to apply when fetching metric configurations
   * @returns A promise that resolves to an array of metric configurations
   * @throws IctError.notFound if no configurations are found
   */
  async getMetricConfigs(
    filters: MetricConfigFilters
  ): Promise<MetricConfig[]> {
    this.logger.info('Getting metric configurations', {filters});

    // Check for facility ID first
    let facilityId = this.context.selectedFacilityId;

    // if the selected facility was not passed then look it up...
    if (!facilityId) {
      this.logger.info('no selected facility map was passed in.');

      // get the default facility from the map...
      const selectedFacility = this.context.facilityMaps?.find(
        f => f.default === true
      );
      if (selectedFacility) {
        facilityId = selectedFacility.id;
      } else {
        throw IctError.notFound(
          'No default facility found in facility maps. Please ensure a default facility is configured.'
        );
      }
    }

    const entities = await this.metricConfigStore.getMetricConfigs(filters);

    this.logger.info('Retrieved metric configurations', {
      count: entities.length,
      filters,
    });

    if (entities.length === 0) {
      throw IctError.notFound(
        'No metric configurations found matching the criteria'
      );
    }

    // Map database entities to the MetricConfig interface
    return entities.map(entity => this.mapEntityToMetricConfig(entity));
  }

  /**
   * Maps a database entity to the MetricConfig interface
   * @param entity The database entity to map
   * @returns A MetricConfig object
   */
  private mapEntityToMetricConfig(
    entity: DefaultMetricConfigurationEntity | CustomMetricConfigurationEntity
  ): MetricConfig {
    const isCustom = entity instanceof CustomMetricConfigurationEntity;
    if (isCustom) {
      return {
        id: entity.id,
        metricName: entity.metricConfigName,
        configType: entity.configType,
        nodeName: entity.nodeName,
        factType: entity.factType,
        enabled: null,
        active: (entity as CustomMetricConfigurationEntity).active,
        isCustom,
        facilityId: (entity as CustomMetricConfigurationEntity).facilityId,
      };
    }
    return {
      id: entity.id,
      metricName: entity.metricConfigName,
      configType: entity.configType,
      nodeName: entity.nodeName,
      factType: entity.factType,
      enabled: (entity as DefaultMetricConfigurationEntity).enabled,
      active: (entity as DefaultMetricConfigurationEntity).active,
      isCustom,
      facilityId: 'default',
    };
  }

  getType(): string {
    return MetricConfigService.type;
  }
}
