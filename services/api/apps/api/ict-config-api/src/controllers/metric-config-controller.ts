import {
  Container,
  ProtectedRouteMiddleware,
  DatabaseTypes,
  PostgresDatabaseOptions,
  IctError,
  HttpStatusCodes,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Controller,
  Example,
  Get,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Response,
  Middlewares,
} from 'tsoa';
import {MetricConfigService} from '../services/metric-config-service.ts';
import {MetricConfig} from '../defs/metric-config.ts';

@Route('config/process-flow')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: {
          entityType: EntityTypes.Config,
        } as PostgresDatabaseOptions,
      },
      {
        type: DatabaseTypes.Postgres,
        options: {
          entityType: EntityTypes.ProcessFlow,
        } as PostgresDatabaseOptions,
      },
    ],
  }),
])
export class MetricConfigController extends Controller {
  static exampleResponse: MetricConfig[] = [
    {
      id: '1',
      metricName: 'test-metric',
      configType: 'node',
      nodeName: 'test-node',
      factType: 'test-fact',
      enabled: true,
      active: true,
      isCustom: false,
      facilityId: 'default',
    },
  ];

  /**
   * Get metric configurations based on filter criteria.
   * Without any query params, returns all metric configs that are not soft_deleted.
   * @param {string} metricName Filter by metric name
   * @param {string} metricId Filter by metric ID
   * @param {string} factType Filter by fact type
   * @param {string} nodeName Filter by node name
   * @param {boolean} active Filter by active status
   * @param {boolean} enabled Filter by enabled status
   * @param {string} configType Filter by config type
   */
  @Example<MetricConfig[]>(MetricConfigController.exampleResponse)
  @SuccessResponse(
    HttpStatusCodes.OK,
    'Returns a list of metric configurations',
  )
  @Response(
    HttpStatusCodes.NOT_FOUND,
    'No metric configurations found matching the criteria',
  )
  @Response(HttpStatusCodes.INTERNAL_SERVER_ERROR, 'Internal server error')
  @Get('metric-configs')
  @OperationId('GetMetricConfigs')
  @Tags('config')
  public async getMetricConfigs(
    @Query() metricName?: string,
    @Query() metricId?: string,
    @Query() factType?: string,
    @Query() nodeName?: string,
    @Query() active?: boolean,
    @Query() enabled?: boolean,
    @Query() configType?: string,
  ): Promise<MetricConfig[]> {
    // Get the metric config service
    const service: MetricConfigService = Container.get(MetricConfigService);

    const filters = {
      metricName,
      metricId,
      factType,
      nodeName,
      active,
      enabled,
      configType,
    };

    const metricConfigs = await service.getMetricConfigs(filters);

    if (!metricConfigs || metricConfigs.length === 0) {
      const filterDetails = Object.entries(filters)
        .filter(([, value]) => value !== undefined)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');

      throw IctError.notFound(
        `No metric configurations found with filters: ${filterDetails}`,
      );
    }

    return metricConfigs;
  }
}
