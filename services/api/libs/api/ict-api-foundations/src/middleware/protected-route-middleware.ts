import {Request, Response, NextFunction} from 'express';
import {BigQuery} from '@google-cloud/bigquery';
import {ContextService} from '../context/context-service.ts';
import {BigQueryDatabase} from '../db/bigquery.ts';
import {DatabaseProvider} from '../db/database-provider.ts';
import {Database} from '../db/database.ts';
import {
  DBMiddlewareDatabaseOption,
  DatabaseMiddlewareOptions,
  DatabaseOptionTemplates,
  DatabaseOptions,
  DatabaseTypes,
  PgPostgresDatabaseOptions,
  PostgresDatabaseOptions,
} from '../db/db-types.ts';
import {Container} from '../di/type-di.ts';
import {IctError} from '../index.ts';
import {EnvironmentService} from '../services/environment-service.ts';
import {ServiceProvider} from '../services/service-provider.ts';
import {StoreProvider} from '../stores/store-provider.ts';
import Neo4jFactory from '../db/neo4j-factory.ts';
import {PostgresFactory} from '../db/postgres-factory.ts';

let bigQuery: BigQuery;

export class ProtectedRouteMiddleware {
  constructor() {}

  /**
   * Express middleware that will ensure a request is authenticated before calling the route handler and create
   * user specific databse objects for the request.
   * @param route Route to protect.
   * @param userAuthOptions Optional set of parameters to use for user auth.
   * @param dbOptions Optional set of parameters to use for creating user specific database interactors.
   * @returns Express middleware function.
   */
  public static apiProtectedDataRoute(
    // eslint-disable-next-line no-empty-function
    dataMiddleware: (req: Request, res: Response) => void = () => {},
    dbOptions: DatabaseMiddlewareOptions = new DatabaseMiddlewareOptions(),
  ) {
    const envService = Container.get(EnvironmentService);
    return async function handleUnitTests(
      req: Request,
      res: Response,
      next: NextFunction,
    ) {
      try {
        // Temporary short circuit for testing until we can find a way to mock this with decorators
        const isUnitTest = envService.app.unitTest === 'true';
        if (!isUnitTest) {
          // create databases
          await ProtectedRouteMiddleware.createDatabasesForUser(res, dbOptions);

          // create providers
          res.locals.stores = new StoreProvider();
          res.locals.services = new ServiceProvider();

          dataMiddleware(req, res);
        }

        next();
      } catch (e) {
        next(e);
      }
    };
  }

  /**
   * Create databases for an authenticated Express request and store them in the Express response locals.
   * @param req Express request.
   * @param res Express response.
   * @param options Optional set of parameters to use for creating user specific database interactors.
   */
  public static async createDatabasesForUser(
    res: Response,
    options: DatabaseMiddlewareOptions = new DatabaseMiddlewareOptions(),
  ) {
    // create databases listed in the options
    const dbProvider = new DatabaseProvider();
    const dbPromises = options.databases.map(async dbData => {
      return ProtectedRouteMiddleware.createDatabaseFromTypeForUser(dbData);
    });
    const databases = await Promise.all(dbPromises);
    for (const db of databases) {
      dbProvider.set(db.getType(), db);
    }

    res.locals.databases = dbProvider;
    const contextService = Container.get(ContextService);
    contextService.dbProvider = dbProvider;
  }

  /**
   * Create a database from a specified type for a user.
   * @param type Database type to create.
   * @param request Express request that contains an authenticated user session.
   * @returns Database object
   */
  public static async createDatabaseFromTypeForUser(
    option: DBMiddlewareDatabaseOption,
  ): Promise<Database> {
    const contextService = Container.get(ContextService);

    // prepare options both generically and per database type
    option.options = ProtectedRouteMiddleware.prepareDatabaseOptions(
      option.options,
    );
    if (option.options && option.options.prepareOptions) {
      option.options = option.options.prepareOptions(option.options);
    }

    try {
      switch (option.type) {
        case DatabaseTypes.BigQuery:
          if (!bigQuery) {
            const envService = Container.get(EnvironmentService);
            const projectId = envService.apiProject.projectId;
            bigQuery = new BigQuery({projectId});
          }
          return new BigQueryDatabase(
            bigQuery,
            option.options && option.options.database
              ? option.options.database
              : contextService.datasetId,
          );
        case DatabaseTypes.Neo4j: {
          const neo4jfactory = Container.get(Neo4jFactory);
          const tenantDb = await neo4jfactory.getDatabase(
            contextService.datasetId,
          );
          return tenantDb;
        }
        case DatabaseTypes.Postgres: {
          if (!option || !option.options) {
            throw new Error('Invalid options specified for Postgres database.');
          }
          const postgresFactory = Container.get(PostgresFactory);
          const postgresOptions = option.options as PostgresDatabaseOptions;
          const database = option.options.database
            ? option.options.database
            : contextService.databaseId;

          return postgresFactory.getDatabase(
            database,
            postgresOptions.entityType,
          );
        }
        case DatabaseTypes.PgPostgres: {
          const pgPostgresOptions = option.options as
            | PgPostgresDatabaseOptions
            | undefined;
          const database =
            pgPostgresOptions?.database ?? contextService.databaseId;

          const postgresFactory = Container.get(PostgresFactory);
          return postgresFactory.getPgDatabase(
            database,
            pgPostgresOptions?.secretName,
            pgPostgresOptions?.secretProjectId,
          );
        }
        default:
          throw new Error(`Unsupported database type: ${option.type}`);
      }
    } catch (err) {
      throw new IctError(
        503,
        `Failed to connect and create database object of type ${option.type}`,
        err,
      );
    }
  }

  // generic function called on all types of database options
  private static prepareDatabaseOptions(
    options?: DatabaseOptions,
  ): DatabaseOptions | undefined {
    const contextService = Container.get(ContextService);

    let databaseId =
      options && options.database ? options.database : contextService.datasetId;
    databaseId = databaseId.replace(
      DatabaseOptionTemplates.DatabaseId,
      contextService.databaseId,
    );

    let newOptions: DatabaseOptions | undefined = options;
    if (options) {
      newOptions = {
        ...options,
        database: databaseId,
      };
    }

    return newOptions;
  }
}
