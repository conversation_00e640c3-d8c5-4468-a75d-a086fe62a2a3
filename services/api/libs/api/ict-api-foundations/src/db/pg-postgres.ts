import pg from 'pg';
import {Database} from './database.ts';
import {DatabaseTypes} from './db-types.ts';
import {Container} from '../di/type-di.ts';
import {WinstonLogger} from '../log/winston-logger.ts';

export interface PgPostgresDatabaseConnectionOptions {
  database: string;
  user: string;
  password: string;
  host: string;
  port: number;
}

export class PgPostgresDatabase extends Database {
  private logger: WinstonLogger;
  private pool: pg.Pool;

  constructor(options: PgPostgresDatabaseConnectionOptions) {
    super();
    this.logger = Container.get(WinstonLogger);

    this.pool = new pg.Pool({
      user: options.user,
      host: options.host,
      database: options.database,
      password: options.password,
      port: options.port,
    });
  }

  public async query<T extends pg.QueryResultRow>(
    text: string,
    params?: unknown[],
  ): Promise<pg.QueryResult<T>> {
    const start = Date.now();
    const res = await this.pool.query<T>(text, params);
    const duration = Date.now() - start;
    this.logger.info('PgPostgresDatabase: executed query', {
      text,
      duration,
      rows: res.rowCount,
    });

    return res;
  }

  public getType(): string {
    return DatabaseTypes.PgPostgres;
  }

  public static async generateDefaultPgPostgresOptions(
    database: string,
    secretName?: string,
    secretProjectId?: string,
  ): Promise<PgPostgresDatabaseConnectionOptions> {
    const defaultSecretName = process.env.GCP_PG_POSTGRES_SECRET_NAME || '';
    const secretString = secretName ?? defaultSecretName;

    if (!secretString) {
      throw new Error('PostgreSQL secret name not provided and GCP_PG_POSTGRES_SECRET_NAME environment variable is not set');
    }

    let options: PgPostgresDatabaseConnectionOptions;
    try {
      options = JSON.parse(secretString) as PgPostgresDatabaseConnectionOptions;
    } catch (error) {
      throw new Error('Failed to parse PostgreSQL configuration from secret environment variable');
    }

      // validate the options
      if (
        !options ||
        typeof options.user !== 'string' ||
        typeof options.password !== 'string' ||
        typeof options.host !== 'string' ||
        typeof options.port !== 'number'
      ) {
        throw new Error(
          'Invalid Postgres database options retrieved from secret manager.',
        );
      }

      return {
        ...options,
        database,
      };
    }

    const portStr = envService.postgresDb.port;
    const port = portStr ? parseInt(portStr, 10) : 5432;
    return {
      database,
      user: envService.postgresDb.username || 'postgres',
      password: envService.postgresDb.password || 'devpassword',
      host: envService.postgresDb.host || 'localhost',
      port,
    };
  }
}
