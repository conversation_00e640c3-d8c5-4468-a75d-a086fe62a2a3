import {DiService} from '../di/type-di.ts';
import {
  GCPSecretManager,
  GCPSecretManagerQuery,
} from '../secrets/gcp-secret-manager.ts';
import {Neo4jDatabase} from './neo4j.ts';
import {EnvironmentService} from '../services/environment-service.ts';
import {WinstonLogger} from '../log/winston-logger.ts';

interface Neo4jConfig {
  NEO4J_URI: string;
  NEO4J_USERNAME: string;
  NEO4J_PASSWORD: string;
}

@DiService()
class Neo4jFactory {
  private instances: Map<string, Neo4jDatabase> = new Map();

  constructor(
    private secretManager: GCPSecretManager,
    private envService: EnvironmentService,
    private logger: WinstonLogger,
  ) {}

  public async getDatabase(datasetId: string): Promise<Neo4jDatabase> {
    if (this.instances.has(datasetId)) {
      return this.instances.get(datasetId)!;
    }

    let neo4jConfig: Neo4jConfig;

    if (
      this.envService.neo4j.uri &&
      this.envService.neo4j.username &&
      this.envService.neo4j.password
    ) {
      // If the local environment has Neo4J env variables set, use those instead of
      // reaching out to GCP Secret Manager to load them dynamically
      neo4jConfig = {
        NEO4J_URI: this.envService.neo4j.uri,
        NEO4J_USERNAME: this.envService.neo4j.username,
        NEO4J_PASSWORD: this.envService.neo4j.password,
      };
    } else {
      // Retrieve the Neo4J secrets from GCP Secret Manager
      // TODO: GCP Secret Manager SDK usage - Via GCPSecretManager wrapper class for Neo4J secrets
      const secretQuery = new GCPSecretManagerQuery();
      secretQuery.name = `projects/${this.envService.apiProject.projectId}/secrets/${datasetId}_AuraDB/versions/latest`;
      this.logger.info('Retrieving secret for Neo4J', {secretQuery, datasetId});
      const secretValue = await this.secretManager.get(secretQuery);
      neo4jConfig = JSON.parse(secretValue);
    }
    const {NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD} = neo4jConfig;

    // Validate connection information
    if (!NEO4J_URI || !NEO4J_USERNAME || !NEO4J_PASSWORD) {
      throw new Error(
        'Neo4j configuration is incomplete. Please check the secret values for NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD.',
      );
    }

    // Create and initialize the Neo4jDatabase instance
    const neo4jDb = new Neo4jDatabase(
      NEO4J_URI,
      NEO4J_USERNAME,
      NEO4J_PASSWORD,
      'neo4j', // use default db name for now
    );
    await neo4jDb.initialize();

    // Cache the instance for reuse
    this.instances.set(datasetId, neo4jDb);
    this.logger.info('Neo4j instance created and cached', {
      datasetId,
      uri: NEO4J_URI,
    });
    return neo4jDb;
  }
}

export default Neo4jFactory;
