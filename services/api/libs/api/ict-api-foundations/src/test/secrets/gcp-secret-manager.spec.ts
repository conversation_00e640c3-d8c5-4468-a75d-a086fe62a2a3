import {assert} from 'chai';
import sinon from 'sinon';
import {GCPSecretManager} from '../../secrets/gcp-secret-manager.ts';
// TODO: GCP Secret Manager SDK usage - Test file import for mocking
import {SecretManagerServiceClient} from '@google-cloud/secret-manager';

describe('GCPSecretManager', () => {
  const testSecretManager = new GCPSecretManager();

  before(() => {
    // replace the accessSecretVersion in the returned object from client() with a spy
    // TODO: GCP Secret Manager SDK usage - Test mocking of SecretManagerServiceClient
    sinon.stub(testSecretManager, 'client').callsFake(() => {
      const clientStub = sinon.createStubInstance(SecretManagerServiceClient);
      clientStub.accessSecretVersion.resolves([
        {
          payload: {
            data: Buffer.from('testSecret'),
          },
        },
      ]);
      return clientStub;
    });
  });
  after(() => {
    sinon.restore();
  });

  it('should return expected secret', async () => {
    const testSecret = await testSecretManager.get({});
    assert.equal(testSecret, 'testSecret');
  });
});
