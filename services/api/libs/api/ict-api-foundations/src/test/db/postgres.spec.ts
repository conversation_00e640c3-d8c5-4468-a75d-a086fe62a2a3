import sinon from 'sinon';
import {DataSource} from 'typeorm';
import {expect} from 'chai';
import {
  EntityTypes,
  SettingLogSubscriber,
  ConfigEntities,
} from 'ict-api-schema';
import {PostgresDatabase} from '../../db/postgres.ts';
import {Environment, GCPSecretManager} from '../../index.ts';

describe('PostgresDatabase', () => {
  const testDataSource = sinon.createStubInstance(DataSource);
  const testDatabase = new PostgresDatabase(testDataSource, EntityTypes.Config);

  beforeEach(() => {
    sinon.replace(
      GCPSecretManager.prototype,
      'get',
      sinon.fake.resolves(`{
      "type": "postgres",
      "host": "test-host",
      "port": 1234,
      "username": "test-user",
      "password": "test-password"
    }`)
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('get datasource', () => {
    it('should return the datasource', () => {
      expect(testDatabase.datasource).to.deep.equal(testDataSource);
    });
  });

  describe('generateDefaultPostgresOptions', () => {
    it('should return defaut options for a dev environment', async () => {
      process.env.ENVIRONMENT = Environment.local;
      process.env.POSTGRES_HOST = 'test-host';
      process.env.POSTGRES_PORT = '1234';
      process.env.POSTGRES_USERNAME = 'test-user';
      process.env.POSTGRES_PASSWORD = 'test-password';
      const testDatabaseName = 'testDatabase';

      const options = await PostgresDatabase.generateDefaultPostgresOptions(
        testDatabaseName,
        EntityTypes.Config
      );
      expect(options).to.deep.equal({
        type: 'postgres',
        host: 'test-host',
        port: 1234,
        username: 'test-user',
        password: 'test-password',
        logging: true,
        schema: 'config',
        subscribers: [SettingLogSubscriber],
        entities: ConfigEntities,
        database: testDatabaseName,
      });

      delete process.env.ENVIRONMENT;
      delete process.env.POSTGRES_HOST;
      delete process.env.POSTGRES_PORT;
      delete process.env.POSTGRES_USERNAME;
      delete process.env.POSTGRES_PASSWORD;
    });

    it('should return default options for a non-dev environment', async () => {
      process.env.ENVIRONMENT = Environment.dev;
      const testDatabaseName = 'testDatabase';
      const options = await PostgresDatabase.generateDefaultPostgresOptions(
        testDatabaseName,
        EntityTypes.Config
      );
      expect(options).to.deep.equal({
        type: 'postgres',
        subscribers: [SettingLogSubscriber],
        host: 'test-host',
        port: 1234,
        username: 'test-user',
        password: 'test-password',
        schema: 'config',
        entities: ConfigEntities,
        database: testDatabaseName,
      });
    });
  });
});
