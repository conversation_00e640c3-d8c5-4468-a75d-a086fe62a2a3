import {assert, expect} from 'chai';
import {Database} from '../../db/database.ts';

const testType = 'test-database';

class TestDatabase extends Database {
  public getType(): string {
    return testType;
  }
}

describe('Database', () => {
  describe('getType', () => {
    it('result should match expected', () => {
      assert.equal(new TestDatabase().getType(), testType);
    });
  });

  describe('generateDefaultSecretQuery', () => {
    it('should return the correct secret query with no project id', () => {
      // eslint-disable-next-line dot-notation
      const query = Database['generateDefaultSecretQuery']('test-secret-name');
      expect(query).to.deep.equal({
        name: 'test-secret-name',
        projectId: undefined,
      });
    });

    it('should return the correct secret query with project id', () => {
      // eslint-disable-next-line dot-notation
      const query = Database['generateDefaultSecretQuery']('test-secret-name', 'test-project-id');
      expect(query).to.deep.equal({
        name: 'test-secret-name',
        projectId: 'test-project-id',
      });
    });
  });
});
