import {DataSource, DataSourceOptions} from 'typeorm';
// TODO: GCP Secret Manager SDK usage - Data Explorer datasource
import {SecretManagerServiceClient} from '@google-cloud/secret-manager';
import {DataSourceUtils, EntityTypes} from '../../utils/datasource-utils.ts';

const buildDataSource = async () => {
  // get the postgres connection info from the secret manager
  const secretName = process.env.GCP_POSTGRES_SECRET_NAME;

  // TODO: GCP Secret Manager SDK usage - Direct client instantiation and accessSecretVersion call
  const client = new SecretManagerServiceClient();
  const [version] = await client.accessSecretVersion({
    name: secretName,
  });
  if (!version.payload || !version.payload.data) {
    throw new Error('Invalid Secret.');
  }
  const secretString = version.payload.data.toString() ?? '';

  const options = JSON.parse(secretString) as DataSourceOptions;
  if (!options) {
    throw new Error('Unable to parse DataSourceOptions from secret.');
  }

  const newOptionsDb: DataSourceOptions = {
    type: 'postgres',
    database: process.env.CURRENT_TENANT,
    schema: 'data_explorer',
    entities: DataSourceUtils.getEntityListFromEntityType(
      EntityTypes.DataExplorer,
    ),
    migrations: DataSourceUtils.getMigrationListFromEntityType(
      EntityTypes.DataExplorer,
    ),
  };

  const newOptions: DataSourceOptions = Object.assign(
    {},
    options,
    newOptionsDb,
  );

  return new DataSource(newOptions);
};

export default buildDataSource();
