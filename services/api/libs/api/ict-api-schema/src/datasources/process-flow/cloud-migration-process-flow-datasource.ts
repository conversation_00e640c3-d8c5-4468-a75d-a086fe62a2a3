import {DataSource, DataSourceOptions} from 'typeorm';
import 'dotenv/config';
import {DataSourceUtils, EntityTypes} from '../../utils/datasource-utils.ts';

const buildDataSource = async () => {
  // get the postgres connection info from the environment variable
  const secretString = process.env.GCP_POSTGRES_SECRET_NAME;

  if (!secretString) {
    throw new Error('GCP_POSTGRES_SECRET_NAME environment variable is not set');
  }

  let options: DataSourceOptions;
  try {
    options = JSON.parse(secretString) as DataSourceOptions;
  } catch (error) {
    throw new Error(
      'Unable to parse DataSourceOptions from GCP_POSTGRES_SECRET_NAME environment variable',
    );
  }

  if (!options) {
    throw new Error('Unable to parse DataSourceOptions from secret.');
  }

  const newOptionsDb: DataSourceOptions = {
    type: 'postgres',
    database: process.env.CURRENT_TENANT,
    schema: 'process_flow',
    migrations: DataSourceUtils.getMigrationListFromEntityType(
      EntityTypes.ProcessFlow,
    ),
    entities: DataSourceUtils.getEntityListFromEntityType(
      EntityTypes.ProcessFlow,
    ),
  };

  const newOptions: DataSourceOptions = Object.assign(
    {},
    options,
    newOptionsDb,
  );

  return new DataSource(newOptions);
};

export default buildDataSource();
