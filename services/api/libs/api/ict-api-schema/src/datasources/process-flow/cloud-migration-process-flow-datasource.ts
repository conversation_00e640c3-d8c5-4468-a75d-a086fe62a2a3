import {DataSource, DataSourceOptions} from 'typeorm';
// TODO: GCP Secret Manager SDK usage - Process Flow datasource
import {SecretManagerServiceClient} from '@google-cloud/secret-manager';
import 'dotenv/config';
import {DataSourceUtils, EntityTypes} from '../../utils/datasource-utils.ts';

const buildDataSource = async () => {
  // get the postgres connection info from the secret manager
  const secretName = process.env.GCP_POSTGRES_SECRET_NAME;

  // TODO: GCP Secret Manager SDK usage - Direct client instantiation and accessSecretVersion call
  const client = new SecretManagerServiceClient();
  const [version] = await client.accessSecretVersion({
    name: secretName,
  });
  if (!version.payload || !version.payload.data) {
    throw new Error('Invalid Secret.');
  }
  const secretString = version.payload.data.toString() ?? '';

  const options = JSON.parse(secretString) as DataSourceOptions;
  if (!options) {
    throw new Error('Unable to parse DataSourceOptions from secret.');
  }

  const newOptionsDb: DataSourceOptions = {
    type: 'postgres',
    database: process.env.CURRENT_TENANT,
    schema: 'process_flow',
    migrations: DataSourceUtils.getMigrationListFromEntityType(
      EntityTypes.ProcessFlow,
    ),
    entities: DataSourceUtils.getEntityListFromEntityType(
      EntityTypes.ProcessFlow,
    ),
  };

  const newOptions: DataSourceOptions = Object.assign(
    {},
    options,
    newOptionsDb,
  );

  return new DataSource(newOptions);
};

export default buildDataSource();
