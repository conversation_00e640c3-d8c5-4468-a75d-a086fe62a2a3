import {Entity, Column, Index, OneToMany} from 'typeorm';
import {BaseMetricConfigurationEntity} from './base-metric-configuration-entity.ts';
import type {CustomMetricConfigurationEntity} from './custom-metric-configuration-entity.ts';

/**
 * Entity for default metric configurations.
 * This is the base configuration that defines how a metric should be processed.
 * It has a unique metric_config_name and can be referenced by both custom configurations
 * and facility settings.
 *
 * The enabled and active fields are h-stores where:
 * - Keys are facility IDs
 * - Values are booleans indicating if the metric is enabled/active for that facility
 */
@Entity({name: 'default_metric_configurations', schema: 'process_flow'})
@Index('UQ_default_metric_config_name', ['metricConfigName'], {unique: true})
export class DefaultMetricConfigurationEntity extends BaseMetricConfigurationEntity {
  /**
   * Whether this metric is enabled for each facility.
   * Keys are facility IDs, values are booleans.
   * A metric can only be disabled by a configurator via the API.
   */
  @Column({type: 'hstore', nullable: true, name: 'enabled'})
  enabled?: Record<string, boolean>;

  /**
   * Whether this metric is active for each facility.
   * Keys are facility IDs, values are booleans.
   * A metric is active if an incoming fact was matched on it.
   */
  @Column({type: 'hstore', nullable: true, name: 'active'})
  active?: Record<string, boolean>;

  /**
   * Custom configurations that extend this default configuration.
   * Each custom configuration is specific to a facility.
   */
  @OneToMany('CustomMetricConfigurationEntity', 'defaultConfig')
  customConfigs?: CustomMetricConfigurationEntity[];
}
