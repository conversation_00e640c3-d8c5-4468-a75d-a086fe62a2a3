import {Entity, Column, Index, ManyToOne, <PERSON>in<PERSON><PERSON>umn} from 'typeorm';
import {BaseMetricConfigurationEntity} from './base-metric-configuration-entity.ts';
import type {DefaultMetricConfigurationEntity} from './default-metric-configuration-entity.ts';

/**
 * Entity for custom metric configurations.
 * Extends the base metric configuration to add facility-specific overrides.
 * The composite unique index on (metric_config_name, facility_id) ensures each facility
 * can only have one custom configuration per metric configuration.
 */
@Entity({name: 'custom_metric_configurations', schema: 'process_flow'})
@Index('UQ_custom_metric_config_facility', ['metricConfigName', 'facilityId'], {
  unique: true,
})
export class CustomMetricConfigurationEntity extends BaseMetricConfigurationEntity {
  /**
   * The facility ID this configuration applies to.
   * Part of the composite unique index with metric_config_name to ensure
   * each facility can only have one custom configuration per metric.
   */
  @Column({name: 'facility_id', type: 'varchar'})
  facilityId!: string;

  /**
   * Whether this metric is active for this facility.
   * A metric is active if an incoming fact was matched on it.
   */
  @Column({default: false, name: 'active'})
  active!: boolean;

  /**
   * Whether this metric is enabled for this facility.
   * A metric is enabled on the facility level if a user has enabled it.
   */
  @Column({default: true, name: 'enabled'})
  enabled!: boolean;

  /**
   * The default configuration this custom configuration extends.
   * This creates a many-to-one relationship with the default configuration.
   * When the default configuration is deleted, this custom configuration will be deleted as well.
   */
  @ManyToOne('DefaultMetricConfigurationEntity', 'customConfigs', {
    onDelete: 'CASCADE',
  })
  @JoinColumn({
    name: 'metric_config_name',
    referencedColumnName: 'metricConfigName',
  })
  defaultConfig!: DefaultMetricConfigurationEntity;
}
