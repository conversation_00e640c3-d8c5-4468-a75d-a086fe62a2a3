-- ****THIS FILE IS AUTO-GENERATED. DO NOT EDIT IT DIRECTLY******
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_total_locations_available', 'sum_item_values', 'node', 'Number of available locations within the DMS Picking Buffer', 'bin_utilization', 'area_node', '{"aisle_code":"^.+$","area_code":"^MS - Storage$","empty_location_position_count":"^.+$"}'::jsonb, 'total_locations_available', 'multishuttle', 'complex_event_set', '{"identifier":"{aisle_code}","value":"{empty_location_position_count}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_total_locations_occupied', 'sum_item_values', 'node', 'Number of occupied locations within the DMS Picking Buffer', 'bin_utilization', 'area_node', '{"aisle_code":"^.+$","area_code":"^MS - Storage$","empty_location_position_count":"^.+$","total_location_position_count":"^.+$"}'::jsonb, 'total_locations_occupied', 'multishuttle', 'total_storage_locations_occupied', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_aisle_locations_available_count', 'static', 'node', 'Number of available locations within this DMS aisle.', 'bin_utilization', 'area_node', 'Aisle', '{"aisle_code":"^.+$","area_code":"^MS - Storage$","empty_location_position_count":"^.+$"}'::jsonb, 'locations_available', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'storage_location_distribution_available', 'value', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_aisle_locations_occupied_count', 'static', 'node', 'Number of occupied locations within this DMS aisle.', 'bin_utilization', 'area_node', 'Aisle', '{"aisle_code":"^.+$","area_code":"^MS - Storage$","empty_location_position_count":"^.+$","total_location_position_count":"^.+$"}'::jsonb, 'locations_occupied', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'storage_location_distribution_occupied', 'value', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('miniload_aisle_available_count', 'count', 'node', 'Number of available aisles within the Mini-Load Buffer', 'bin_utilization', 'area_node', '{"area_code":"^ML - Storage$","empty_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'aisles_available', 'miniload', 'distinct_item_count', '{"identifier":"{aisle_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('miniload_total_locations_available', 'sum_item_values', 'node', 'Number of available locations per aisle within the Mini-Load Buffer', 'bin_utilization', 'area_node', '{"area_code":"^ML - Storage$","empty_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'total_locations_available', 'miniload', 'complex_event_set', '{"value":"{empty_location_position_count}","identifier":"{aisle_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_total_locations_occupied', 'sum_item_values', 'node', 'Number of occupied locations within the Mini-Load Buffer', 'bin_utilization', 'area_node', '{"area_code":"^ML - Storage$","empty_location_position_count":"^.+$","total_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'total_locations_occupied', 'miniload', 'total_storage_locations_occupied', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_aisle_storage_utilization', 'static', 'node', 'Ratio of occupied locations to total locations within this DMS aisle.', 'bin_utilization', 'area_node', 'Aisle', '{"area_code":"^MS - Storage$","empty_location_position_count":"^.+$","total_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'storage_utilization', '%', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'storage_utilization', 'value', ARRAY['multishuttle', 'MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_aisle_storage_location_distribution_available', 'static', 'node', 'Number of available locations within this DMS aisle.', 'bin_utilization', 'area_node', 'Aisle', '{"area_code":"^MS - Storage$","empty_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'location_distribution_available', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'storage_location_distribution_available', 'value', ARRAY['multishuttle', 'MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_aisle_storage_location_distribution_occupied', 'static', 'node', 'Number of occupied locations within this DMS aisle.', 'bin_utilization', 'area_node', 'Aisle', '{"area_code":"^MS - Storage$","empty_location_position_count":"^.+$","total_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'location_distribution_occupied', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'storage_location_distribution_occupied', 'value', ARRAY['multishuttle', 'MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('miniload_aisle_locations_available_count', 'static', 'node', 'Number of available locations within this Mini-Load aisle', 'bin_utilization', 'area_node', 'Aisle', '{"aisle_code":"^.+$","area_code":"^ML - Storage$","empty_location_position_count":"^.+$"}'::jsonb, 'locations_available', 'MLAI{aisle_code}', ARRAY['miniload'], 'storage_location_distribution_available', 'value', ARRAY['miniload']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('miniload_aisle_locations_occupied', 'static', 'node', 'Number of occupied locations within this Mini-Load aisle.', 'bin_utilization', 'area_node', 'Aisle', '{"area_code":"^ML - Storage$","empty_location_position_count":"^.+$","total_location_position_count":"^.+$","aisle_code":"^.+$"}'::jsonb, 'locations_occupied', 'MLAI{aisle_code}', ARRAY['miniload'], 'storage_location_distribution_occupied', 'value', ARRAY['miniload']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "hu_id", "match_conditions", "outbound_area", "units", "views") 
    VALUES ('receiving_outbound_units_edge', 'outbound-edge', 'Hourly rate of outbound units departing the receiving area based on last 15 minutes.', 'connection_movement', 'handling_unit_code', '{"source_location_code":"RECV-INDUCT","handling_unit_code":"^.+$"}'::jsonb, 'receiving', 'handling_unit', ARRAY['facility', 'multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "hu_id" = EXCLUDED."hu_id",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "units" = EXCLUDED."units",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "hu_id", "inbound_area", "match_conditions", "redis_operation", "views") 
    VALUES ('multishuttle_inbound_totes_edge', 'inbound-edge', 'Hourly rate of Inbound units arriving at the DMS picking buffer based on last 15 minutes.', 'connection_movement', 'area_edge', 'handling_unit_code', 'multishuttle', '{"destination_location_code":"MSAI.*PS","handling_unit_code":"^.+$"}'::jsonb, 'event_set', ARRAY['facility', 'workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "hu_id" = EXCLUDED."hu_id",
      "inbound_area" = EXCLUDED."inbound_area",
      "match_conditions" = EXCLUDED."match_conditions",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "hu_id", "match_conditions", "outbound_area", "units", "views") 
    VALUES ('multishuttle_outbound_totes_edge', 'outbound-edge', 'Hourly rate of outbound units departing the DMS picking buffer based on last 15 minutes.', 'connection_movement', 'handling_unit_code', '{"source_location_code":"MSAI.*DS","handling_unit_code":"^.+$"}'::jsonb, 'multishuttle', 'handling_unit', ARRAY['facility', 'workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "hu_id" = EXCLUDED."hu_id",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "units" = EXCLUDED."units",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "hu_id", "inbound_area", "inbound_parent_nodes", "label", "match_conditions", "name_formula", "redis_operation", "views") 
    VALUES ('multishuttle_aisle_inbound_totes_edge', 'inbound-edge', 'Hourly rate of inbound units arriving at the DMS picking buffer per aisle based on last 15 minutes.', 'connection_movement', 'area_edge', 'handling_unit_code', '{destination_location_code}', ARRAY['multishuttle'], 'Aisle', '{"destination_location_code":"MSAI.*PS","handling_unit_code":"^.+$"}'::jsonb, '{"source":"{destination_location_code}","pattern":"MSAI(?P<aisle>\\d{2}).*","template":"MSAI{aisle}"}'::jsonb, 'event_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "hu_id" = EXCLUDED."hu_id",
      "inbound_area" = EXCLUDED."inbound_area",
      "inbound_parent_nodes" = EXCLUDED."inbound_parent_nodes",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "name_formula" = EXCLUDED."name_formula",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "hu_id", "label", "match_conditions", "name_formula", "outbound_area", "outbound_parent_nodes", "units", "views") 
    VALUES ('multishuttle_aisle_outbound_totes_edge', 'outbound-edge', 'Hourly rate of outbound units departing the DMS picking buffer per aisle based on last 15 minutes.', 'connection_movement', 'handling_unit_code', 'Aisle', '{"source_location_code":"MSAI.*DS","handling_unit_code":"^.+$"}'::jsonb, '{"source":"{source_location_code}","pattern":"MSAI(?P<aisle>\\d{2}).*","template":"MSAI{aisle}"}'::jsonb, '{source_location_code}', ARRAY['multishuttle'], 'handling_unit', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "hu_id" = EXCLUDED."hu_id",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "name_formula" = EXCLUDED."name_formula",
      "outbound_area" = EXCLUDED."outbound_area",
      "outbound_parent_nodes" = EXCLUDED."outbound_parent_nodes",
      "units" = EXCLUDED."units",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "hu_id", "inbound_area", "match_conditions", "redis_operation", "views") 
    VALUES ('workstations_inbound_totes_edge', 'inbound-edge', 'Hourly rate of inbound units arriving at the Workstations area based on last 15 minutes.', 'connection_movement', 'area_edge', 'handling_unit_code', 'workstations', '{"destination_location_code":"M.*GTP.*D1","handling_unit_code":"^.+$"}'::jsonb, 'event_set', ARRAY['facility', 'multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "hu_id" = EXCLUDED."hu_id",
      "inbound_area" = EXCLUDED."inbound_area",
      "match_conditions" = EXCLUDED."match_conditions",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "hu_id", "match_conditions", "outbound_area", "units", "views") 
    VALUES ('workstations_outbound_totes_edge', 'outbound-edge', 'Hourly rate of outbound units departing the Workstations area based on last 15 minutes.', 'connection_movement', 'handling_unit_code', '{"source_location_code":"M.*GTP.*D1","handling_unit_code":"^.+$"}'::jsonb, 'workstations', 'handling_unit', ARRAY['facility', 'multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "hu_id" = EXCLUDED."hu_id",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "units" = EXCLUDED."units",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "hu_id", "inbound_area", "inbound_parent_nodes", "label", "match_conditions", "name_formula", "redis_operation", "views") 
    VALUES ('workstations_workstation_inbound_totes_edge', 'inbound-edge', 'Hourly rate of inbound units arriving at each work station based on last 15 minutes.', 'connection_movement', 'area_edge', 'handling_unit_code', '{destination_location_code}', ARRAY['workstations'], 'Station', '{"destination_location_code":"^M.*GTP.*(B[1-5]|F[1-6])$","handling_unit_code":"^.+$"}'::jsonb, '{"source":"{destination_location_code}","pattern":"(?P<workstation_code>.*)(.{2}$)","template":"{workstation_code}"}'::jsonb, 'event_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "hu_id" = EXCLUDED."hu_id",
      "inbound_area" = EXCLUDED."inbound_area",
      "inbound_parent_nodes" = EXCLUDED."inbound_parent_nodes",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "name_formula" = EXCLUDED."name_formula",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "hu_id", "label", "match_conditions", "name_formula", "outbound_area", "outbound_parent_nodes", "units", "views") 
    VALUES ('workstations_workstation_outbound_totes_edge', 'outbound-edge', 'Hourly rate of outbound units departing each work station based on last 15 minutes.', 'connection_movement', 'handling_unit_code', 'Station', '{"source_location_code":"^M.*GTP.*D1$","handling_unit_code":"^.+$"}'::jsonb, '{"source":"{source_location_code}","pattern":"(?P<workstation_code>.*)(.{2}$)","template":"{workstation_code}"}'::jsonb, '{source_location_code}', ARRAY['workstations'], 'handling_unit', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "hu_id" = EXCLUDED."hu_id",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "name_formula" = EXCLUDED."name_formula",
      "outbound_area" = EXCLUDED."outbound_area",
      "outbound_parent_nodes" = EXCLUDED."outbound_parent_nodes",
      "units" = EXCLUDED."units",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('receiving_stock_time_start', 'count', 'node', 'Total time between a unit being inducted into a stock location and being inducted to the DMS picking buffer.', 'connection_movement', 'area_node', '{"destination_location_code":"RECV-INDUCT","handling_unit_code":"^.+$"}'::jsonb, 'stock_time', 'mins', 'receiving', 'cycle_time_start', '{"instance_id":"{handling_unit_code}"}'::jsonb, '60m_set', ARRAY['facility', 'multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('receiving_stock_time_stop', 'count', 'node', 'Total time between a unit being inducted into a stock location and being inducted to the DMS picking buffer.', 'connection_movement', 'area_node', '{"source_location_code":"RECV-INDUCT","handling_unit_code":"^.+$"}'::jsonb, 'stock_time', 'mins', 'receiving', 'cycle_time_stop', '{"instance_id":"{handling_unit_code}"}'::jsonb, '60m_set', ARRAY['facility', 'multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "hu_id", "inbound_area", "match_conditions", "redis_operation", "views") 
    VALUES ('miniload_inbound_totes_edge', 'inbound-edge', 'Hourly rate of inbound units arriving at the Miniload area based on last 15 minutes.', 'connection_movement', 'area_edge', 'handling_unit_code', 'miniload', '{"destination_location_code":"ML.*I.*","handling_unit_code":"^.+$"}'::jsonb, 'event_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "hu_id" = EXCLUDED."hu_id",
      "inbound_area" = EXCLUDED."inbound_area",
      "match_conditions" = EXCLUDED."match_conditions",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "hu_id", "match_conditions", "outbound_area", "units", "views") 
    VALUES ('miniload_outbound_totes_edge', 'outbound-edge', 'Hourly rate of outbound units departing the Miniload area based on last 15 minutes.', 'connection_movement', 'handling_unit_code', '{"source_location_code":"ML.*O.*","handling_unit_code":"^.+$"}'::jsonb, 'miniload', 'handling_unit', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "hu_id" = EXCLUDED."hu_id",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "units" = EXCLUDED."units",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_active_devices_lift', 'count', 'node', 'Number of active lift devices within the DMS picking buffer.', 'fault_event', 'area_node', '{"availability_status":"^AU$","device_id_code":"^MSAI.*E[RL].*LO.*$"}'::jsonb, 'active_lift_devices', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_active_devices_shuttle', 'count', 'node', 'Number of active shuttle devices within the DMS picking buffer.', 'fault_event', 'area_node', '{"availability_status":"^AU$","device_id_code":"^MSAI.*LV.*SH.*$"}'::jsonb, 'active_shuttle_devices', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('active_devices', 'count', 'node', 'Number of active devices per aisle within the DMS Picking Buffer.', 'fault_event', 'area_node', 'Aisle', '{"aisle_code":"^.+$","or_condition":[{"availability_status":"^AU$"},{"availability_status":"^Automatic Mode$"}]}'::jsonb, 'active_devices', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['facility', 'MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_num_distinct_aisles', 'count', 'node', 'Number of aisles within the DMS picking buffer', 'multishuttle_movement', 'area_node', '{"aisle_code":"^.+$"}'::jsonb, 'num_aisles', 'multishuttle', 'distinct_item_count', '{"identifier":"{aisle_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_num_distinct_shuttles', 'count', 'node', 'Number of shuttles within the DMS picking buffer', 'multishuttle_movement', 'area_node', '{"shuttle_code":"^.+$"}'::jsonb, 'num_shuttles', 'multishuttle', 'distinct_item_count', '{"identifier":"{shuttle_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_num_distinct_lifts', 'count', 'node', 'Number of lifts within the DMS picking buffer', 'multishuttle_movement', 'area_node', '{"lift_code":"^.+$"}'::jsonb, 'num_lifts', 'multishuttle', 'distinct_item_count', '{"identifier":"{lift_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_total_movements', 'count', 'node', 'Total movements within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"^.+$"}'::jsonb, 'total_movements', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_movements_per_fault_numerator', 'ratio', 'node', 'Total movements per fault within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"^(ByPass|Retrieval|Shuffle|Storage|IAT)$"}'::jsonb, 'movements_per_fault_numerator', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_movement_retrieval_rate', 'hourly_rate', 'node', 'Hourly rate of retrieval movements in the DMS picking buffer based on the last 15 minutes.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"Retrieval"}'::jsonb, 'retrieval_rate', '/hr', 'multishuttle', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_movement_total_retrieval_movements', 'count', 'node', 'Total retrieval movements within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"Retrieval"}'::jsonb, 'total_retrieval_movements', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_storage_movement_rate', 'hourly_rate', 'node', 'Hourly rate of storage movements in the DMS picking buffer based on the last 15 minutes.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"Storage"}'::jsonb, 'storage_rate', '/hr', 'multishuttle', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_total_storage_movements', 'count', 'node', 'Total storage movements within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"Storage"}'::jsonb, 'total_storage_movements', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_total_shuffle_movements', 'count', 'node', 'Total shuffle movements within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"Shuffle"}'::jsonb, 'total_shuffle_movements', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_shuffle_movement_rate', 'hourly_rate', 'node', 'Hourly rate of shuffle movements in the DMS picking buffer based on the last 15 minutes.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"Shuffle"}'::jsonb, 'shuffle_rate', '/hr', 'multishuttle', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_total_bypass_movements', 'count', 'node', 'Total bypass movements within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"ByPass"}'::jsonb, 'total_bypass_movements', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_bypass_movement_rate', 'hourly_rate', 'node', 'Hourly rate of bypass movements in the DMS picking buffer based on the last 15 minutes.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"ByPass"}'::jsonb, 'bypass_rate', '/hr', 'multishuttle', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_total_iat_movements', 'count', 'node', 'Total intra aisle transfer movements within the DMS picking buffer in the last hour.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"IAT"}'::jsonb, 'total_iat_movements', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('multishuttle_iat_movement_rate', 'hourly_rate', 'node', 'Hourly rate of intra-aisle transfer movements in the DMS picking buffer based on the last 15 minutes.', 'multishuttle_movement', 'area_node', '{"movement_type_code":"IAT"}'::jsonb, 'iat_rate', '/hr', 'multishuttle', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_num_distinct_lifts', 'count', 'node', 'Number of lifts within this DMS aisle', 'multishuttle_movement', 'area_node', 'Aisle', '{"aisle_code":"^.+$","lift_code":"^.+$"}'::jsonb, 'num_lifts', 'MSAI{aisle_code}', 'distinct_item_count', '{"identifier":"{lift_code}"}'::jsonb, '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_num_distinct_shuttles', 'count', 'node', 'Number of shuttles within this DMS aisle', 'multishuttle_movement', 'area_node', 'Aisle', '{"aisle_code":"^.+$","shuttle_code":"^.+$"}'::jsonb, 'num_shuttles', 'MSAI{aisle_code}', 'distinct_item_count', '{"identifier":"{shuttle_code}"}'::jsonb, '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('aisle_total_movements', 'count', 'node', 'Total movements within this DMS aisle in the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"^Storage|Retrieval|Shuffle|ByPass|IAT$"}'::jsonb, 'total_movements', 'MSAI{aisle_code}', 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('aisle_total_storage_movements', 'count', 'node', 'Total storage movements within this DMS aisle in the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"Storage"}'::jsonb, 'total_storage_movements', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_storage_rate', 'hourly_rate', 'node', 'Hourly rate of storage movements within this DMS aisle based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"Storage"}'::jsonb, 'storage_rate', '/hr', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('aisle_movement_total_retrieval_movements', 'count', 'node', 'Total retrieval movements within this DMS aisle in the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"Retrieval"}'::jsonb, 'total_retrieval_movements', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_retrieval_rate', 'hourly_rate', 'node', 'Hourly rate of retrieval movements within this DMS aisle based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"Retrieval"}'::jsonb, 'retrieval_rate', '/hr', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('aisle_total_shuffle_movements', 'count', 'node', 'Total shuffle movements within this DMS aisle in the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"Shuffle"}'::jsonb, 'total_shuffle_movements', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_shuffle_rate', 'hourly_rate', 'node', 'Hourly rate of shuffle movements within this DMS aisle based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"Shuffle"}'::jsonb, 'shuffle_rate', '/hr', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('aisle_total_bypass_movements', 'count', 'node', 'Total bypass movements within this DMS aisle in the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"ByPass"}'::jsonb, 'total_bypass_movements', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_bypass_rate', 'hourly_rate', 'node', 'Hourly rate of bypass movements within this DMS aisle based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"ByPass"}'::jsonb, 'bypass_rate', '/hr', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('aisle_total_iat_movements', 'count', 'node', 'Total intra aisle transfer movements within this DMS aisle in the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"IAT"}'::jsonb, 'total_iat_movements', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('aisle_iat_rate', 'hourly_rate', 'node', 'Hourly rate of intra-aisle transfer movements within this DMS aisle based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"IAT"}'::jsonb, 'iat_rate', '/hr', 'MSAI{aisle_code}', ARRAY['multishuttle'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('aisle_movements_per_fault_numerator', 'ratio', 'node', 'Total lift movements per fault within this DMS aisle over the last hour.', 'multishuttle_movement', 'area_node', 'Aisle', '{"movement_type_code":"^Storage|Retrieval|Shuffle|ByPass|IAT$"}'::jsonb, 'movements_per_fault_numerator', 'MSAI{aisle_code}', 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_total_shuttle_movements', 'count', 'node', 'Total movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","shuttle_code":"^.+$"}'::jsonb, 'total_shuttle_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_total_storage_movements', 'count', 'node', 'Total storage movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Storage$","shuttle_code":"^.+$"}'::jsonb, 'total_shuttle_storage_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_inventory_totes_storage_movements', 'count', 'node', 'Total inventory tote storage movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Storage$","shuttle_code":"^.+$","sku_code":"^.+$"}'::jsonb, 'inventory_totes_shuttle_storage_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_empty_totes_storage_movements', 'count', 'node', 'Total empty tote storage movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Storage$","shuttle_code":"^.+$","sku_code":"^(null|)$"}'::jsonb, 'empty_totes_shuttle_storage_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_total_retrieval_movements', 'count', 'node', 'Total retrieval movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Retrieval$","shuttle_code":"^.+$"}'::jsonb, 'total_shuttle_retrieval_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_inventory_totes_retrieval_movements', 'count', 'node', 'Total inventory tote retrieval movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Retrieval$","shuttle_code":"^.+$","sku_code":"^.+$"}'::jsonb, 'inventory_totes_shuttle_retrieval_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_empty_totes_retrieval_movements', 'count', 'node', 'Total empty tote retrieval movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Retrieval$","shuttle_code":"^.+$","sku_code":"^(null|)$"}'::jsonb, 'empty_totes_shuttle_retrieval_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_total_shuffle_movements', 'count', 'node', 'Total shuffle movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Shuffle$","shuttle_code":"^.+$"}'::jsonb, 'total_shuttle_shuffle_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_inventory_totes_shuffle_movements', 'count', 'node', 'Total inventory tote shuffle movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Shuffle$","shuttle_code":"^.+$","sku_code":"^.+$"}'::jsonb, 'inventory_totes_shuttle_shuffle_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('shuttle_empty_totes_shuffle_movements', 'count', 'node', 'Total empty tote shuffle movements made by this shuttle in the last hour.', 'multishuttle_movement', 'shuttle_node', 'Shuttle', '{"aisle_code":"^.+$","movement_type_code":"^Shuffle$","shuttle_code":"^.+$","sku_code":"^(null|)$"}'::jsonb, 'empty_totes_shuttle_shuffle_movements', '{shuttle_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "inbound_area", "label", "match_conditions", "outbound_area", "outbound_node_label", "redis_operation", "views") 
    VALUES ('edge_from_lift_to_aisle', 'complete-edge', 'Hourly rate of storage movements from lift to aisle based on the last 15 minutes.', 'multishuttle_movement', 'area_edge', 'MSAI{aisle_code}', 'Aisle', '{"aisle_code":"^.+$","handling_unit_code":"^.+$","lift_code":"^MSAI.*ER.*LO01$","movement_type_code":"^Storage$"}'::jsonb, '{lift_code}', 'Lift', 'event_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "inbound_area" = EXCLUDED."inbound_area",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "outbound_node_label" = EXCLUDED."outbound_node_label",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "inbound_area", "label", "match_conditions", "outbound_area", "outbound_node_label", "redis_operation", "views") 
    VALUES ('edge_from_aisle_to_lift', 'complete-edge', 'Hourly rate of retrieval movements from aisle to lift based on the last 15 minutes.', 'multishuttle_movement', 'area_edge', '{lift_code}', 'Lift', '{"aisle_code":"^.+$","handling_unit_code":"^.+$","lift_code":"^MSAI.*EL.*LO01$","movement_type_code":"^Retrieval$"}'::jsonb, 'MSAI{aisle_code}', 'Aisle', 'event_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "inbound_area" = EXCLUDED."inbound_area",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "outbound_node_label" = EXCLUDED."outbound_node_label",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('lift_total_movements', 'count', 'node', 'Total movements made by this lift in the last hour.', 'multishuttle_movement', 'area_node', 'Lift', '{"aisle_code":"^.+$","lift_code":"^.+$"}'::jsonb, 'total_movements', '{lift_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('lift_total_storage_movements', 'count', 'node', 'Total storage movements made by this lift in the last hour.', 'multishuttle_movement', 'area_node', 'Lift', '{"aisle_code":"^.+$","lift_code":"^.+$","movement_type_code":"^Storage$"}'::jsonb, 'total_storage_movements', '{lift_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('lift_total_retrieval_movements', 'count', 'node', 'Total retrieval movements made by this lift in the last hour.', 'multishuttle_movement', 'area_node', 'Lift', '{"aisle_code":"^.+$","lift_code":"^.+$","movement_type_code":"^Retrieval$"}'::jsonb, 'total_retrieval_movements', '{lift_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('lift_retrieval_rate', 'hourly_rate', 'node', 'Hourly rate of retrieval movements made by this lift based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Lift', '{"aisle_code":"^.+$","lift_code":"^.+$","movement_type_code":"^Retrieval$"}'::jsonb, 'retrieval_rate', '/hr', '{lift_code}', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('lift_total_shuffle_movements', 'count', 'node', 'Total shuffle movements made by this lift in the last hour.', 'multishuttle_movement', 'area_node', 'Lift', '{"aisle_code":"^.+$","lift_code":"^.+$","movement_type_code":"^Shuffle$"}'::jsonb, 'total_shuffle_movements', '{lift_code}', 'event_set', '60m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('lift_storage_movement_rate', 'hourly_rate', 'node', 'Hourly rate of storage movements made by this lift based on the last 15 minutes.', 'multishuttle_movement', 'area_node', 'Lift', '{"aisle_code":"^.+$","lift_code":"^.+$","movement_type_code":"^Storage$"}'::jsonb, 'storage_rate', '/hr', '{lift_code}', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['MSAI{aisle_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_arrivals_per_fault_denominator', 'ratio', 'node', 'Total arrivals per fault across all stations in the last hour.', 'nok_interval', 'area_node', '{"equipment_code":".*GTP-\\d{2}.*"}'::jsonb, 'arrivals_per_fault_denominator', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "name_formula", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_arrivals_per_fault_denominator', 'ratio', 'node', 'Total arrivals per fault in this station in the last hour.', 'nok_interval', 'area_node', 'Station', '{"equipment_code":".*GTP-\\d{2}.*"}'::jsonb, 'arrivals_per_fault_denominator', '{"source":"{equipment_code}","pattern":".*(?P<gtp_part>GTP-\\d{2}).*","template":"M11-{gtp_part}"}'::jsonb, ARRAY['workstations'], 'event_set', '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "name_formula" = EXCLUDED."name_formula",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('multishuttle_movements_per_fault_denominator', 'ratio', 'node', 'Total movements per fault in the DMS picking buffer in the last hour.', 'nok_interval', 'area_node', '{"equipment_code":"^MSAI.*$"}'::jsonb, 'movements_per_fault_denominator', 'multishuttle', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "name_formula", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('aisle_movements_per_fault_denominator', 'ratio', 'node', 'Total movements per fault in this DMS aisle in the last hour.', 'nok_interval', 'area_node', 'Aisle', '{"equipment_code":"^MSAI.*(?:E[RL].*LO01|LV.*SH01)$"}'::jsonb, 'movements_per_fault_denominator', '{"source":"{equipment_code}","pattern":"(?P<aisle_code>MSAI\\d{2}).*","template":"{aisle_code}"}'::jsonb, ARRAY['multishuttle'], 'event_set', '60m_set', ARRAY['multishuttle']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "name_formula" = EXCLUDED."name_formula",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_cycle_count', 'count', 'node', 'Total cycle counts across all stations in the last hour.', 'pick', 'area_node', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"CycleCount"}'::jsonb, 'cycle_count', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_cycle_count_rate', 'hourly_rate', 'node', 'Hourly rate of cycle counts across all stations based on the last 15 minutes.', 'pick', 'area_node', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"CycleCount"}'::jsonb, 'cycle_count_rate', '/hr', 'workstations', 'event_set', '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_orders_picked_rate', 'hourly_rate', 'node', 'Orders picked hourly rate based on the last 15 minutes.', 'pick', 'area_node', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"GTPPrint"}'::jsonb, 'orders_picked_rate', '/hr', 'workstations', 'event_set', '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "description", "config_type", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_orders_picked_count', 'count', 'Total orders picked in the last hour.', 'node', 'pick', 'area_node', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"GTPPrint"}'::jsonb, 'orders_picked_count', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "description" = EXCLUDED."description",
      "config_type" = EXCLUDED."config_type",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_order_lines_picked_rate', 'hourly_rate', 'node', 'Hourly rate of order lines picked across all stations based on the last 15 minutes.', 'pick', 'area_node', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"PICK"}'::jsonb, 'order_lines_picked_rate', '/hr', 'workstations', 'event_set', '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "graph_operation", "fact_type", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_order_lines_picked_count', 'count', 'node', 'Total order lines picked across all stations in the last hour.', 'area_node', 'pick', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"PICK"}'::jsonb, 'order_lines_picked_count', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "graph_operation" = EXCLUDED."graph_operation",
      "fact_type" = EXCLUDED."fact_type",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_order_quantity_picked', 'sum', 'node', 'Total order quantity picked across all stations in the last hour.', 'pick', 'area_node', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"PICK"}'::jsonb, 'order_quantity_picked', 'workstations', 'event_set', '{"value":"{picked_qty}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_order_lines_picked_rate', 'hourly_rate', 'node', 'Hourly rate of order lines picked in this station based on the last 15 minutes.', 'pick', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"PICK","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'order_lines_picked_rate', 'mins', '{workstation_code}', ARRAY['workstations'], 'event_set', '15m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_order_lines_picked_count', 'count', 'node', 'Total order lines picked in this station in the last hour.', 'pick', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"PICK","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'order_lines_picked', '{workstation_code}', ARRAY['workstations'], 'event_set', '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_cycle_counts', 'count', 'node', 'Total cycle counts in this station in the last hour.', 'pick', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"CycleCount","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'cycle_counts', '{workstation_code}', ARRAY['workstations'], 'event_set', '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstation_cycle_count_rate', 'hourly_rate', 'node', 'Hourly rate of cycle counts in this station based on the last 15 minutes.', 'pick', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"CycleCount","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'cycle_count_rate', '/hr', '{workstation_code}', 'event_set', '15m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_order_lines_picked_by_destination', 'count', 'node', 'Total order lines picked across all destination locations in this station in the last hour.', 'pick', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workflow_code":"PICK","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'order_lines_picked_{induction_zone_code}', '{workstation_code}', ARRAY['workstations'], 'event_set', '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('destination_location_order_lines_picked_count', 'count', 'node', 'Total order lines picked in this destination location in the last hour.', 'pick', 'area_node', 'StationLocation', '{"induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6])$","workflow_code":"PICK","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'picked_order_lines', '{induction_zone_code}', ARRAY['{workstation_code}'], 'event_set', '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_source_container_arrival_count', 'count', 'node', 'Total source container arrivals across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'source_container_arrival_count', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_source_container_arrival_rate', 'hourly_rate', 'node', 'Hourly rate of source container arrivals across all workstations based on the last 15 minutes.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'source_container_arrival_rate', '/hr', 'workstations', 'event_set', '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_destination_container_arrival_count', 'count', 'node', 'Total destination container arrivals across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$"}'::jsonb, 'destination_container_arrival_count', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_destination_container_arrival_rate', 'hourly_rate', 'node', 'Hourly rate of destination container arrivals across all workstations based on the last 15 minutes.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$"}'::jsonb, 'destination_container_arrival_rate', '/hr', 'workstations', 'event_set', '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('workstations_arrivals_per_fault_numerator', 'ratio', 'node', 'Total arrivals per fault across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6]|D1)$"}'::jsonb, 'arrivals_per_fault_numerator', 'workstations', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_active_operators_increment', 'count', 'node', 'Total active operators across all workstations in the last hour.', 'pick_activity', 'area_node', '{"workstation_code":"-GTP-","event_code":"^Logon$"}'::jsonb, 'active_operators', 'workstations', 'distinct_item_count', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_active_operators_decrement', 'count', 'node', 'Total active operators across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Logoff$"}'::jsonb, 'active_operators', 'workstations', 'distinct_item_subtract', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_occupied_source_locations_increment', 'count', 'node', 'Total occupied source locations across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'occupied_source_locations', 'workstations', 'distinct_item_count', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_occupied_source_locations_decrement', 'count', 'node', 'Total occupied source locations across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'occupied_source_locations', 'workstations', 'distinct_item_subtract', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_occupied_destination_locations_increment', 'count', 'node', 'Total occupied destination locations across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$"}'::jsonb, 'occupied_destination_locations', 'workstations', 'distinct_item_count', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_occupied_destination_locations_decrement', 'count', 'node', 'Total occupied destination locations across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_destination_locations', 'workstations', 'distinct_item_subtract', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_logged_in_time_start', 'sum', 'node', 'Total time operators are logged in across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Logon$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'logged_in_time', 'mins', 'workstations', 'cycle_time_start', '{"instance_id":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_logged_in_time_stop', 'sum', 'node', 'Total time operators are logged in across all workstations in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Logoff$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'logged_in_time', 'mins', 'workstations', 'cycle_time_stop', '{"instance_id":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_occupied_time_start', 'sum', 'node', 'Total time one or more source destinations at a workstation are occupied in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'occupied_time', 'mins', 'workstations', 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_occupied_time_stop', 'sum', 'node', 'Total time one or more source destinations at a workstation are occupied in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(Departure|Release)$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'occupied_time', 'mins', 'workstations', 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_active_time_start', 'sum', 'node', 'Total time one or more source destinations at a workstation are occupied while an operator is logged in or a terminal is active in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$","operator_code":"^.+$"}'::jsonb, 'active_time', 'mins', 'workstations', 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_active_time_stop', 'sum', 'node', 'Total time one or more source destinations at a workstation are occupied while an operator is logged in or a terminal is active in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(Release|Departure|Logoff)$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'active_time', 'mins', 'workstations', 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_idle_time_start', 'sum', 'node', 'Total time an operator is logged in to a workstation or a terminal is active and no source destinations are occupied in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(Departure|Release)$","operator_code":"^.+$","induction_zone_code":"^M.*GTP.*(D1|(B[1-5]|F[1-6]))$"}'::jsonb, 'idle_time', 'mins', 'workstations', 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_idle_time_stop', 'sum', 'node', 'Total time an operator is logged in to a workstation or a terminal is active and no source destinations are occupied in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(Arrival|Logoff)$","induction_zone_code":"^M.*GTP.*(D1|(B[1-5]|F[1-6]))$"}'::jsonb, 'idle_time', 'mins', 'workstations', 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_starved_time_start', 'sum', 'node', 'Total time an operator is present or logged in to a workstation when no Source Container is present in the last hour.', 'pick_activity', 'area_node', '{"handling_unit_code":"^None$","operator_code":"^.+$"}'::jsonb, 'starved_time', 'mins', 'workstations', 'cycle_time_start', '{"instance_id":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_starved_time_stop', 'sum', 'node', 'Total time an operator is present or logged in to a workstation when no Source Container is present in the last hour.', 'pick_activity', 'area_node', '{"or_condition":[{"handling_unit_code":"^.+$"},{"event_code":"^Logoff$"}]}'::jsonb, 'starved_time', 'mins', 'workstations', 'cycle_time_stop', '{"instance_id":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_blocked_time_start', 'sum', 'node', 'Total time a source load unit is present between "Release" and "Departure" events and an operator is logged in to a workstation in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^Release$","induction_zone_code":"^M.*GTP.*D1$","operator_code":"^.+$"}'::jsonb, 'blocked_time', 'mins', 'workstations', 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstations_blocked_time_stop', 'sum', 'node', 'Total time a source load unit is present between "Release" and "Departure" events and an operator is logged in to a workstation in the last hour.', 'pick_activity', 'area_node', '{"event_code":"^(Logoff|Departure)$","induction_zone_code":"^M.*GTP.*D1$"}'::jsonb, 'blocked_time', 'mins', 'workstations', 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_source_container_arrival_rate', 'hourly_rate', 'node', 'Hourly rate of source container arrivals in this station based on the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'source_container_arrival_rate', '/hr', '{workstation_code}', ARRAY['workstations'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_source_container_arrival_count', 'count', 'node', 'Total source container arrivals in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'source_container_arrivals', '{workstation_code}', ARRAY['workstations'], 'event_set', '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_occupied_source_locations_increment', 'count', 'node', 'Total occupied source locations in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*D1$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_source_locations', '{workstation_code}', ARRAY['workstations'], 'distinct_item_count', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_occupied_source_locations_decrement', 'count', 'node', 'Total occupied source locations in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^M.*GTP.*D1$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_source_locations', '{workstation_code}', ARRAY['workstations'], 'distinct_item_subtract', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_occupied_destination_locations_increment', 'count', 'node', 'Total occupied destination locations in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^Arrival$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_destination_locations', '{workstation_code}', ARRAY['workstations'], 'distinct_item_count', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_occupied_destination_locations_decrement', 'count', 'node', 'Total occupied destination locations in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_destination_locations', '{workstation_code}', ARRAY['workstations'], 'distinct_item_subtract', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_active_operators_increment', 'count', 'node', 'Total active operators in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^Logon$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'active_operators', '{workstation_code}', ARRAY['workstations'], 'distinct_item_count', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_active_operators_decrement', 'count', 'node', 'Total active operators in this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"event_code":"^Logoff$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'active_operators', '{workstation_code}', ARRAY['workstations'], 'distinct_item_subtract', '{"identifier":"{workstation_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('workstation_destination_position_pick_lines_ratio_numerator', 'destination_position_ratio', 'node', 'Ratio of pick order lines completed to destination containers in each of the available positions at this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","zone_code":"^.+$","workflow_code":"PICK","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, '{induction_zone_code}_destination_position_ratio', '{workstation_code}', ARRAY['workstations'], 'event_set', '{"denominator_node":"{zone_code}"}'::jsonb, '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('workstation_destination_position_pick_lines_ratio_denominator', 'destination_position_ratio', 'node', 'Ratio of pick order lines completed to destination containers in each of the available positions at this station in the last hour.', 'pick_activity', 'area_node', 'Station', '{"induction_zone_code":"^M.*GTP.*(B[1-5]|F[1-6])$","zone_code":"^.+$","workflow_code":"PICK","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'destination_position_ratio_denominator', '{workstation_code}', ARRAY['workstations'], 'event_set', '60m_set', ARRAY['workstations']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "config_type", "description", "fact_type", "graph_operation", "inbound_area", "inbound_parent_nodes", "label", "match_conditions", "outbound_area", "outbound_node_label", "outbound_parent_nodes", "redis_operation", "views") 
    VALUES ('edge_from_workstation_source_location_to_destination_location', 'complete-edge', 'Hourly rate of units moving from the station source location to the destination location based on the last 15 minutes.', 'pick_activity', 'area_edge', '{induction_zone_code}', ARRAY['{workstation_code}'], 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6])$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, '{workstation_code}D1', 'StationLocation', ARRAY['{workstation_code}'], 'event_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "inbound_area" = EXCLUDED."inbound_area",
      "inbound_parent_nodes" = EXCLUDED."inbound_parent_nodes",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "outbound_area" = EXCLUDED."outbound_area",
      "outbound_node_label" = EXCLUDED."outbound_node_label",
      "outbound_parent_nodes" = EXCLUDED."outbound_parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_occupied', 'static', 'node', 'Whether this location is occupied or unoccupied.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_location', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":"Yes"}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_unoccupied', 'static', 'node', 'Whether this location is occupied or unoccupied.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_location', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":"No"}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('destination_location_arrival_rate', 'hourly_rate', 'node', 'Hourly rate of units arriving at this location based on the last 15 minutes.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6])$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'destination_container_arrival_rate', '/hr', '{induction_zone_code}', ARRAY['{workstation_code}'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('destination_location_arrival_count', 'count', 'node', 'Total units arriving at this location in the last hour.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6])$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'destination_container_arrivals', '{induction_zone_code}', ARRAY['{workstation_code}'], 'event_set', '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_active_operator', 'static', 'node', 'Whether or not an operator is active at this location''s station.', 'pick_activity', 'area_node', 'StationLocation', '{"operator_code":"^.+$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'active_operator', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":"Yes"}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_container_id', 'static', 'node', 'The ID of the container that is currently at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$","handling_unit_code":"^.+$"}'::jsonb, 'container_id', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":"{handling_unit_code}"}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_container_id_remove', 'static', 'node', 'The ID of the container that is currently at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'container_id', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":""}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_inactive_operator', 'static', 'node', 'Whether or not an operator is active at this location''s station.', 'pick_activity', 'area_node', 'StationLocation', '{"operator_code":"^None$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'active_operator', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":"No"}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_load_unit_type', 'static', 'node', 'The type of load unit (carton, pallet, tote, etc.) that is currently at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","handling_unit_type":"^.+$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'load_unit_type', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":"{handling_unit_type}"}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_load_unit_type_remove', 'static', 'node', 'The type of load unit (carton, pallet, tote, etc.) that is currently at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^(?!Arrival$).+$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'load_unit_type', '{induction_zone_code}', ARRAY['{workstation_code}'], 'store_static_value', '{"value":""}'::jsonb, 'value', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_occupied_time_start', 'sum', 'node', 'Total time this location is occupied.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_time', 'mins', '{induction_zone_code}', ARRAY['{workstation_code}'], 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_occupied_time_stop', 'sum', 'node', 'Total time this location is occupied.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^(Release|Departure)$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'occupied_time', 'mins', '{induction_zone_code}', ARRAY['{workstation_code}'], 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_active_time_start', 'sum', 'node', 'Total time an operator is active at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","operator_code":"^.+$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'active_time', 'mins', '{induction_zone_code}', ARRAY['{workstation_code}'], 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_active_time_stop', 'sum', 'node', 'Total time an operator is active at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"or_condition":[{"operator_code":"^None$"},{"event_code":"^(Release|Departure)$"}],"induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'active_time', 'mins', '{induction_zone_code}', ARRAY['{workstation_code}'], 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_idle_time_start', 'sum', 'node', 'Total time an operator is idle at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^(Departure|Release)$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","operator_code":"^.+$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'idle_time', 'mins', '{induction_zone_code}', ARRAY['{workstation_code}'], 'cycle_time_start', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('station_location_idle_time_stop', 'sum', 'node', 'Total time an operator is idle at this location.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6]|D1)$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'idle_time', 'mins', '{induction_zone_code}', ARRAY['{workstation_code}'], 'cycle_time_stop', '{"instance_id":"{induction_zone_code}"}'::jsonb, '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('destination_location_destination_position_ratio_numerator', 'destination_position_ratio', 'node', 'Ratio of pick order lines completed to destination containers in each of the available positions at this station in the last hour.', 'pick_activity', 'area_node', 'StationLocation', '{"induction_zone_code":"^{workstation_code}(B[1-5]|F[1-6])$","zone_code":"^.+$","workflow_code":"PICK","workstation_code":"^M.*-GTP-\\d{2}$"}'::jsonb, 'destination_position_ratio', '{induction_zone_code}', ARRAY['{workstation_code}'], 'event_set', '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "metric_units", "node_name", "parent_nodes", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('source_location_arrival_rate', 'hourly_rate', 'node', 'Hourly rate of units arriving at this source location based on the last 15 minutes.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}D1$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'source_container_arrival_rate', '/hr', '{induction_zone_code}', ARRAY['{workstation_code}'], 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "label", "match_conditions", "metric_type", "node_name", "parent_nodes", "redis_operation", "time_window", "views") 
    VALUES ('source_location_arrival_count', 'count', 'node', 'Total units arriving at this source location in the last hour.', 'pick_activity', 'area_node', 'StationLocation', '{"event_code":"^Arrival$","induction_zone_code":"^{workstation_code}D1$","workstation_code":"^M.*GTP-\\d{2}$"}'::jsonb, 'source_container_arrivals', '{induction_zone_code}', ARRAY['{workstation_code}'], 'event_set', '60m_set', ARRAY['{workstation_code}']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "label" = EXCLUDED."label",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "parent_nodes" = EXCLUDED."parent_nodes",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('miniload_num_miniloads_available', 'count', 'node', 'Number of available miniloads', 'vehicle_movement', 'area_node', '{"equipment_code":"^ML.*"}'::jsonb, 'miniloads_available', 'miniload', 'distinct_item_count', '{"identifier":"{equipment_code}"}'::jsonb, '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_aisle_total_movements', 'count', 'node', 'Total movements across all aisles within the Mini-Load buffer in the last hour.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$"}'::jsonb, 'aisle_total_movements', 'miniload', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_aisle_storage_movements', 'count', 'node', 'Total storage movements across all aisles within the Mini-Load buffer in the last hour.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$","movement_type_code":"^Storage$"}'::jsonb, 'aisle_storage_movements', 'miniload', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_aisle_retrieval_movements', 'count', 'node', 'Total retrieval movements across all aisles within the Mini-Load buffer in the last hour.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$","movement_type_code":"^Retrieval$"}'::jsonb, 'aisle_retrieval_movements', 'miniload', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_aisle_bypass_movements', 'count', 'node', 'Total bypass movements across all aisles within the Mini-Load buffer in the last hour.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$","movement_type_code":"^Bypass$"}'::jsonb, 'aisle_bypass_movements', 'miniload', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('miniload_storage_rate', 'hourly_rate', 'node', 'Hourly rate of storage movements across all aisles in the Mini-Load buffer based on the last 15 minutes.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$","movement_type_code":"^Storage$"}'::jsonb, 'storage_rate', '/hr', 'miniload', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('miniload_retrieval_rate', 'hourly_rate', 'node', 'Hourly rate of retrieval movements across all aisles in the Mini-Load buffer based on the last 15 minutes.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$","movement_type_code":"^Retrieval$"}'::jsonb, 'retrieval_rate', '/hr', 'miniload', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "metric_units", "node_name", "redis_operation", "redis_params", "time_window", "views") 
    VALUES ('miniload_bypass_rate', 'hourly_rate', 'node', 'Hourly rate of bypass movements across all aisles in the Mini-Load buffer based on the last 15 minutes.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$","movement_type_code":"^Bypass$"}'::jsonb, 'bypass_rate', '/hr', 'miniload', 'event_set', '{"ttl":900}'::jsonb, '15m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "metric_units" = EXCLUDED."metric_units",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "redis_params" = EXCLUDED."redis_params",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_movements_per_fault_numerator', 'ratio', 'node', 'Total movements per fault across all aisles within the Mini-Load buffer in the last hour.', 'vehicle_movement', 'area_node', '{"or_condition":[{"source_location_code":"^ML.*"},{"destination_location_code":"^ML.*"}],"movement_status_code":"^OK$"}'::jsonb, 'movements_per_fault_numerator', 'miniload', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;
INSERT INTO "process_flow"."default_metric_configurations" ("metric_config_name", "aggregation", "config_type", "description", "fact_type", "graph_operation", "match_conditions", "metric_type", "node_name", "redis_operation", "time_window", "views") 
    VALUES ('miniload_movements_per_fault_denominator', 'ratio', 'node', 'Total movements per fault across all aisles within the Mini-Load buffer in the last hour.', 'viz_event_log', 'area_node', '{"equipment_code":"^ML.+$","or_condition":[{"area":".*ML.*"},{"area":".*Miniload.*"}]}'::jsonb, 'movements_per_fault_denominator', 'miniload', 'event_set', '60m_set', ARRAY['facility']) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
      "metric_config_name" = EXCLUDED."metric_config_name",
      "aggregation" = EXCLUDED."aggregation",
      "config_type" = EXCLUDED."config_type",
      "description" = EXCLUDED."description",
      "fact_type" = EXCLUDED."fact_type",
      "graph_operation" = EXCLUDED."graph_operation",
      "match_conditions" = EXCLUDED."match_conditions",
      "metric_type" = EXCLUDED."metric_type",
      "node_name" = EXCLUDED."node_name",
      "redis_operation" = EXCLUDED."redis_operation",
      "time_window" = EXCLUDED."time_window",
      "views" = EXCLUDED."views"
    ;