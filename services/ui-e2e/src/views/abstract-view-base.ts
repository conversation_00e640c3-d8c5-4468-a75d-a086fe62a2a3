import { Page, Locator, Response } from '@playwright/test';
import { Login } from '@ui-adk/views/login';
import { ITestConfigurationService } from '@ui-adk/services/configuration/test-configuration-service';
import { User, Tenant } from '@ui-adk/types';
import { AppHeader } from '../app/layout/app-header';
import { AppSideNav } from '../app/layout/app-side-nav';
import { PageUrl } from '@ui-adk/types/routes';
import { ApiRoute } from '@ui-adk/types/api-routes';

export abstract class AbstractViewBase {
  private apiRoutes: ApiRoute[];
  protected locator: Locator;
  protected route: PageUrl;
  public page: Page;
  protected config: ITestConfigurationService;

  constructor(page: Page, locator: Locator, config: ITestConfigurationService, route: PageUrl, apiRoutes: ApiRoute[]) {
    this.page = page;
    this.locator = locator;
    this.config = config;
    this.route = route;
    this.apiRoutes = apiRoutes;
    this.titleBar = new AppHeader(page);
    this.sideMenu = new AppSideNav(page);
  }

  public titleBar: AppHeader;
  public sideMenu: AppSideNav;

  /**
   *
   */
  public async login(user?: User, organization?: Tenant): Promise<void>;
  public async login(user: User, organization: Tenant): Promise<void> {
    user = user ? user : this.config.user;
    organization = organization ? organization : this.config.organization;

    await this.page.goto(this.config.env.baseUrl);
    const loginPopup: Login = new Login(this.page, this.config);
    await loginPopup.waitFor();

    await loginPopup.login(user);
    await loginPopup.chooseOrganization(organization);
    await this.waitUntilLoaded();
  }

  /**
   * Will launch the goto to the url.
   * @example await goto();
   */
  public async goto() {
    const responsePromise = this.monitorApiResponses(this.apiRoutes);
    await this.page.goto(new URL(this.route, this.config.env.baseUrl).href);
    if (responsePromise.length > 0) {
      await Promise.all(responsePromise);
    }

    await this.waitUntilLoaded();
  }

  /**
   * Gets the current url
   */
  public getUrl(): string {
    return this.page.url();
  }

  public async getUserEmail(): Promise<string> {
    return this.titleBar.userProfileMenu.getUserEmail();
  }

  public async logout() {
    await this.titleBar.userProfileMenu.logout();
  }

  public async switchOrganization(tenant: Tenant) {
    if (!(await this.titleBar.userProfileMenu.isOpen())) {
      await this.titleBar.userProfileMenu.toggle();
    }

    await this.titleBar.userProfileMenu.switchOrganization();
    const loginPopup: Login = new Login(this.page, this.config);
    await loginPopup.waitFor();

    await loginPopup.chooseOrganization(tenant);
    await this.waitUntilLoaded();
  }

  public async getTenantName(): Promise<string> {
    return this.titleBar.getTenantName();
  }

  public async isActive(): Promise<boolean> {
    return this.route == (await this.getUrl());
  }

  public async getUserRoles(): Promise<string[]> {
    return this.titleBar.userProfileMenu.getUserRoles();
  }

  public async waitUntilLoaded() {
    await this.locator.waitFor({ state: 'visible', timeout: 30000 });
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForLoadState('load');
    await this.page.waitForLoadState('networkidle');

    await this.titleBar.waitFor();
    await this.page.waitForTimeout(2 * 1000);
  }

  //
  protected monitorApiResponses(apiResponses: ApiRoute[]): Promise<Response>[] {
    const responses: Promise<Response>[] = [];
    if (apiResponses && apiResponses.length > 0) {
      for (let i = 0; i < apiResponses.length; i++) {
        const responseToMonitor = `**/${apiResponses[i]}**`;
        responses.push(this.page.waitForResponse(responseToMonitor));
      }

      this.page.on('response', (response) => this.printApiResponses(response));
    }

    return responses;
  }

  private printApiResponses(response: Response) {
    if (response.url().startsWith(this.config.env.apiUrl)) {
      // NOTE: use this if you need to monitor the api call...
      // console.log(response.status(), response.url().replace(this.config.env.apiUrl, ''));
    }
  }
}
