import { Page } from '@playwright/test';
import { AbstractElementBase } from '@ui-adk/components/abstract-element-base';

export class AppHeaderUserProfileMenu extends AbstractElementBase {
  constructor(page: Page) {
    super(page, page.getByRole('button', { name: 'Account' }));
  }

  public async isOpen() {
    const openClass = await this.locator.getAttribute('class');
    return openClass.includes('cds--header__action--active');
  }

  /**
   * Will toggle the user menu open or close
   */
  public async toggle() {
    await this.click();
  }

  public async getUserEmail() {
    if ((await this.isOpen()) == false) {
      await this.toggle();
    }

    const locator = this.page.getByTestId('user-profile-email');
    await locator.waitFor({ state: 'visible' });
    const email = await locator.innerText();
    return email;
  }

  public async switchOrganization() {
    if ((await this.isOpen()) == false) {
      await this.toggle();
    }
    const locator = this.page.getByTestId('switch-organization');
    await locator.waitFor({ state: 'visible' });
    await locator.click();
  }

  public async getUserRoles(): Promise<string[]> {
    if ((await this.isOpen()) == false) {
      await this.toggle();
    }
    const locator = this.page.getByTestId('role-preview');
    await locator.waitFor({ state: 'visible' });
    const roles = await locator.locator('div[class*="_roleItemContent"] span').allInnerTexts();
    return roles;
  }

  public async logout() {
    if ((await this.isOpen()) == false) {
      await this.toggle();
    }

    const locator = this.page.getByTestId('log-out');
    await locator.waitFor({ state: 'visible' });
    await locator.click();
  }
}
