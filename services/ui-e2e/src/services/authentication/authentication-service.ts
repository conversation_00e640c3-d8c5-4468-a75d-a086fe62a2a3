import { AuthenticationClient, AuthenticationClientOptions, TokenSet } from 'auth0';
import fs from 'fs';
import * as dotenv from 'dotenv';
import { Response } from '@playwright/test';
import { CacheableToken } from './cacheable-token';

const CACHED_TOKEN_FILE = './token.json';

export interface Auth0UserInfo {
  domain: string;
  audience: string;
  clientId: string;
  clientSecret: string;
}

export class AuthenticationService {
  private static _instance: AuthenticationService = undefined;
  private userInfo: Auth0UserInfo;
  private auth0Options: AuthenticationClientOptions;
  private auth0Client: AuthenticationClient;

  private constructor() {
    dotenv.config();
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    this.userInfo = {
      domain: process.env.AUTH_DOMAIN,
      audience: process.env.AUTH_AUDIENCE,
      clientId: process.env.AUTH_CLIENT_ID,
      clientSecret: process.env.AUTH_CLIENT_SECRET,
    };
    this.auth0Options = this.setOptions(this.userInfo);
    this.auth0Client = new AuthenticationClient(this.auth0Options);
  }

  public static get instance(): AuthenticationService {
    if (!this._instance) {
      this._instance = new AuthenticationService();
    }

    return this._instance;
  }

  public async getAuthToken(): Promise<string> {
    let token: CacheableToken;

    // Do we have a token that is cached...
    if (this.isTokenCached()) {
      token = this.getCachedToken();

      // is the token is not expired...
      if (!this.isTokenExpired(token)) {
        // return the cached token...
        return token.access_token;
      }
    }

    token = await this.generateToken();
    this.setCachedToken(token);
    // Return token...
    return token.access_token;
  }

  private isTokenCached() {
    return fs.existsSync(CACHED_TOKEN_FILE);
  }

  public getCachedToken(): CacheableToken {
    const rawData = fs.readFileSync(CACHED_TOKEN_FILE, 'utf8');
    const token: CacheableToken = JSON.parse(rawData) as CacheableToken;
    return token;
  }

  private setCachedToken(token: CacheableToken) {
    fs.writeFile(CACHED_TOKEN_FILE, JSON.stringify(token), (err) => {
      if (err) console.log('Error writing token cached file:', err);
    });
  }

  private async generateToken(): Promise<CacheableToken> {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    const response = await this.auth0Client.oauth.clientCredentialsGrant({ audience: this.userInfo.audience });
    return this.createCachedToken(response.data);
  }

  public async interceptToken(tokenResponse: Promise<Response>): Promise<void> {
    const response = await tokenResponse;
    const body = await response.body();
    const bodyString = body.toString();

    // Validate response
    if (response.status() !== 200) {
      throw new Error(`Token request failed with status ${response.status()}: ${bodyString}`);
    }

    if (!bodyString || bodyString.trim().length === 0) {
      throw new Error(`Empty response body from token endpoint. Status: ${response.status()}`);
    }

    try {
      const tokenSet = JSON.parse(bodyString) as TokenSet;

      // Validate token structure
      if (!tokenSet.access_token) {
        throw new Error('Token response missing access_token');
      }

      const cachedToken = this.createCachedToken(tokenSet);
      this.setCachedToken(cachedToken);
    } catch (parseError) {
      throw new Error(`Invalid JSON in token response: ${parseError.message}. Body: ${bodyString.substring(0, 200)}...`);
    }
  }

  private createCachedToken(token: TokenSet): CacheableToken {
    const createdTime = Math.floor(Date.now() / 1000);
    return {
      created: createdTime,
      expiration_time: createdTime + token.expires_in,
      token_type: token.token_type,
      access_token: token.access_token,
      expires_in: token.expires_in,
    };
  }

  private isTokenExpired(token: CacheableToken): boolean {
    return token.expiration_time < Math.floor(Date.now() / 1000);
  }

  private setOptions(info: Auth0UserInfo): AuthenticationClientOptions {
    return {
      domain: info.domain,
      clientId: info.clientId,
      clientSecret: info.clientSecret,
    };
  }
}
