import os
import structlog
from datetime import datetime

logging = structlog.get_logger()

# TODO: Eventually these should be stored as tenant level config value
# The default expiration time for all metrics.
EVENT_TIME_PROCESSING_WINDOW_MINUTES = os.getenv(
    "EVENT_TIME_PROCESSING_WINDOW_MINUTES", 60
)

# The amount of time in minutes that we expect to pass between a real-world event occuring, and the
# event time sent along in message data being processed by MP.
EVENT_TIME_EXPECTED_DELAY_MINUTES = os.getenv("EVENT_TIME_EXPECTED_DELAY_MINUTES", 1)

# Name for Redis keys that store previous Neo4j Graph responses (ie {nodes:[], edges:[]})
GRAPH_CACHE_NAME = 'process_flow_graph'

# Name for cache of area nodes that have been created in Neo4j.
# Used when processing and creating Neo4j area/aisle/shuttle/lift/station nodes.
GRAPH_AREA_CACHE_NAME = 'graph_area_cache'

# Name for cache of edges that have been created in Neo4j.
# Used when processing and creating Neo4j edges.
GRAPH_EDGE_CACHE_NAME = 'graph_edge_cache'

# Name for cache of metric nodes that have been created in Neo4j.
# Used when processing and creating Neo4j metric nodes.
GRAPH_METRIC_CACHE_NAME = 'graph_metric_cache'

# TEMPORARY SOLUTION: This allows us to control which facilities are processed.
# This is needed because we are reusing the same metric processor for multiple tenants, and we want
# to control which facilities are processed by the metric processor as we roll out to each tenant.
# Most likely will remove this once EDP is managing the data pipeline per tenant for us, then we can
# safely consume everything that comes in.
METRIC_PROCESSOR_FACILITY_ALLOW_LIST = [
    "superioruniform_eudoraar",
    "acehardware_wilmertx",
]

# The field in each fact that we use as the time that the corresponding event occurred.
TIMESTAMP_FIELD_NAMES = {
    "bin_utilization": "event_timestamp_utc",
    "connection_movement": "movement_end_timestamp_utc",
    "fault_event": "event_timestamp_utc",
    "multishuttle_movement": "movement_end_timestamp_utc",
    "nok_interval": "fault_end_timestamp_utc",
    "pick": "event_timestamp_utc",
    "pick_activity": "event_timestamp_utc",
    "vehicle_movement": "movement_end_timestamp_utc",
    "viz_event_log": "event_timestamp_utc",
}


def get_event_time(fact_type, fact_obj):
    """Given a fact object, returns the date/time that the event occurred.

    Args:
        fact_obj (Object): A dictionary representing one fact event occuring.

    Returns:
        _type_: Datetime or None
    """
    # Get the datetime field to use for this event type and extract the value.
    field_name = TIMESTAMP_FIELD_NAMES.get(fact_type, "")
    event_timestamp = None

    if fact_type and field_name:
        event_timestamp = fact_obj.get(field_name)
        # If event timestamp is a string, try to convert to date.
        if isinstance(event_timestamp, str):
            try:
                event_timestamp = datetime.fromisoformat(event_timestamp)
            except Exception:
                event_timestamp = None
                logging.error(
                    f"Cannot convert event_timestamp string value to datetime: '{event_timestamp}'"
                )

    if event_timestamp is None:
        logging.error(
            f"Unable to determine event time for field:{field_name} and fact:{fact_type}"
        )

    return event_timestamp
