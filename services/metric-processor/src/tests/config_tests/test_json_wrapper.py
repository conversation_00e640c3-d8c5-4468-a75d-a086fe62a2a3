import os
import json
import logging
from typing import List, Dict, Any
from pydantic import ValidationError

from src.logging_config import logger
from src.schemas.test_schema import TestSuite


class TestJsonWrapper:
    """
    Wrapper class for handling JSON-based test suites with suite metadata.
    """

    def __init__(self, fact_name: str, filepath: str, test_suite: Dict[str, Any]):
        self.fact_name = fact_name
        self.filepath = filepath

        # Validate the test suite using Pydantic
        try:
            validated_suite = TestSuite(**test_suite)
        except ValidationError as e:
            raise ValueError(f"Invalid TEST_SUITE format in {fact_name}:\n{e}")

        self.test_suite_name = validated_suite.test_suite
        self.test_suite_description = validated_suite.test_suite_description
        self.tests = validated_suite.tests
        self.results = []

    def compare_results(
        mock_calls: List[Any], expected_output: List[Dict[str, Any]]
    ) -> bool:
        logging.debug(f"Mock calls captured: {[call.kwargs for call in mock_calls]}")
        for expected in expected_output:
            matched_call = any(
                expected.items() <= call.kwargs.items() for call in mock_calls
            )
            if not matched_call:
                logging.error(f"Expected call not found: {expected}")
                logging.error(
                    f"Available mock calls: {[call.kwargs for call in mock_calls]}"
                )
                return False
        return True

    def write_junit(self, output_dir="test_results"):
        """Writes test results in JUnit XML format with detailed output information."""
        try:
            os.makedirs(output_dir, exist_ok=True)
            filepath = os.path.join(output_dir, f"{self.fact_name}.xml")

            with open(filepath, "w") as file:
                file.write(
                    f"<testsuite name='{self.test_suite_name}' "
                    f"description='{self.test_suite_description}'>\n"
                )

                for test in self.tests:
                    test_description = test.test_description
                    expected_output = test.expected_output  # Get expected outputs

                    # Create a testcase for the overall test input
                    file.write(f"  <testcase name='{test_description}'>\n")

                    # Iterate through expected outputs and create nested testcases
                    for i, expected_output in enumerate(expected_output):
                        output_name = f"Output {i + 1}"
                        matched = False  # Flag to track if the output was matched
                        matched_result = None  # Store the actual matched result

                        # Iterate over config results to look for a match for the current output.
                        for actual_result in self.results:
                            if all(
                                getattr(actual_result, k) == v
                                for k, v in expected_output.items()
                            ):
                                matched = True
                                matched_result = (
                                    actual_result  # Store the matched result
                                )
                                break

                        status = "passed" if matched else "failed"
                        file.write(
                            f"    <testcase name='{output_name}' status='{status}'>\n"
                        )

                        if matched_result:
                            # Include the matched config result directly in the output
                            file.write(
                                f"      <matched_config_result>{matched_result}</matched_config_result>\n"
                            )
                        elif not matched:
                            file.write(
                                f"      <failure message='Expected output not found or mismatched'>"
                                f"{json.dumps(expected_output, indent=4)}"
                                f"</failure>\n"
                            )

                        file.write("    </testcase>\n")

                    file.write("  </testcase>\n")

                file.write("</testsuite>\n")

        except Exception as e:
            logger.error(f"Failed to write JUnit results: {e}")

    def write_text_summary(self, output_dir="test_results"):
        """
        Writes a human-readable summary of test results to a text file and console.
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            filepath = os.path.join(output_dir, f"{self.fact_name}_summary.txt")

            # Create a formatted console output
            console_output = []
            
            # Header
            console_output.append("\n" + "="*80)
            console_output.append(f"Test Suite: {self.test_suite_name}")
            console_output.append(f"Description: {self.test_suite_description}")
            console_output.append("="*80 + "\n")

            # Group results by test description
            results_by_test = {}
            for result in self.results:
                test_desc = result.get('test_description', 'Unknown Test')
                if test_desc not in results_by_test:
                    results_by_test[test_desc] = []
                results_by_test[test_desc].append(result)

            # Format each test group
            for test_desc, test_results in results_by_test.items():
                console_output.append(f"\nTest: {test_desc}")
                console_output.append("-"*40)
                
                for result in test_results:
                    status = result.get('status', 'UNKNOWN')
                    actual = result.get('actual', 'No output')
                    expected = result.get('expected', 'No expectation')
                    
                    # Status with icon
                    status_icon = "✓" if status == "passed" else "✗"
                    console_output.append(f"{status_icon} {status.upper()}")
                    
                    # Format expected vs actual with highlighting
                    if status != "passed":
                        # Find the differences between expected and actual
                        expected_parts = expected.split(':')
                        actual_parts = actual.split(':')
                        diff_expected = []
                        diff_actual = []
                        
                        for i, (e, a) in enumerate(zip(expected_parts, actual_parts)):
                            if e != a:
                                diff_expected.append(f"*{e}*")
                                diff_actual.append(f"*{a}*")
                            else:
                                diff_expected.append(e)
                                diff_actual.append(a)
                        
                        expected = ':'.join(diff_expected)
                        actual = ':'.join(diff_actual)
                    
                    console_output.append(f"  Expected: {expected}")
                    console_output.append(f"  Actual:   {actual}")
                    
                    # Add config name if available
                    config_name = result.get('config_name')
                    if config_name:
                        console_output.append(f"  Config:   {config_name}")
                    
                    console_output.append("")

            # Add summary statistics
            total_tests = len(self.results)
            passed_tests = sum(1 for r in self.results if r.get('status') == 'passed')
            failed_tests = total_tests - passed_tests
            
            console_output.append("\n" + "="*80)
            console_output.append("Summary:")
            console_output.append(f"  Total Tests: {total_tests}")
            console_output.append(f"  Passed: {passed_tests}")
            console_output.append(f"  Failed: {failed_tests}")
            console_output.append("="*80)

            # Write to file
            with open(filepath, "w") as file:
                file.write("\n".join(console_output))

            # Print to console
            print("\n".join(console_output))
            logger.info(f"Summary results written to: {filepath}")

        except Exception as e:
            logger.error(f"Failed to write summary results: {e}")
