from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "fault_denominator_tests",
    "test_suite_description": "Tests for fault denominators used to calculate movements_per_fault and arrivals_per_fault",
    "tests": [
        # Test workstations arrivals per Fault Denominator
        {
            "test_description": "workstations arrivals per fault denominator metric",
            "test_input": [
                {
                    "classification": "manual",
                    "equipment_code": "GTP78-GTP-07_LF",
                    "end_timestamp_utc": recent_timestamp,
                    "fault_duration_milliseconds": "2000",
                    "fault_end_timestamp_utc": recent_timestamp,
                    "status_code": "M:DMSRackOutfeedDevi"
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:arrivals_per_fault_denominator:60m_set:ratio",
                    "success": True,
                    "config_name": "workstations_arrivals_per_fault_denominator",
                }
            ],
        },
        # Test workstations Arrivals per Fault Denominator
        {
            "test_description": "workstations arrivals per fault denominator metric",
            "test_input": [
                {
                    "classification": "manual",
                    "equipment_code": "GTP78-GTP-07_LF",
                    "end_timestamp_utc": recent_timestamp,
                    "fault_duration_milliseconds": "2000",
                    "fault_end_timestamp_utc": recent_timestamp,
                    "status_code": "M:DMSRackOutfeedDevi"
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-07:arrivals_per_fault_denominator:60m_set:ratio",
                    "success": True,
                    "config_name": "workstation_arrivals_per_fault_denominator",
                }
            ],
        },
    ],
}
