from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "connection_movement_edge_tests",
    "test_suite_description": "Tests for connection_movement edge metrics",
    "tests": [
        {
            "test_description": "Test receiving_outbound_units_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "source_location_code": "RECV-INDUCT",
                    "handling_unit_code": "HU123",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "receiving_outbound_units_edge"}
            ],
        },
        {
            "test_description": "Test multishuttle_inbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "destination_location_code": "MSAI01PS",
                    "handling_unit_code": "HU456",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "multishuttle_inbound_totes_edge"}
            ],
        },
        {
            "test_description": "Test multishuttle_outbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "source_location_code": "MSAI01DS",
                    "handling_unit_code": "HU789",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "multishuttle_outbound_totes_edge"}
            ],
        },
        {
            "test_description": "Test multishuttle_aisle_inbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "destination_location_code": "MSAI02PS",
                    "handling_unit_code": "HU101",
                }
            ],
            "expected_output": [
                {
                    "success": True,
                    "config_name": "multishuttle_aisle_inbound_totes_edge",
                }
            ],
        },
        {
            "test_description": "Test multishuttle_aisle_outbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "source_location_code": "MSAI03DS",
                    "handling_unit_code": "HU202",
                }
            ],
            "expected_output": [
                {
                    "success": True,
                    "config_name": "multishuttle_aisle_outbound_totes_edge",
                }
            ],
        },
        {
            "test_description": "Test workstations_inbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "destination_location_code": "M123GTPD1",
                    "handling_unit_code": "HU303",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "workstations_inbound_totes_edge"}
            ],
        },
        {
            "test_description": "Test workstations_outbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "source_location_code": "M456GTPD1",
                    "handling_unit_code": "HU404",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "workstations_outbound_totes_edge"}
            ],
        },
        {
            "test_description": "Test receiving_stock_time_start",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "destination_location_code": "RECV-INDUCT",
                    "handling_unit_code": "HU505",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "receiving_stock_time_start"}
            ],
        },
        {
            "test_description": "Test receiving_stock_time_stop",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "source_location_code": "RECV-INDUCT",
                    "handling_unit_code": "HU606",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "receiving_stock_time_stop"}
            ],
        },
        {
            "test_description": "Test miniload_inbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "destination_location_code": "ML001I",
                    "handling_unit_code": "HU707",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "miniload_inbound_totes_edge"}
            ],
        },
        {
            "test_description": "Test miniload_outbound_totes_edge",
            "test_input": [
                {
                    "movement_end_timestamp_utc": recent_timestamp,
                    "source_location_code": "ML002O",
                    "handling_unit_code": "HU808",
                }
            ],
            "expected_output": [
                {"success": True, "config_name": "miniload_outbound_totes_edge"}
            ],
        },
        # {
        #     "test_description": "Test packing_inbound_totes_edge",
        #     "test_input": [
        #         {
        #             "movement_end_timestamp_utc": recent_timestamp,
        #             "destination_location_code": "PACK-STN-123",
        #             "handling_unit_code": "HU909",
        #         }
        #     ],
        #     "expected_output": [
        #         {"success": True, "config_name": "packing_inbound_totes_edge"}
        #     ],
        # },
        # {
        #     "test_description": "Test packing_outbound_totes_edge",
        #     "test_input": [
        #         {
        #             "movement_end_timestamp_utc": recent_timestamp,
        #             "source_location_code": "PACK-STN-456",
        #             "handling_unit_code": "HU010",
        #         }
        #     ],
        #     "expected_output": [
        #         {"success": True, "config_name": "packing_outbound_totes_edge"}
        #     ],
        # },
    ],
}
