from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "miniload_movement_tests",
    "test_suite_description": "Tests for miniload movement metrics",
    "tests": [
        # Test number of distinct miniloads available
        {
            "test_description": "distinct miniload count metric",
            "test_input": [
                {
                    "equipment_code": "ML001",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:miniloads_available:60m_set:count",
                    "success": True,
                    "config_name": "miniload_num_miniloads_available",
                }
            ],
        },
        # Test miniload total OK aisle movements
        {
            "test_description": "miniload aisle total movements metric",
            "test_input": [
                {
                    "source_location_code": "ML001",
                    "destination_location_code": "ML002",
                    "movement_status_code": "OK",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:aisle_total_movements:60m_set:count",
                    "success": True,
                    "config_name": "miniload_aisle_total_movements",
                }
            ],
        },
        # Test miniload storage movements
        {
            "test_description": "miniload aisle storage movements metric",
            "test_input": [
                {
                    "source_location_code": "ML003",
                    "destination_location_code": "ML004",
                    "movement_status_code": "OK",
                    "movement_type_code": "Storage",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:aisle_storage_movements:60m_set:count",
                    "success": True,
                    "config_name": "miniload_aisle_storage_movements",
                }
            ],
        },
        # Test miniload retrieval movements
        {
            "test_description": "miniload aisle retrieval movements metric",
            "test_input": [
                {
                    "source_location_code": "ML005",
                    "destination_location_code": "ML006",
                    "movement_status_code": "OK",
                    "movement_type_code": "Retrieval",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:aisle_retrieval_movements:60m_set:count",
                    "success": True,
                    "config_name": "miniload_aisle_retrieval_movements",
                }
            ],
        },
        # Test miniload bypass movements
        {
            "test_description": "miniload aisle bypass movements metric",
            "test_input": [
                {
                    "source_location_code": "ML007",
                    "destination_location_code": "ML008",
                    "movement_status_code": "OK",
                    "movement_type_code": "Bypass",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:aisle_bypass_movements:60m_set:count",
                    "success": True,
                    "config_name": "miniload_aisle_bypass_movements",
                }
            ],
        },
        # Test miniload storage rate
        {
            "test_description": "miniload storage rate metric",
            "test_input": [
                {
                    "source_location_code": "ML009",
                    "destination_location_code": "ML010",
                    "movement_status_code": "OK",
                    "movement_type_code": "Storage",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:storage_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "miniload_storage_rate",
                }
            ],
        },
        # Test miniload retrieval rate
        {
            "test_description": "miniload retrieval rate metric",
            "test_input": [
                {
                    "source_location_code": "ML011",
                    "destination_location_code": "ML012",
                    "movement_status_code": "OK",
                    "movement_type_code": "Retrieval",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:retrieval_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "miniload_retrieval_rate",
                }
            ],
        },
        # Test miniload bypass rate
        {
            "test_description": "miniload bypass rate metric",
            "test_input": [
                {
                    "source_location_code": "ML013",
                    "destination_location_code": "ML014",
                    "movement_status_code": "OK",
                    "movement_type_code": "Bypass",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:bypass_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "miniload_bypass_rate",
                }
            ],
        },
        # Test miniload movements per fault numerator
        {
            "test_description": "miniload movements per fault numerator metric",
            "test_input": [
                {
                    "source_location_code": "ML015",
                    "destination_location_code": "ML016",
                    "movement_status_code": "OK",
                    "movement_end_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:miniload:movements_per_fault_numerator:60m_set:ratio",
                    "success": True,
                    "config_name": "miniload_movements_per_fault_numerator",
                }
            ],
        },
    ],
}
