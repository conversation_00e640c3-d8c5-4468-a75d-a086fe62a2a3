from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "pick_tests",
    "test_suite_description": "Tests for the pick fact metric configuration.",
    "tests": [
        {
            "test_description": "Test workstations_cycle_count",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "CycleCount",
                    "picked_qty": "1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:cycle_count:60m_set:count",
                    "success": True,
                    "config_name": "workstations_cycle_count",
                },
            ],
        },
        {
            "test_description": "Test workstations_cycle_count_rate",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "CycleCount",
                    "picked_qty": "1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:cycle_count_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstations_cycle_count_rate",
                },
            ],
        },
        {
            "test_description": "Test workstations_orders_picked_rate",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "GTPPrint",
                    "picked_qty": "1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:orders_picked_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstations_orders_picked_rate",
                },
            ],
        },
        {
            "test_description": "Test workstations_order_lines_picked_count with PICK workflow_code",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "PICK",
                    "picked_qty": "1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:order_lines_picked_count:60m_set:count",
                    "success": True,
                    "config_name": "workstations_order_lines_picked_count",
                },
            ],
        },
        {
            "test_description": "Test workstations_order_lines_picked_rate",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "PICK",
                    "picked_qty": "1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:order_lines_picked_rate:15m_set:hourly_rate",
                    "success": True,
                    "config_name": "workstations_order_lines_picked_rate",
                }
            ],
        },
        ## note this test is failing!!!
        {
            "test_description": "Test workstations_orders_picked_count",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "GTPPrint",
                    "picked_qty": "1",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:orders_picked_count:60m_set:count",
                    "success": True,
                    "config_name": "workstations_orders_picked_count",
                },
            ],
        },
        {
            "test_description": "Test workstations_order_quantity_picked",
            "test_input": [
                {
                    "event_type": "pick",
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-02F1",
                    "workflow_code": "PICK",
                    "picked_qty": "10",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:workstations:order_quantity_picked:60m_set:sum",
                    "success": True,
                    "config_name": "workstations_order_quantity_picked",
                },
            ],
        },
        {
            "test_description": "Test workstation_order_lines_picked_count",
            "test_input": [
                {
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-03B3",
                    "picked_qty": "1",
                    "workflow_code": "PICK",
                    "workstation_code": "M11-GTP-03",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-03:order_lines_picked:60m_set:count",
                    "success": True,
                    "config_name": "workstation_order_lines_picked_count",
                },
            ],
        },
        {
            "test_description": "Test workstation_cycle_counts",
            "test_input": [
                {
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-01F6",
                    "picked_qty": "1",
                    "workflow_code": "CycleCount",
                    "workstation_code": "M11-GTP-01",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-01:cycle_counts:60m_set:count",
                    "success": True,
                    "config_name": "workstation_cycle_counts",
                },
            ],
        },
        {
            "test_description": "Test workstation_order_lines_picked_by_destination",
            "test_input": [
                {
                    "event_timestamp_utc": recent_timestamp,
                    "induction_zone_code": "M11-GTP-04B1",
                    "picked_qty": "1",
                    "workflow_code": "PICK",
                    "workstation_code": "M11-GTP-04",
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:M11-GTP-04:order_lines_picked_M11-GTP-04B1:60m_set:count",
                    "success": True,
                    "config_name": "workstation_order_lines_picked_by_destination",
                },
            ],
        },
    ],
}
