"""
This module provides functionality to dynamically discover, load, and execute test suites 
for validating the `MetricProcessor` functionality and metric configurations. It loads test configurations from tenant-specific 
directories and verifies that the computed results match the expected outputs.

Features:
- Dynamically loads test modules from tenant directories.
- Runs tests and compares results against expected output.
- Uses unittest mock patches to simulate dependencies like Redis and Neo4j. This avoids calling external services.
- Logs test results and generates JUnit-compatible reports.

Dependencies:
- `unittest.mock.patch` is used to mock external services.
- `MetricProcessor` is the core processing class under test.
- `TestJsonWrapper` handles test execution and result storage.
"""

from argparse import ArgumentParser
import os
import glob
import importlib.util
import sys
from types import ModuleType
from typing import Any, Dict, List, Optional, Union
from unittest.mock import patch
from src.tests.config_tests.test_json_wrapper import TestJsonWrapper
from src.metric_processor import MetricProcessor
import logging

# Define ANSI color codes
GREEN = '\033[0;32m'
RED = '\033[0;31m'
YELLOW = '\033[0;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'  # No Color

# Completely disable all logging
logging.disable(logging.CRITICAL)

# Define base directory explicitly
BASE_DIR = os.path.join(os.path.dirname(__file__), "test_suites")


###############################################################################
# Helper functions to load test modules
###############################################################################


def load_module_from_path(module_name, filepath) -> ModuleType:
    """
    Dynamically loads a Python module from a specified file path.

    Args:
        module_name (str): Name to assign to the dynamically loaded module.
        filepath (str): Absolute path to the Python file to be loaded.

    Returns:
        module: The dynamically imported module.

    Raises:
        ImportError: If the module cannot be loaded due to a missing file or invalid path.
    """
    spec = importlib.util.spec_from_file_location(module_name, filepath)
    if spec is None or spec.loader is None:
        raise ImportError(f"Cannot load module {module_name} from path {filepath}")
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def load_test_module(filepath) -> ModuleType:
    """
    Loads a test module dynamically from a given file path.

    Args:
        filepath (str): Path to the test file.

    Returns:
        module: The dynamically loaded test module.
    """
    module_name = os.path.splitext(os.path.basename(filepath))[0]
    return load_module_from_path(module_name, filepath)


###############################################################################
# Validation helper functions
###############################################################################


def validate_test_output(
    test_output: List[Dict[str, str]], config_results: List[object]
) -> List[Dict[str, List[str]]]:
    """
    Validates test output against the expected results.

    Compares expected and actual results field-by-field, identifying mismatched
    fields if the results do not match.

    Args:
        test_output (list[dict]): Expected test outputs as a list of dictionaries.
        config_results (list[object]): Processed results from the metric processor.

    Returns:
        list[dict]: A list of mismatched cases, where each dictionary contains:
            - `expected` (dict): The expected output dictionary.
            - `mismatched_fields` (list[str]): List of fields that did not match.

        Returns an empty list if all test cases pass.

    """
    unmatched_outputs = []

    for expected in test_output:
        matched = False
        mismatched_fields = []
        for actual in config_results:
            normalized_actual = {k: getattr(actual, k, None) for k in expected.keys()}
            mismatched_fields = [
                key for key in expected if expected[key] != normalized_actual.get(key)
            ]
            if not mismatched_fields:
                matched = True
                break
        if not matched:
            unmatched_outputs.append(
                {"expected": expected, "mismatched_fields": mismatched_fields}
            )

    return unmatched_outputs


###############################################################################
# Main test runner
###############################################################################


def run_tests(
    tenant: Optional[str] = None, factType: Optional[str] = None
) -> Union[int, None]:
    """
    Executes tests for a specified tenant or all available tenants.

    - Loads test modules dynamically from the `test_suites` directory.
    - Runs tests for each facility within a tenant directory.
    - Uses mock patches for Redis and Neo4j to isolate test behavior and avoid calling external services.
    - Validates test outputs and logs mismatched results.
    - Generates JUnit-compatible reports.

    Args:
        tenant (str, optional): The tenant for which tests should be run.
            If not provided, tests for all tenants in `test_suites/` will be executed.
        factType (str, optional): The fact type to test.  It should match the file
            name of your test, e.g. `pick` will only run pick.test.py

    Logs:
        - Pass/fail summary for all test cases.
        - Detailed error messages for failed test cases.
    """
    print(f"Running tests from: {BASE_DIR}")

    total_tests = 0
    failed_tests = 0
    failed_test_details = []
    test_results = []  # Store all test results

    tenant = sys.argv[1] if len(sys.argv) > 1 else None
    factType = sys.argv[2] if len(sys.argv) > 2 else None

    # Determine which tenants to process
    tenants_to_process = []
    if tenant:
        tenant_path = os.path.join(BASE_DIR, tenant)
        if os.path.isdir(tenant_path):
            tenants_to_process.append(tenant)
        else:
            print(f"{RED}Tenant '{tenant}' not found in {BASE_DIR}{NC}")
            return
    else:
        tenants_to_process = [d.name for d in os.scandir(BASE_DIR) if d.is_dir()]

    # Iterate over selected tenants
    for tenant in tenants_to_process:
        tenant_path = os.path.join(BASE_DIR, tenant)

        # Iterate over facility directories
        for facility_dir in os.scandir(tenant_path):
            if not facility_dir.is_dir():
                continue

            facility = facility_dir.name
            test_dir = facility_dir.path

            if factType:
                file_name = f"{factType}.test.py"

                files = glob.glob(os.path.join(test_dir, file_name))
            else:
                files = glob.glob(os.path.join(test_dir, "[!_]*.py"))

            for filepath in files:
                try:
                    test_module = load_test_module(filepath)
                    fact_type = os.path.basename(filepath).replace(".test.py", "")
                    test_wrapper = TestJsonWrapper(
                        fact_type, filepath, test_module.TEST_SUITE
                    )

                    for test in test_wrapper.tests:
                        total_tests += 1
                        test_input = test.test_input
                        expected_output = test.expected_output

                        cumulative_results = []

                        for input_data in test_input:
                            try:
                                with patch("src.metric_processor.RedisService"), patch(
                                    "src.metric_processor.Neo4jFactory.get_instance"
                                ), patch(
                                    "src.services.neo4j_factory.Neo4jFactory._fetch_secret",
                                    return_value="MockedSecretValue",
                                ):
                                    config = MetricProcessor.load_config(
                                        tenant, facility, fact_type
                                    )
                                    if not config:
                                        continue

                                    processor = MetricProcessor(
                                        tenant, facility, fact_type, '1', {}, config
                                    )
                                    processor.data = input_data
                                    processor.process_metrics()
                                    cumulative_results.extend(processor.config_results)

                            except Exception as e:
                                failed_tests += 1
                                error_msg = f"{test.test_description}: Exception - {e}"
                                failed_test_details.append(error_msg)
                                test_results.append({
                                    "status": "failed",
                                    "description": test.test_description,
                                    "error": error_msg
                                })
                                continue

                        # Validate results
                        failed_cases = validate_test_output(
                            expected_output, cumulative_results
                        )
                        if failed_cases:
                            failed_tests += 1
                            for case in failed_cases:
                                expected = case["expected"]
                                mismatched_fields = case["mismatched_fields"]
                                failure_message = (
                                    f"Failed test case: {test.test_description}: "
                                    f"Expected {expected}, but mismatched fields {mismatched_fields}"
                                    f"Actual results: {cumulative_results}"
                                )
                                failed_test_details.append(failure_message)
                                test_results.append({
                                    "status": "failed",
                                    "description": test.test_description,
                                    "expected": expected,
                                    "actual": cumulative_results,
                                    "mismatched_fields": mismatched_fields
                                })
                        else:
                            test_results.append({
                                "status": "passed",
                                "description": test.test_description
                            })

                        test_wrapper.results.extend(cumulative_results)
                        test_wrapper.write_junit()

                except Exception as e:
                    failed_tests += 1
                    error_msg = f"Failed to process {filepath}: {e}"
                    failed_test_details.append(error_msg)
                    test_results.append({
                        "status": "failed",
                        "description": f"Failed to process {filepath}",
                        "error": error_msg
                    })

    # Create formatted console output
    print("\n" + "="*80)

    # Show failed tests with details first
    failed_tests_list = [r for r in test_results if r["status"] == "failed"]
    if failed_tests_list:
        print("Failed Tests:")
        print("-"*40)
        for result in failed_tests_list:
            print(f"{RED}✗ {result['description']}{NC}")
            if "error" in result:
                print(f"  Error: {result['error']}")
            if "expected" in result:
                print(f"  Expected: {result['expected']}")
                print(f"  Actual:   {result['actual']}")
                print(f"  Mismatched fields: {result['mismatched_fields']}")
            print("")

    # Show test results summary at the end
    print("="*80)
    print("Test Results Summary")
    print("="*80 + "\n")

    passed_tests = [r for r in test_results if r["status"] == "passed"]
    print(f"{GREEN}✓ {len(passed_tests)} tests passed{NC}")
    if failed_tests_list:
        print(f"{RED}✗ {len(failed_tests_list)} tests failed{NC}")
    print("")
    print("="*80)
