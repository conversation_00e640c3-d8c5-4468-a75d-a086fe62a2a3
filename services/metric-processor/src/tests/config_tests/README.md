# Unit Testing Guide for Metric Processor

This document provides guidelines for writing and adding unit tests for metrics processing in the framework. The purpose of these tests is to validate our configurations per tenant located in `metric-processor/src/config/{tenant}`. Each invocation of `process_metrics` builds a list of `ConfigurationResult` objects which store the results of each processed configuration. Our expected results within our test suites are compared with the actual results of these `ConfigurationResult` objects.

## 1. How the Framework Works When a test runs:

1. The **test input** (event data) is passed into the metric processor.
2. The processor applies **match conditions** from the metric configurations to determine if the event should trigger a metric.
3. If a match is found, a **resolved name** is generated using the **name formula** specified in the configuration.
4. The resulting metric output is stored in a `ConfigurationResult` object.
5. The **test framework automatically compares the actual `ConfigurationResult` output to the expected output**.
6. If the outputs match, the test passes. If they don't, the framework reports the failure and highlights differences. Because the framework does most of the work under the hood, writing a test case is **as simple as defining the right input data and expected output in a structured test configuration**.

## 2. Test Structure

Unit tests are organized into **test suites**, each containing multiple **test cases**. Each test case consists of:

- **Test Description**: A brief description of the test.
- **Test Input**: A JSON-like dictionary that represents an event message.
- **Expected Output**: A list of expected results including resolved metric names and configuration names.

---

## 3. Test File Format

Test cases should be defined in a Python dictionary format within a `.py` file. Example structure:

```python
from tools.generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

TEST_SUITE = {
    "test_suite": "workstations_tests",
    "test_suite_description": "Tests for workstations metrics",
    "tests": [
        {
            "test_description": "source container arrival count metric",
            "test_input": [
                {
                    "message_uid": "source_arrival_001",
                    "event_code": "Arrival",
                    "induction_zone_code": "M001GTP01D1",
                    "event_timestamp_utc": recent_timestamp,
                }
            ],
            "expected_output": [
                {
                    "resolved_name": "superior_uniform:superioruniform_eudoraar:facility:workstations:source_container_arrival_count:60m_set:count",
                    "success": True,
                    "config_name": "workstations_source_container_arrival_count",
                }
            ],
        }
    ],
}
```

\*`recent_timestamp` is used to create now's timestamp

---

## 4. Writing Test Cases

Each test case should follow the **input-output** format.

### Test Input

The `test_input` section should include the relevant fields that match the metric’s **match_conditions**:

- Ensure event fields match the regex patterns in the metric’s `match_conditions`.
- Include necessary identifiers such as `message_uid`, `aisle_code`, `shuttle_code`, etc.

Example:

```python
"test_input": [
    {
        "message_uid": "arrival_test_01",
        "event_code": "Arrival",
        "induction_zone_code": "M123GTP02D1",
    }
]
```

### Expected Output

Each metric has a **resolved name** and a **config name** that should match exactly.

```python
"expected_output": [
    {
        "resolved_name": "superior_uniform:superioruniform_eudoraar:facility:workstations:source_container_arrival_count:60m_set:count",
        "success": True,
        "config_name": "workstations_source_container_arrival_count",
    }
]
```

---

## 5. Running the Tests

To run the tests:

```bash
poetry run test
```

Or, for a specific test suite for a tenant:

```bash
poetry run test superior_uniform pick
```

---

## 6. Debugging Failures

If a test fails, check:

- **Mismatch in `resolved_name`**: Verify that the test's `resolved_name` follows the correct format as defined in the config you are testing.
- **Mismatch in `config_name`**: Make sure the correct metric name is being used.
- **Missing Expected Output**: If `Actual results: []`, it likely means the event didn't match the conditions at all. Make sure the event timestamp you are sending in your input matches the right timestamp field that is set per event type in `metric-processor/src/utils.py`

---

## 7. Adding a New Test

1. **Identify the metric** in {event_type}.py that needs testing. Example: connection_movement.py
2. **Create a test case** in an existing test suite or a new test file whose name matches that of the event type you are testing.
3. **Ensure input fields** match the metric’s `match_conditions`.
4. **Run the test** and verify the output.

---

## 8. Best Practices

✔ **Use meaningful `message_uid` values**  
✔ **Make sure inputs match `match_conditions`**  
✔ **Double-check `resolved_name` formatting**  
✔ **Group similar tests into test suites**  
✔ **Run tests regularly during development**
✔ **Add new tests whenever adding additional configurations**

---
