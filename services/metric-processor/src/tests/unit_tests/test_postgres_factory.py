"""
Unit tests for the PostgresFactory class.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import os
import json
from src.services.postgres_factory import PostgresFactory
from src.services.postgres_service import PostgresService


class TestPostgresFactory(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.tenant = "test-tenant"
        self.mock_secret_data = {
            "POSTGRES_HOST": "test-host",
            "POSTGRES_USER": "test-user",
            "POSTGRES_PASSWORD": "test-password",
            "POSTGRES_PORT": "5432",
        }
        # Clear the factory's instance cache before each test
        PostgresFactory.clear_cache()
        # Save original environment
        self._original_env = os.environ.copy()

    def tearDown(self):
        """Clean up after each test."""
        PostgresFactory.clear_cache()
        # Restore environment
        os.environ.clear()
        os.environ.update(self._original_env)

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_success(self, mock_postgres_service, mock_fetch_secret):
        """Test successful instance creation and retrieval."""
        # Mock the secret fetch
        mock_fetch_secret.return_value = self.mock_secret_data

        # Mock the PostgresService instance
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Get instance
        instance = PostgresFactory.get_instance(self.tenant)

        # Verify
        self.assertEqual(instance, mock_service)
        mock_fetch_secret.assert_called_once()
        mock_postgres_service.assert_called_once_with(
            self.mock_secret_data, self.tenant
        )

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_no_secret(self, mock_postgres_service, mock_fetch_secret):
        """Test instance creation when secret fetch fails."""
        mock_fetch_secret.return_value = None

        instance = PostgresFactory.get_instance(self.tenant)

        self.assertIsNone(instance)
        mock_fetch_secret.assert_called_once()
        mock_postgres_service.assert_not_called()

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_connection_failure(
        self, mock_postgres_service, mock_fetch_secret
    ):
        """Test instance creation when connection fails."""
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = False
        mock_postgres_service.return_value = mock_service

        instance = PostgresFactory.get_instance(self.tenant)

        self.assertIsNone(instance)
        mock_fetch_secret.assert_called_once()
        mock_postgres_service.assert_called_once_with(
            self.mock_secret_data, self.tenant
        )

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_caching(self, mock_postgres_service, mock_fetch_secret):
        """Test that instances are properly cached."""
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Get instance twice
        instance1 = PostgresFactory.get_instance(self.tenant)
        instance2 = PostgresFactory.get_instance(self.tenant)

        # Verify
        self.assertEqual(instance1, instance2)
        mock_fetch_secret.assert_called_once()  # Secret should only be fetched once
        mock_postgres_service.assert_called_once()  # Service should only be created once

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_close_instance(self, mock_postgres_service, mock_fetch_secret):
        """Test closing a specific instance."""
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Get and then close instance
        instance = PostgresFactory.get_instance(self.tenant)
        PostgresFactory.close_instance(self.tenant)

        # Verify
        mock_service.close.assert_called_once()
        self.assertNotIn(self.tenant, PostgresFactory._instances)

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_clear_cache(self, mock_postgres_service, mock_fetch_secret):
        """Test clearing all instances."""
        # Override tearDown for this test to avoid double clear_cache
        self.tearDown = lambda: None
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Create multiple instances
        instance1 = PostgresFactory.get_instance("tenant1")
        instance2 = PostgresFactory.get_instance("tenant2")
        instance1.close.reset_mock()
        instance2.close.reset_mock()

        # Clear cache
        PostgresFactory.clear_cache()

        # Verify
        self.assertGreaterEqual(instance1.close.call_count, 1)
        self.assertGreaterEqual(instance2.close.call_count, 1)
        self.assertEqual(len(PostgresFactory._instances), 0)

    @patch("src.services.postgres_factory.secretmanager.SecretManagerServiceClient")
    def test_fetch_secret_cloud(self, mock_secret_client):
        """Test fetching secret from Cloud Secret Manager."""
        # Ensure LOCALDEV is not set
        os.environ.pop("LOCALDEV", None)
        os.environ["POSTGRES_SOURCE_PROJECT_ID"] = "test-project"
        os.environ["POSTGRES_SOURCE_SECRET_NAME"] = "test-secret"

        # Mock secret client
        mock_client = MagicMock()
        mock_secret_client.return_value = mock_client

        # Mock secret response
        mock_response = MagicMock()
        mock_response.payload.data.decode.return_value = json.dumps(
            self.mock_secret_data
        )
        mock_client.access_secret_version.return_value = mock_response

        # Test
        result = PostgresFactory._fetch_secret()

        # Verify
        self.assertEqual(result, self.mock_secret_data)
        mock_client.access_secret_version.assert_called_once()

    @patch.dict(os.environ, {"LOCALDEV": "true"})
    def test_fetch_secret_local(self):
        """Test fetching secret in local development mode."""
        # Set up environment variables
        os.environ["POSTGRES_HOST"] = "localhost"
        os.environ["POSTGRES_USER"] = "local-user"
        os.environ["POSTGRES_PASSWORD"] = "local-pass"
        os.environ["POSTGRES_PORT"] = "5432"

        result = PostgresFactory._fetch_secret()

        self.assertEqual(result["POSTGRES_HOST"], "localhost")
        self.assertEqual(result["POSTGRES_USER"], "local-user")
        self.assertEqual(result["POSTGRES_PASSWORD"], "local-pass")
        self.assertEqual(result["POSTGRES_PORT"], "5432")


if __name__ == "__main__":
    unittest.main()
