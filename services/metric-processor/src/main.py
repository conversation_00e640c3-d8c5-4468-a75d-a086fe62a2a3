"""
This module is the main entry point for the ICT Metric Processor service.

It initializes the Flask application, sets up routes for processing events and metrics,
and provides functions to handle incoming requests. This service processes events,
logs relevant data, updates Redis and Neo4j databases, and handles complex metric calculations.
"""

import base64
import datetime
import json
import os
import uuid
import structlog
from dotenv import load_dotenv
from flask import Flask, request
from google.cloud import pubsub_v1
from google.api_core.exceptions import GoogleAPIError
from .services.metric_service import MetricService
from .metric_processor import MetricProcessor
from .logging_config import logger
from .utils import METRIC_PROCESSOR_FACILITY_ALLOW_LIST, get_event_time
from .services.redis_service import RedisService

load_dotenv()  # Load environment variables from .env.

# Initialize Flask application
app = Flask(__name__)
app.logger = logger

# Dead letter topic configuration
DEAD_LETTER_TOPIC = "metric_processor_dlq"


def publish_to_dead_letter_topic(message, attributes):
    """
    Publishes a failed message to the dead letter topic for debugging and tracking.

    Args:
        message (dict): The original message that failed processing.
        attributes (dict): Additional attributes associated with the message.

    Logs:
        Errors or successful publication to the dead letter topic.
    """
    if not DEAD_LETTER_TOPIC:
        logger.error("Dead letter topic is not configured.")
        return

    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(os.getenv("PROJECT_ID"), DEAD_LETTER_TOPIC)

    try:
        message_json = json.dumps({"message": message, "attributes": attributes})
        future = publisher.publish(topic_path, message_json.encode("utf-8"))
        future.result()
        logger.info(f"Message sent to dead letter topic: {DEAD_LETTER_TOPIC}")
    except GoogleAPIError as e:
        logger.error(f"Failed to publish to dead letter topic: {e}")
    except Exception as e:
        logger.exception(f"Unexpected error while publishing to dead letter topic: {e}")


def process_facts(cloud_event: dict) -> tuple[dict, int]:
    """
    Processes incoming facts from the message and handles metric updates.

    Args:
        cloud_event (dict): The cloud event containing message data and attributes.

    Returns:
        tuple: Response JSON and HTTP status code.
    """
    try:
        base64data = base64.b64decode(cloud_event["message"]["data"]).decode()
        attributes = cloud_event["message"].get("attributes", {})

        if not base64data:
            error_message = "No data found in message!"
            app.logger.error(error_message)
            return {"error": "No data found in message!"}, 422

        tenant = attributes.get("tenant_id")
        facility = attributes.get("facility_id")
        fact_type = attributes.get("event_type")
        row_hash = attributes.get("row_hash")

        if not tenant or not facility or not fact_type or not row_hash:
            error_msg = f"Tenant, facility, fact_type, or row_hash is missing. ('{tenant}', '{facility}', '{fact_type}', '{row_hash}')"
            app.logger.warning(error_msg)
            return {"status": "ignored", "message": error_msg}, 200

        if not facility in METRIC_PROCESSOR_FACILITY_ALLOW_LIST:
            error_msg = f"Facility {facility} is not in the allow list."
            app.logger.warning(error_msg)
            return {"status": "ignored", "message": error_msg}, 200
        
        try:
            fact_data = json.loads(base64data)
        except json.JSONDecodeError as err:
            error_message = f"Error parsing JSON: {err}"
            app.logger.error(error_message)
            return {"error": "Invalid JSON format in message."}, 400

        # Ensure `fact_data` is a dictionary.
        if not isinstance(fact_data, dict):
            error_message = "Expected an event to be a dictionary."
            app.logger.error(error_message)
            return {"error": "error_message"}, 400

        # Validate and load config before initializing MetricProcessor
        config = MetricProcessor.load_config(tenant, facility, fact_type)
        if not config:
            # Ignore if event type does not return a config. 200 automatically ack's the pubsub message
            return {"status": "ignored"}, 200

        # Process each fact
        log_message(fact_type, fact_data, attributes)
        processor = MetricProcessor(
            tenant, facility, fact_type, row_hash, fact_data, config
        )
        processor.process_metrics()

        return {"status": "success"}, 200

    except Exception as e:
        error_message = f"Error processing message: {e}"
        app.logger.exception(error_message)
        return {"error": "An error occurred while processing the message."}, 500


def get_metrics(cloud_event: dict) -> tuple[dict, int]:
    """
    Fetches metrics based on the provided metric IDs.

    Args:
        cloud_event (dict): The cloud event containing a list of metric IDs.

    Returns:
        tuple: Response JSON and HTTP status code.

    Logs:
        Errors or successful retrieval of metrics.
    """
    try:
        metric_ids = cloud_event.get("metric_ids", [])
        tenant_id = cloud_event.get("tenant_id", "")
        if not metric_ids:
            app.logger.error("No metric_ids found in message!")
            return {"error": "No metric_ids found in message!"}, 422

        if not tenant_id:
            app.logger.error("No tenant_id found in message!")
            return {"error": "No tenant_id found in message!"}, 422

        if not isinstance(metric_ids, list) or len(metric_ids) == 0:
            error_msg = "No metrics in request."
            app.logger.warning(error_msg)
            return {"status": "ignored", "message": error_msg}, 200

        # Process calculations for all complex metrics.
        app.logger.debug("Processing calculations...")
        metric_service = MetricService()
        results = metric_service.get_metric_values(metric_ids, tenant_id)

        last_processed_time = ""
        encoded_last_processed_time = results["last_processed_time"]
        if encoded_last_processed_time:
            last_processed_time = encoded_last_processed_time.decode("utf-8")

        return {
            "status": "success",
            "metrics": results["metrics"],
            "last_processed_time": last_processed_time,
        }, 200
    except Exception:
        app.logger.exception("Error processing/fetching requested metrics.")
        return {
            "error": "An error occurred while processing/fetching the requested metrics."
        }, 500


def log_message(fact_type, fact_obj, attributes):
    """
    Logs details of a processed message.

    Args:
        fact_type (str): The type of fact being processed.
        fact_obj (dict): The fact data object.
        attributes (dict): The attributes associated with the fact.

    Logs:
        Event and latency details for debugging and monitoring.
    """
    raw_message_ingestion_time = attributes.get("raw_message_ingestion_time")
    event_timestamp = get_event_time(fact_type, fact_obj) if fact_type else None

    # If event time stamp is a string, convert to date or set to None so it is ignored.
    if isinstance(event_timestamp, str):
        try:
            event_timestamp = datetime.datetime.fromisoformat(event_timestamp)
        except:
            event_timestamp = None

    now = datetime.datetime.now(datetime.timezone.utc)
    if raw_message_ingestion_time:
        try:
            raw_message_ingestion_time = datetime.datetime.fromisoformat(
                raw_message_ingestion_time
            )
            subscription_latency = now - raw_message_ingestion_time
        except:
            raw_message_ingestion_time = None
            subscription_latency = None
    else:
        subscription_latency = None
    if event_timestamp and raw_message_ingestion_time:
        event_latency = raw_message_ingestion_time - event_timestamp
    else:
        event_latency = None

    logger.info(
        "Processing message",
        attributes=attributes,
        fact_type=fact_type,
        fact_obj=fact_obj,
        event_timestamp=event_timestamp,
        raw_message_ingestion_time=(
            raw_message_ingestion_time.isoformat()
            if raw_message_ingestion_time
            else None
        ),
        subscription_latency=(
            subscription_latency.seconds if subscription_latency else None
        ),
        event_latency=event_latency.seconds if event_latency else None,
    )


@app.route("/", methods=["POST"])
def process_message() -> tuple[dict, int]:
    """
    Handles the primary POST route to process incoming events.

    Returns:
        tuple: Response JSON and HTTP status code.
    """
    try:
        request_json = request.get_json(silent=True)
        app.logger.debug(f"Incoming request JSON: {request_json}")

        if request_json and "message" in request_json:
            return process_facts(request_json)
        else:
            app.logger.error("No valid message data in request.")
            return {"error": "No valid message data in request."}, 400
    except Exception as e:
        app.logger.error(f"Error handling request: {e}")
        return {"error": "An error occurred while handling the request."}, 500


@app.route("/metrics/", methods=["POST"])
def metrics() -> tuple[dict, int]:
    """
    Handles the POST route to fetch metrics based on metric IDs.

    Returns:
        tuple: Response JSON and HTTP status code.
    """
    try:
        request_json = request.get_json(silent=True)
        app.logger.debug(f"Incoming request to calculate metrics JSON: {request_json}")

        if request_json and "metric_ids" and "tenant_id" in request_json:
            return get_metrics(request_json)
        else:
            app.logger.error("No valid metric id or tenant id data in request.")
            return {"error": "No valid metric id or tenant id data in request."}, 400
    except Exception as e:
        app.logger.error(f"An error occurred while handling the metric request: {e}")
        return {"error": "An error occurred while handling the metric request."}, 500


@app.route("/clear-cache/", methods=["POST"])
def clear_cache() -> tuple[dict, int]:
    """
    Handles the POST route to clear Redis cache

    Returns:
        tuple: Response JSON and HTTP status code.
    """
    try:
        request_json: dict = request.get_json(silent=True)
        app.logger.debug(f"Incoming request to clear Redis Cache JSON: {request_json}")

        if request_json and "scope" and "tenant" in request_json:
            scope = request_json.get("scope", None)
            tenant = request_json.get("tenant", None)
            if scope in ["graph", "metrics", "all"] and tenant is not None:
                RedisService().clear_cache(scope, tenant)
            else:
                app.logger.error(
                    f"Invalid scope or tenant data in request. Scope: {scope}, Tenant: {tenant}"
                )
                return {"error": "Invalid scope or tenant data in request."}, 400

            return {"status": "success"}, 200
        else:
            app.logger.error("No valid scope or tenant data in request.")
            return {"error": "An error occured while clearing the cache."}, 500

    except Exception as e:
        app.logger.error(f"An error occurred while clearing Redis cache: {e}")
        return {"error": "An error occurred while clearing Redis cache."}, 500


@app.before_request
def add_request_uuid():
    """
    Adds a unique request ID to each incoming request for tracking and debugging.
    """
    request_id = str(uuid.uuid4())
    structlog.threadlocal.bind_threadlocal(request_id=request_id)
    logger.info("Request started", request_id=request_id)


@app.teardown_request
def clear_request_context(exception=None):
    """
    Clears the request context after each request to clean up thread-local storage.
    """
    logger.info("Request ended")
    structlog.threadlocal.clear_threadlocal()


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=os.getenv("PORT") or 8080)
