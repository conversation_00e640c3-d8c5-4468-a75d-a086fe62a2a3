"""
This module contains the PostgresService class for interacting with PostgreSQL databases.

The PostgresService class provides methods to connect to and interact with tenant-specific
PostgreSQL databases. Each tenant has its own database, and this service manages those
connections.
"""

import os
import structlog
import psycopg2
from psycopg2 import pool
from typing import Optional, Dict, Any

logging = structlog.get_logger()


class PostgresService:
    """
    A service class to interact with PostgreSQL databases.

    Attributes:
        connection_pool (psycopg2.pool.SimpleConnectionPool): A connection pool for the database.
        tenant (str): The tenant identifier for this database connection.
    """

    def __init__(self, secret_data: Dict[str, Any], tenant: str):
        """
        Initializes the PostgresService instance.

        Args:
            secret_data (dict): A dictionary containing PostgreSQL connection details.
                Can be in either format:
                - Local format from .env:
                    - 'POSTGRES_HOST': The host for the PostgreSQL database.
                    - 'POSTGRES_USER': The username for PostgreSQL authentication.
                    - 'POSTGRES_PASSWORD': The password for PostgreSQL authentication.
                    - 'POSTGRES_PORT': The port for the PostgreSQL database.
                - Secret Manager format:
                    - 'host': The host for the PostgreSQL database.
                    - 'username': The username for PostgreSQL authentication.
                    - 'password': The password for PostgreSQL authentication.
                    - 'port': The port for the PostgreSQL database.
            tenant (str): The tenant identifier for this database connection.
        """
        self.tenant = tenant
        self.connection_pool = None
        self._initialize_pool(secret_data)

    def _initialize_pool(self, secret_data: Dict[str, Any]):
        """
        Initializes the connection pool using the provided secret data.

        Args:
            secret_data (dict): A dictionary containing the PostgreSQL connection parameters.
                Can be in either format:
                - Local format from .env:
                    - 'POSTGRES_HOST': The host for the PostgreSQL database.
                    - 'POSTGRES_USER': The username for PostgreSQL authentication.
                    - 'POSTGRES_PASSWORD': The password for PostgreSQL authentication.
                    - 'POSTGRES_PORT': The port for the PostgreSQL database.
                - Secret Manager format:
                    - 'host': The host for the PostgreSQL database.
                    - 'username': The username for PostgreSQL authentication.
                    - 'password': The password for PostgreSQL authentication.
                    - 'port': The port for the PostgreSQL database.

        Raises:
            Exception: If the PostgreSQL connection cannot be established.
        """
        try:
            is_local = os.getenv("LOCALDEV", "false").lower() == "true"
            host = secret_data["POSTGRES_HOST" if is_local else "host"]
            user = secret_data["POSTGRES_USER" if is_local else "username"]
            password = secret_data["POSTGRES_PASSWORD" if is_local else "password"]
            port = secret_data["POSTGRES_PORT" if is_local else "port"]
            database = self.tenant  # Use tenant name as database name

            # Create a connection pool with min 1 and max 10 connections
            self.connection_pool = pool.SimpleConnectionPool(
                1,
                10,
                host=host,
                database=database,
                user=user,
                password=password,
                port=port,
            )

            # Test the connection
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    cur.fetchone()

        except Exception as e:
            logging.exception(
                "An unexpected error occurred while connecting to PostgreSQL"
            )
            self.connection_pool = None
            raise e

    def get_connection(self):
        """
        Gets a connection from the pool.

        Returns:
            psycopg2.extensions.connection: A database connection from the pool.
        """
        if not self.connection_pool:
            raise Exception("Connection pool not initialized")
        return self.connection_pool.getconn()

    def release_connection(self, conn):
        """
        Releases a connection back to the pool.

        Args:
            conn (psycopg2.extensions.connection): The connection to release.
        """
        if self.connection_pool:
            self.connection_pool.putconn(conn)

    def is_connected(self) -> bool:
        """
        Checks if the PostgreSQL connection pool is initialized.

        Returns:
            bool: True if connected, False otherwise.
        """
        return self.connection_pool is not None

    def close(self):
        """
        Closes all connections in the pool.
        """
        if self.connection_pool:
            self.connection_pool.closeall()
            self.connection_pool = None

    def execute_query(self, query: str, params: Optional[tuple] = None) -> Any:
        """
        Executes a query and returns the results.

        Args:
            query (str): The SQL query to execute.
            params (tuple, optional): Parameters for the query.

        Returns:
            Any: The query results.
        """
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(query, params)
                if cur.description:  # If the query returns results
                    return cur.fetchall()
                return None
