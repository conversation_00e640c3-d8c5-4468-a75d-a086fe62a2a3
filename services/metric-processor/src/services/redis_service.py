"""This module contains the RedisService class for interacting with a Redis database."""

import json
import os
import redis
from src.utils import GRAPH_AREA_CACHE_NAME, GRAPH_CACHE_NAME, GRAPH_EDGE_CACHE_NAME, GRAP<PERSON>_METRIC_CACHE_NAME
import structlog
import time
import re

logging = structlog.get_logger()

class RedisService:
    """
    A service class to interact with a Redis database.
    """

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        """
        Initializes the Redis client with configuration from environment variables.

        Configurations include:
            - `REDIS_HOST`: Hostname or IP of the Redis server.
            - `REDIS_PORT`: Port number of the Redis server.
            - `REDIS_DB`: Redis database index.
            - `REDIS_AUTH_STRING`: Optional authentication password for <PERSON><PERSON>.
        """
        if not hasattr(
            self, "redis_client"
        ):  # Ensure redis_client is only initialized once
            host = os.getenv("REDIS_HOST", "localhost")
            port = int(os.getenv("REDIS_PORT", "6379"))
            db = int(os.getenv("REDIS_DB", "0"))
            auth_string = os.getenv("REDIS_AUTH_STRING", "")
            self.redis_client = redis.StrictRedis(
                host=host, port=port, db=db, password=auth_string
            )

    def boolean_toggle(self, metric_name: str, **kwargs: dict[str]) -> None:
        """Toggles a boolean value in Redis to opposite of current value.

        Args:
            metric_name (str): _description_
            **kwargs: optional `ttl`.
        """
        ttl = kwargs.get("ttl", 3600)
        current_value = self.redis_client.get(metric_name, False)
        self.redis_client.set(metric_name, not current_value)
        self.redis_client.expire(metric_name, ttl)

    def cycle_time_start(self, metric_name: str, **kwargs: dict[str]) -> None:
        """
        Stores the start time of a cycle for the given metric name.
        The name_formula in the metric config for both the start and end metrics should match.
        This function appends ":start" to the end of the start metric.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Contains additional details, such as `event_timestamp` and `instance_id`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        instance_id = kwargs.get("instance_id", "")
        self.redis_client.hset(f"{metric_name}:start", instance_id, event_timestamp)

    def cycle_time_stop(self, metric_name: str, **kwargs: dict[str]) -> None:
        """
        Stores the duration of a cycle for the given metric name and removes stale records. Deletes the associated start time key.
        The name_formula in the metric config for both the start and end metrics should match.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Contains details such as `event_timestamp`, `instance_id`, and `ttl`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        instance_id = kwargs.get("instance_id", "")
        row_hash = kwargs.get("row_hash", "")
        ttl = kwargs.get("ttl", 3600)

        event_time_start = self.redis_client.hget(f"{metric_name}:start", instance_id)

        if event_time_start:
            cycle_time_duration = (event_timestamp - float(event_time_start)) / 60
            event_key = f"{row_hash}-{instance_id}_{cycle_time_duration}"
            # Store in ZSET with timestamp as the score and JSON as the value
            self.redis_client.zadd(metric_name, {event_key: event_timestamp})

            # Purge old records outside of TTL and remove the start time key.
            # Assumption: Shouldnt need to account for pipeline-delay here because we are
            # basing expiration time off of the event-time, which has same delay.
            self.redis_client.zremrangebyscore(metric_name, 0, event_timestamp - ttl)
            self.redis_client.hdel(f"{metric_name}:start", instance_id)

    def decr(self, metric_name, **kwargs):
        """
        Decrements the counter for the specified metric by 1.

        Args:
            metric_name (str): The name of the metric.
        """
        self.redis_client.decr(metric_name, 1)

    def distinct_item_count(
        self, metric_name: str, **kwargs: dict[str, float | int | str]
    ) -> None:
        """
        Maintains a distinct count of items for the specified metric over a rolling window. The item is a column signified by the identifier argument.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Includes `event_timestamp`, `ttl`, and `identifier`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        ttl = kwargs.get("ttl", 3600)
        identifier = str(kwargs.get("identifier", ""))

        if identifier != "":
            # Store in ZSET with timestamp as the score and `identifier` as the key
            # NOTE: Calling zadd() w/ same identifier multiple times overwrites existing datapoint,
            # so that keeps this list a distinct set based on `identifier`, and allows us to expire
            # old data to purge list of identifiers no longer being sent.
            self.redis_client.zadd(metric_name, {identifier: event_timestamp})
            # Purge old records that occurred `TTL` seconds before `event_timestamp`.
            self.redis_client.zremrangebyscore(metric_name, 0, event_timestamp - ttl)

    def distinct_item_subtract(
        self, metric_name: str, **kwargs: dict[str, float | int | str]
    ) -> None:
        """
        Removes one distinct item from the sorted set of items for the specified metric. The item to remove is based on the identifier keyword argument.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Includes `event_timestamp`, `ttl`, and `identifier`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        ttl = kwargs.get("ttl", 3600)
        identifier = str(kwargs.get("identifier", ""))

        if identifier != "":
            # Remove the item from the sorted set by its identifier.
            self.redis_client.zrem(metric_name, identifier)
            # Purge old records that occurred `TTL` seconds before `event_timestamp`.
            self.redis_client.zremrangebyscore(metric_name, 0, event_timestamp - ttl)

    def event_set(self, metric_name: str, **kwargs: dict[str]) -> None:
        """
        Stores an event and a value in a sorted set for rolling hourly counts.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Includes `event_timestamp`, `row_hash`, `ttl`, and `value`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        row_hash = kwargs.get("row_hash", "")
        ttl = kwargs.get("ttl", 3600)
        value = kwargs.get("value", "")

        event_key = f"{row_hash}_{value if value else 0}"
        # Store in ZSET with timestamp as the score and JSON as the value
        self.redis_client.zadd(metric_name, {event_key: event_timestamp})
        # Purge old records that occurred `TTL` seconds before `event_timestamp`.
        # Assumption: Shouldnt need to account for pipeline-delay here because we are
        # basing expiration time off of the event-time, which has same delay.
        self.redis_client.zremrangebyscore(metric_name, 0, event_timestamp - ttl)

    def complex_event_set(self, metric_name: str, **kwargs: dict[str]) -> None:
        """
        Stores one event within the sorted set for rolling hourly sums for each unique item that makes up the metric.
        Event consists of a timestamp, a unique identifier, and a value.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Includes `event_timestamp`, `ttl`, `identifier` and `value`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        identifier = str(kwargs.get("identifier", ""))
        value = str(kwargs.get("value", ""))
        ttl = kwargs.get("ttl", 3600)

        if identifier and value:
            # Combine the item identifier with the metric's item value and stringify to form the item key.
            # This lets us keep the value within the sorted set as the timestamp so that we can easily expire individual datapoints.
            # This also allows us to search for existing items by `identifier` to keep the set of items unique while also storing the items metric value.
            item_key = json.dumps({"identifier": identifier, "value": value})

            # Find and remove any existing entry for the identifier within this metric's sorted set.
            self._remove_existing_item_from_metric(metric_name, identifier)

            # Store in ZSET with timestamp as the score and JSON as the value
            self.redis_client.zadd(metric_name, {item_key: event_timestamp})

            # Purge old records that occurred `TTL` seconds before `event_timestamp`.
            self.redis_client.zremrangebyscore(metric_name, 0, event_timestamp - ttl)

    def fault_start(self, metric_name: str, **kwargs: dict[str]) -> None:
        """
        Registers the start of a fault event for the given metric name.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Contains `event_timestamp` and `fault_id`.
        """
        event_timestamp = kwargs.get("event_timestamp")
        fault_id = kwargs.get("fault_id")

        self.redis_client.hset(metric_name, fault_id, event_timestamp)

    def fault_end(self, metric_name: str, **kwargs: dict[str]) -> None:
        """
        Registers the end of a fault event and calculates its duration.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Contains `event_timestamp` and `fault_id`.
        """
        fault_id = kwargs.get("fault_id")
        event_timestamp = kwargs.get("event_timestamp")

        fault_start_time = self.redis_client.hget(metric_name, fault_id)
        fault_duration = event_timestamp - float(fault_start_time)
        self.redis_client.zadd(f"{metric_name}:durations", {fault_id: fault_duration})
        self.redis_client.hdel(metric_name, fault_id)

    def incr(self, metric_name, **kwargs):
        """
        Increments the counter for the specified metric by 1.

        Args:
            metric_name (str): The name of the metric.
        """
        self.redis_client.incr(metric_name, 1)

    def set_last_processed_time(self, tenant):
        """Store the given event timestamp as the last processed time with a 1-hour expiration. Each facility has its own last processed time."""
        metric_name = f"{tenant}:last_processed_time"
        self.redis_client.set(metric_name, time.time())

    def storage_location_distribution_available(
        self, metric_name: str, **kwargs: dict[str]
    ) -> None:
        """
        Stores the total number of available storage locations.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Contains `empty_location_position_count` and optional `ttl`.
        """
        empty_location_position_count = float(
            kwargs.get("empty_location_position_count")
        )
        self.redis_client.set(metric_name, empty_location_position_count)
        self.redis_client.expire(metric_name, kwargs.get("ttl", 3600))

    def storage_location_distribution_occupied(
        self,
        metric_name: str,
        empty_location_position_count: float,
        total_location_position_count: float,
        **kwargs: dict[str],
    ) -> None:
        """
        Stores the total number of occupied storage locations without expiration.

        Args:
            metric_name (str): The name of the metric.
            empty_location_position_count (float): The number of empty locations.
            total_location_position_count (float): The total number of locations.
            **kwargs: Optional parameters, including `ttl`.
        """
        available_locations = float(empty_location_position_count)
        occupied_locations = float(total_location_position_count) - available_locations
        self.redis_client.set(metric_name, occupied_locations)
        self.redis_client.expire(metric_name, kwargs.get("ttl", 3600))

    def storage_utilization(
        self,
        metric_name: str,
        empty_location_position_count: float,
        total_location_position_count: float,
        **kwargs: dict[str],
    ) -> None:
        """
        Stores the percentage of occupied storage locations with expiration.

        Args:
            metric_name (str): The name of the metric.
            empty_location_position_count (float): The number of empty locations.
            total_location_position_count (float): The total number of locations.
            **kwargs: Optional parameters, including `ttl`.
        """
        # Calculate percent of locations that are occupied.
        occupied_location_position_count = float(total_location_position_count) - float(
            empty_location_position_count
        )
        percent_occupied = (
            occupied_location_position_count / float(total_location_position_count)
        ) * 100.0
        # Store percent metric in Redis with expiration time.
        self.redis_client.set(metric_name, percent_occupied)
        self.redis_client.expire(metric_name, kwargs.get("ttl", 3600))

    def store_handling_unit_for_edge(
        self,
        handling_unit_key,
        leave_time,
        outbound_area,
        units,
        outbound_node_label,
        **kwargs,
    ):
        """
        Stores a handling unit in Redis for tracking edge metrics.

        Args:
            handling_unit_key (str): Redis key for the handling unit.
            leave_time (str): The time the unit left the area.
            outbound_area (str): The node the unit leaving.
            units (int): Number of units associated with the handling unit.
            outbound_node_label (str): The label for the outbound node.
            **kwargs: Additional arguments including outbound_parent_nodes.
        """
        value = {
            "leave_time": leave_time,
            "outbound_area": outbound_area,
            "units": units,
            "outbound_node_label": outbound_node_label,
        }

        # Add outbound_parent_nodes if it exists
        if "outbound_parent_nodes" in kwargs:
            value["outbound_parent_nodes"] = str(kwargs["outbound_parent_nodes"])

        self.redis_client.hmset(handling_unit_key, value)

    def store_static_value(self, metric_name: str, **kwargs: dict[str]) -> None:
        """Stores a static value in a simple Redis key w/ expiration.
        Args:
            metric_name (str): _description_
            **kwargs: value and optional `ttl`.
        """
        ttl = kwargs.get("ttl", 3600)
        value = kwargs.get("value")
        if value:
            self.redis_client.set(metric_name, value)
            self.redis_client.expire(metric_name, ttl)


    def total_storage_locations_occupied(
        self, metric_name: str, **kwargs: dict[str]
    ) -> None:
        """
        Stores the total number of available storage locations in each aisle for the entire miniload/multishuttle.

        Args:
            metric_name (str): The name of the metric.
            **kwargs: Contains `empty_location_position_count`, `total_location_position_count` and optional `ttl`.
        """
        aisle_code = kwargs.get("aisle_code", "")
        empty_location_position_count = kwargs.get("empty_location_position_count", "")
        total_location_position_count = kwargs.get("total_location_position_count", "")
        if (
            aisle_code
            and empty_location_position_count
            and total_location_position_count
        ):
            occupied_location_position_count = str(
                int(total_location_position_count) - int(empty_location_position_count)
            )
            self.complex_event_set(
                metric_name,
                value=occupied_location_position_count,
                identifier=aisle_code,
                **kwargs,
            )

    def add_area_to_cache(self, area_key: str, tenant: str):
        """
        Adds an area to the Redis area cache set.

        Args:
            area_key (str): The key representing the area to add.
        """
        key = f"{tenant}:{GRAPH_AREA_CACHE_NAME}"
        self.redis_client.sadd(key, area_key)
        self.set_cache_key_expiration(key)

    def is_area_in_cache(self, area_key: str, tenant: str) -> bool:
        """
        Checks if an area is present in the Redis area cache set.

        Args:
            area_key (str): The key representing the area to check.

        Returns:
            bool: True if the area is in the cache, False otherwise.
        """
        return self.redis_client.sismember(f"{tenant}:{GRAPH_AREA_CACHE_NAME}", area_key)
    
    def add_edge_to_cache(self, edge_id: str, tenant: str):
        """
        Adds an edge to the Redis edge cache set.

        Args:
            edge_key (str): The key representing the edge to add.
        """
        key = f"{tenant}:{GRAPH_EDGE_CACHE_NAME}"
        self.redis_client.sadd(key, edge_id)
        self.set_cache_key_expiration(key)

    def is_edge_in_cache(self, edge_key: str, tenant: str) -> bool:
        """
        Checks if an edge is present in the Redis edge cache set.

        Args:
            edge_key (str): The key representing the edge to check.

        Returns:
            bool: True if the edge is in the cache, False otherwise.
        """
        return self.redis_client.sismember(f"{tenant}:{GRAPH_EDGE_CACHE_NAME}", edge_key)

    def add_metric_to_cache(self, metric_name: str, tenant: str):
        """
        Adds a metric to the Redis metric cache set.

        Args:
            metric_name (str): The name of the metric to add.
        """
        key = f"{tenant}:{GRAPH_METRIC_CACHE_NAME}"
        self.redis_client.sadd(key, metric_name)
        self.set_cache_key_expiration(key)

    def is_metric_in_cache(self, metric_name: str, tenant: str) -> bool:
        """
        Checks if a metric is present in the Redis metric cache set.

        Args:
            metric_name (str): The name of the metric to check.

        Returns:
            bool: True if the metric is in the cache, False otherwise.
        """
        return self.redis_client.sismember(f"{tenant}:{GRAPH_METRIC_CACHE_NAME}", metric_name)

    def clear_cache(self, scope: str, tenant: str) -> bool:
        """
        Clears keys from Redis for tenant, and based on scope (graph, metrics or all)

        Args:
            scope (str): What to clear in Redis (graph, metrics, all)
            tenant (str): The tenant of the cache being cleared

        Returns:
            bool: True if the flush was successful
        """

        # Delete all keys that store the nodes and edges that exist in Neo4j.
        if scope in ('all', 'graph'):
            self.clear_cached_graph(tenant, 'all')

        # Delete all keys that store metric values.
        if scope in ('all', 'metrics'):
            self.clear_metric_values(tenant)

        # Now that cached nodes/edges/metrics/metric values have been deleted for this tenant,
        # we can delete the cached graph responses (used by API and UI to render the graph).
        self.clear_cached_graph_responses(tenant)

        return True

    def clear_cached_graph(self, tenant: str, cache_to_delete: str) -> None:
        """
        Deletes keys from Redis for tenant which store nodes and edges that exist in Neo4j.
        Metric Processor checks this cache before creating new nodes and edges.
        Once deleted, Metric Processor will recreate the nodes and edges as data is processed.

        Args:
            tenant (str): The tenant of the cache being cleared
        """
        graph_caches = {
            'area': f"{tenant}*{GRAPH_AREA_CACHE_NAME}", 
            'edge': f"{tenant}*{GRAPH_EDGE_CACHE_NAME}",
            'metric': f"{tenant}*{GRAPH_METRIC_CACHE_NAME}",
        }
        for cache_type, pattern in graph_caches.items():
            if cache_to_delete == "all" or cache_to_delete == cache_type:
                for key in self.redis_client.scan_iter(match=pattern):
                    key_str = key.decode("utf-8")
                    logging.debug(f"Removing {cache_type} cache: {key_str}")
                    self.redis_client.delete(key_str)

    def clear_metric_values(self, tenant: str) -> None:
        """
        Deletes keys from Redis for tenant which store actual metric values.
        Metric Processor will begin recalculating metric values as data is processed.

        Args:
            tenant (str): The tenant that owns the metric values being deleted.
        """
        exclude_substrings = [
            GRAPH_AREA_CACHE_NAME,
            GRAPH_EDGE_CACHE_NAME,
            GRAPH_METRIC_CACHE_NAME,
            GRAPH_CACHE_NAME,
            "last_processed_time"
        ]

        for key in self.redis_client.scan_iter(match=f"{tenant}*"):
            metric_key = key.decode("utf-8")
            # Delete all tenant's keys in Redis except for keys in `exclude_substrings`
            if not any(sub in metric_key for sub in exclude_substrings):
                logging.debug(f"Removing metric: {metric_key}")
                self.redis_client.delete(metric_key)

    def clear_cached_graph_responses(self, tenant: str) -> None:
        """
        Deletes keys from Redis for tenant which store previous graph responses from Neo4j.
        These caches store the nodes, edges fetched in previous GET requests.
        Once cleared, subsequent GET requests will fetch data from Neo4j, instead of from cache.

        Args:
            tenant (str): The tenant that owns the graph caches that are being deleted.
        """
        for key in self.redis_client.scan_iter(match=f"{tenant}*{GRAPH_CACHE_NAME}"):
            key_str = key.decode("utf-8")
            logging.debug(f"Removing cached graph response: {key_str}")
            self.redis_client.delete(key_str)

    def _remove_existing_item_from_metric(
        self, metric_name: str, identifier: str
    ) -> None:
        """
        Removes the existing entry for a specific item from the sorted set.

        Args:
            metric_name (str): The name of the metric.
            identifier (str): The unique identifier of one of the items stored in this metric.
        """
        # Scan through all members to find the event entry for this item.
        members = self.redis_client.zrange(metric_name, 0, -1)
        for member in members:
            event_data = json.loads(member.decode())
            if event_data.get("identifier", "") == identifier:
                # Remove the event for this item from the sorted set.
                self.redis_client.zrem(metric_name, member)
                break

    def set_cache_key_expiration(self, key: str):
        """
        Sets the expiration time for keys used to store cached values.

        Args:
            key (str): The name of the key stored in Redis.
        """
        # Only set expiry if key has no expiration, otherwise
        # expiration time gets continuously updated.
        if self.redis_client.ttl(key) == -1:
            self.redis_client.expire(key, 1800)
