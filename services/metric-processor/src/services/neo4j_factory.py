import os
import json
import structlog
from google.cloud import secretmanager
from src.services.neo4j_service import Neo4jService

logging = structlog.get_logger()


class Neo4jFactory:
    """
    A factory class to create and manage Neo4jService instances for different tenants.
    """

    _instances = {}

    @classmethod
    def get_instance(cls, tenant, facility, redis_service):
        if tenant not in cls._instances:
            secret_data = cls._fetch_secret(tenant, facility)
            if secret_data:
                instance = Neo4jService(secret_data, redis_service)
                if instance.is_connected():
                    cls._instances[tenant] = instance
        return cls._instances.get(tenant)

    @classmethod
    def _fetch_secret(cls, tenant, facility):
        if os.getenv("LOCALDEV", "false").lower() == "true":
            # Use environment variables for local development
            secret_data = {
                "NEO4J_URI": os.getenv("NEO4J_URI"),
                "NEO4J_USERNAME": os.getenv("NEO4J_USERNAME"),
                "NEO4J_PASSWORD": os.getenv("NEO4J_PASSWORD"),
            }
            return secret_data

        try:
            secret_client = secretmanager.SecretManagerServiceClient()
            secret_name = f"{tenant}_{facility}_AuraDB"
            project_id = os.getenv("PROJECT_ID")
            secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
            secret_response = secret_client.access_secret_version(name=secret_path)
            secret_payload = secret_response.payload.data.decode("UTF-8")
            return json.loads(secret_payload)
        except json.JSONDecodeError:
            logging.exception(f"Failed to decode JSON from secret {secret_name}")
        except Exception:
            logging.exception(f"An unexpected error occurred while fetching secret")
        return None

    @classmethod
    def close_instance(cls, tenant):
        if tenant in cls._instances:
            cls._instances[tenant].close()
            del cls._instances[tenant]

    @classmethod
    def clear_cache(cls):
        cls._instances = {}
