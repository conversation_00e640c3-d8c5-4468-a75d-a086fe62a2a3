"""
This module contains the Neo4jService class for interacting with a Neo4j graph database.

The Neo4jService class provides methods to create and manage area nodes, area edges,
and their associated metrics in a Neo4j graph database. The service leverages a Redis
cache to optimize performance and ensure idempotent operations.
"""

import re
import time
import logging
from typing import List, Optional
import structlog
from neo4j import GraphDatabase
from .redis_service import RedisService  # Import RedisService

logging = structlog.get_logger()


def _get_parent_label_for_child_label(child_label):
    parent_label_map = {
        "Aisle": "Area",
        "Level": "Aisle",
        "Station": "Area",
        "StationLocation": "Station",
        "Shuttle": "Level",
    }
    return parent_label_map.get(child_label, "Area")

class Neo4jService:
    """
    A service class to interact with a Neo4j graph database.

    Attributes:
        redis_service (RedisService): An instance of RedisService to manage cache operations.
        driver (neo4j.Driver): A driver instance for connecting to the Neo4j database.
    """

    def __init__(self, secret_data, redis_service: RedisService):
        """
        Initializes the Neo4jService instance.

        Args:
            secret_data (dict): A dictionary containing Neo4j connection details:
                - 'NEO4J_URI': The URI for the Neo4j database.
                - 'NEO4J_USERNAME': The username for Neo4j authentication.
                - 'NEO4J_PASSWORD': The password for Neo4j authentication.
            redis_service (RedisService): An instance of RedisService for caching operations.
        """
        self.redis_service = redis_service
        self.driver = None
        self._initialize_driver(secret_data)
        self._initialize_graph()

    def _initialize_graph(self):
        """
        Creates all Neo4j database objects needed to start creating nodes and edges.
        """
        logging.debug("Initializing Neo4j graph...")
        with self.driver.session() as session:
            session.write_transaction(self._create_constraints)

    def _initialize_driver(self, secret_data):
        """
        Initializes the Neo4j driver using the provided secret data.

        Args:
            secret_data (dict): A dictionary containing the Neo4j connection parameters.

        Raises:
            Exception: If the Neo4j connection cannot be established.
        """
        try:
            URI = secret_data["NEO4J_URI"]
            username = secret_data["NEO4J_USERNAME"]
            password = secret_data["NEO4J_PASSWORD"]
            AUTH = (username, password)

            self.driver = GraphDatabase.driver(URI, auth=AUTH)
            self.driver.verify_connectivity()
        except Exception:
            logging.exception("An unexpected error occurred while connecting to Neo4j")
            self.driver = None

    def is_connected(self) -> bool:
        """
        Checks if the Neo4j driver is connected.

        Returns:
            bool: True if connected, False otherwise.
        """
        return self.driver is not None

    def close(self):
        """
        Closes the Neo4j driver connection.
        """
        if self.driver:
            self.driver.close()
            self.driver = None

    def area_node(self, metric_name, view, **kwargs):
        """
        Creates an area node in the Neo4j database if it does not already exist and
        associates a metric with it. The method ensures no duplicate entries by using
        Redis caching.

        Args:
            metric_name (str): The name of the metric to associate with the area node.
            view (str): The graph view to associate with the area node.
        """
        # Parse metric name to extract tenant, facility, and area
        parts = metric_name.split(":")
        tenant_id, facility_id, area_name = parts[0], parts[1], parts[2]
        area_key = f"{tenant_id}:{facility_id}:{area_name}"
        label = kwargs.get("label", "Area")

        # Get parent_nodes from kwargs - they should already be normalized in MetricProcessor
        parent_nodes = kwargs.get("parent_nodes")

        # Check if area has been created, if not, create it
        if not self.redis_service.is_area_in_cache(area_key, tenant_id):
            try:
                start_time = time.time()
                with self.driver.session() as session:
                    # Always create parent nodes first, even if they're not in the current view
                    if parent_nodes:
                        session.write_transaction(
                            self._create_parent_nodes, label, parent_nodes, created_by=kwargs.get("metric_config_name")
                        )
                    session.write_transaction(
                        self._create_area_node,
                        view,
                        area_name,
                        label,
                        parent_nodes,
                        created_by=kwargs.get("metric_config_name"),
                    )
                self.redis_service.add_area_to_cache(area_key, tenant_id)
                end_time = time.time()
                logging.debug(
                    "Created area node for %s in %s seconds",
                    area_key,
                    end_time - start_time,
                )
            except Exception:
                logging.exception("Error creating area node for %s", area_key)

        # Add metric to area node, ensuring no duplication
        if not self.redis_service.is_metric_in_cache(metric_name, tenant_id):
            logging.info("Adding metric %s to area node %s", metric_name, area_key)
            metric_units = kwargs.get("metric_units", "")
            try:
                start_time = time.time()
                with self.driver.session() as session:
                    session.write_transaction(
                        self._add_metric_to_area_node,
                        view,
                        area_name,
                        metric_name,
                        label,
                        parent_nodes,
                        metric_units,
                        metric_config_name=kwargs.get("metric_config_name"),
                    )
                self.redis_service.add_metric_to_cache(metric_name, tenant_id)
                end_time = time.time()
                logging.debug(
                    "Added metric %s to area node %s in %s seconds",
                    metric_name,
                    area_key,
                    end_time - start_time,
                )
            except Exception:
                logging.exception(
                    "Error adding metric %s to area node %s", metric_name, area_key
                )

    def shuttle_node(self, metric_name, view, **kwargs):
        """
        Creates a Shuttle node inside an Aisle Level by extracting values from `metric_name`.

        - Extracts `shuttle_id` from `metric_name` using `parts`. Shuttle id expects to be extracted from the third member of the list.
           - Like so: "{tenant}:{facility}:{shuttle_code}:empty_totes_shuttle_shuffle_movements:60min_set:count"
        - Extracts `aisle_code`, `view`, and `shuttle_code` from `shuttle_id`.
        - Ensures nodes are uniquely named to avoid cross-aisle duplicates.

        Args:
            metric_name (str): The formatted metric name containing encoded values.
            view (str): The view inside one specific aisle (name of aisle already extracted earlier).
        """

        # Extract values from metric_name
        parts = metric_name.split(":")
        tenant_id, facility_id = parts[0], parts[1]
        shuttle_id = parts[2]  # Now this is the shuttle code since we removed view

        # Extract `aisle_code`, `view`, and `shuttle_code` from `shuttle_id`
        aisle_match = re.match(r"^(MSAI\d+)(LV\d+)(SH\d+)$", shuttle_id)

        if not aisle_match:
            logging.error(f"Invalid shuttle ID format: {shuttle_id}")
            return

        aisle_code = aisle_match.group(1)  # Extract aisle code from shuttle ID
        aisle_level_id = f"{aisle_code}_{aisle_match.group(2)}"  # "MSAI01_LV01"
        shuttle_node_id = f"{aisle_code}_{aisle_match.group(2)}_{aisle_match.group(3)}"  # "MSAI01_LV01_SH01"
        shuttle_key = (
            f"{tenant_id}:{facility_id}:{view}:{aisle_level_id}:{shuttle_node_id}"
        )

        parent_nodes = ["multishuttle"]
        if not self.redis_service.is_area_in_cache(shuttle_key, tenant_id):
            try:
                with self.driver.session() as session:
                    # Ensure the Aisle Node exists inside the Graph Level
                    session.write_transaction(
                        self._create_area_node,
                        view,
                        aisle_code,
                        label="Aisle",
                        parent_nodes=parent_nodes,
                        created_by=kwargs.get("metric_config_name"),
                    )

                    # Ensure the Aisle Level Node exists inside the Aisle
                    session.write_transaction(
                        self._create_or_get_aisle_level, aisle_code, aisle_level_id, created_by=kwargs.get("metric_config_name")
                    )

                    # Ensure the Shuttle Node exists inside the Aisle Level
                    session.write_transaction(
                        self._create_shuttle_node,
                        view,
                        aisle_level_id,
                        shuttle_node_id,
                        created_by=kwargs.get("metric_config_name"),
                    )
                self.redis_service.add_area_to_cache(shuttle_key, tenant_id)

            except Exception:
                logging.exception(
                    "Error creating shuttle node %s inside aisle level %s",
                    shuttle_node_id,
                    aisle_level_id,
                )

        # Add metric to shuttle node, ensuring no duplication
        if not self.redis_service.is_metric_in_cache(metric_name, tenant_id):
            logging.info(
                "Adding metric %s to shuttle node %s", metric_name, shuttle_key
            )
            try:
                start_time = time.time()
                metric_units = kwargs.get("metric_units", "")
                with self.driver.session() as session:
                    session.write_transaction(
                        self._add_metric_to_shuttle_node,
                        view,
                        aisle_level_id,
                        shuttle_node_id,
                        metric_name,
                        metric_units,
                        metric_config_name=kwargs.get("metric_config_name"),
                    )
                self.redis_service.add_metric_to_cache(metric_name, tenant_id)
                end_time = time.time()
                logging.debug(
                    "Added metric %s to shuttle node %s in %s seconds",
                    metric_name,
                    shuttle_key,
                    end_time - start_time,
                )
            except Exception:
                logging.exception(
                    "Error adding metric %s to shuttle node %s",
                    metric_name,
                    shuttle_key,
                )

    def area_edge(self, metric_name, view, **kwargs):
        """
        Creates an edge between two area nodes in the Neo4j database if it does not
        already exist and associates a metric with it. The method ensures no duplicate
        entries by using Redis caching.

        Args:
            metric_name (str): The name of the metric to associate with the edge.
            view (str): The graph view to associate with the area edge.
        """
        # Parse metric name to extract tenant, facility, source area, destination area, and metric
        parts = metric_name.split(":")
        tenant_id, facility_id, source_area, destination_area = (
            parts[0],
            parts[1],
            parts[2],
            parts[3],
        )
        source_area_key = f"{tenant_id}:{facility_id}:{source_area}"
        destination_area_key = f"{tenant_id}:{facility_id}:{destination_area}"
        label = kwargs.get("label")
        outbound_node_label = kwargs.get("outbound_node_label")

        # Get parent nodes from kwargs - they should already be normalized in MetricProcessor
        outbound_parent_nodes = kwargs.get("outbound_parent_nodes")
        inbound_parent_nodes = kwargs.get("inbound_parent_nodes")

        # Add debug logging for parent nodes
        logging.debug(
            "Area edge creation parameters",
            metric_name=metric_name,
            view=view,
            source_area=source_area,
            destination_area=destination_area,
            outbound_parent_nodes=outbound_parent_nodes,
            inbound_parent_nodes=inbound_parent_nodes,
            outbound_node_label=outbound_node_label,
            label=label,
        )

        # Add debug logging for "r" node creation
        if source_area == "r" or destination_area == "r":
            logging.warning(
                "Attempting to create 'r' node in area_edge!",
                metric_name=metric_name,
                view=view,
                source_area=source_area,
                destination_area=destination_area,
                outbound_parent_nodes=outbound_parent_nodes,
                inbound_parent_nodes=inbound_parent_nodes,
                stack_trace=True,
            )

        # Ensure source and destination areas are created
        if not self.redis_service.is_area_in_cache(source_area_key, tenant_id):
            logging.debug(
                "Creating source area node",
                source_area_key=source_area_key,
                outbound_parent_nodes=outbound_parent_nodes,
                outbound_node_label=outbound_node_label,
            )
            with self.driver.session() as session:
                if outbound_parent_nodes:
                    session.write_transaction(
                        self._create_parent_nodes,
                        label,
                        outbound_parent_nodes, 
                        created_by=kwargs.get("metric_config_name")
                    )
                session.write_transaction(
                    self._create_area_node,
                    view,
                    source_area,
                    label=outbound_node_label,
                    parent_nodes=outbound_parent_nodes,
                    created_by=kwargs.get("metric_config_name"),
                )
            self.redis_service.add_area_to_cache(source_area_key, tenant_id)

        if not self.redis_service.is_area_in_cache(destination_area_key, tenant_id):
            logging.debug(
                "Creating destination area node",
                destination_area_key=destination_area_key,
                inbound_parent_nodes=inbound_parent_nodes,
                label=label,
            )
            with self.driver.session() as session:
                if inbound_parent_nodes:
                    session.write_transaction(
                        self._create_parent_nodes,
                        label,
                        inbound_parent_nodes, 
                        created_by=kwargs.get("metric_config_name")
                    )
                session.write_transaction(
                    self._create_area_node,
                    view,
                    destination_area,
                    label=label,
                    parent_nodes=inbound_parent_nodes,
                    created_by=kwargs.get("metric_config_name"),
                )
            self.redis_service.add_area_to_cache(destination_area_key, tenant_id)

        # Add metric to the edge, ensuring no duplication of the edge or the metric.
        if (
            not self.redis_service.is_edge_in_cache(metric_name, tenant_id) or 
            not self.redis_service.is_metric_in_cache(metric_name, tenant_id)
        ):
            logging.info(
                "Creating edge between %s and %s and metric %s",
                source_area_key,
                destination_area_key,
                metric_name,
            )
            metric_units = kwargs.get("metric_units", "")
            with self.driver.session() as session:
                session.write_transaction(
                    self._create_edge,
                    view,
                    source_area,
                    destination_area,
                    metric_name,
                    label,
                    metric_units,
                    outbound_node_label,
                    metric_config_name=kwargs.get("metric_config_name"),
                )
            self.redis_service.add_edge_to_cache(metric_name, tenant_id)
            self.redis_service.add_metric_to_cache(metric_name, tenant_id)

    @staticmethod
    def _create_constraints(tx):
        """
        Creates constraints in Neo4j instance to enforce uniqueness among nodes.

        Args:
            tx (_type_): _description_tx: Neo4j transaction object.
        """
        logging.info("Creating Neo4j constraints...")
        # Aisle
        tx.run(
            "CREATE CONSTRAINT unique_aisle IF NOT EXISTS FOR (aisle:Aisle) REQUIRE aisle.name IS UNIQUE;"
        )
        # AisleLevel
        tx.run(
            "CREATE CONSTRAINT unique_aisle_level IF NOT EXISTS FOR (aisle_level:Level) REQUIRE aisle_level.name IS UNIQUE;"
        )
        # Area
        tx.run(
            "CREATE CONSTRAINT unique_area IF NOT EXISTS FOR (area:Area) REQUIRE area.name IS UNIQUE;"
        )
        # Lift
        tx.run(
            "CREATE CONSTRAINT unique_lift IF NOT EXISTS FOR (lift:Lift) REQUIRE lift.name IS UNIQUE;"
        )
        # Metric
        tx.run(
            "CREATE CONSTRAINT unique_metric IF NOT EXISTS FOR (metric:Metric) REQUIRE metric.name IS UNIQUE;"
        )
        # Shuttle
        tx.run(
            "CREATE CONSTRAINT unique_shuttle IF NOT EXISTS FOR (shuttle:Shuttle) REQUIRE shuttle.name IS UNIQUE;"
        )
        # Station (workstation)
        tx.run(
            "CREATE CONSTRAINT unique_station IF NOT EXISTS FOR (station:Station) REQUIRE station.name IS UNIQUE;"
        )

    @staticmethod
    def _create_or_get_facility(tx, facility_id):
        query = "MERGE (f:Facility {FacilityID: $facility_id}) RETURN f"
        tx.run(query, facility_id=facility_id)

    @staticmethod
    def _create_or_get_view_node(tx, view):
        query = "MERGE (v:View {name: $view}) RETURN v"
        tx.run(query, view=view)

    @staticmethod
    def _create_parent_nodes(tx, child_label, parent_nodes, created_by=None):
        """
        Creates parent nodes in sequence with CONTAINS relationships.
        Parent nodes should already be normalized in MetricProcessor.

        Args:
            tx: Neo4j transaction object
            parent_nodes: List of parent node names in hierarchical order
            created_by: The metric config name that created these nodes
        """
        if not parent_nodes or len(parent_nodes) == 0:
            return
        
        parent_node_label = _get_parent_label_for_child_label(child_label)
        
        # Build the query using a list comprehension for cleaner code
        # NOTE: Though technically this is looping through a list of parent_nodes. At the time of writing,
        # there are no metric configs with more than one parent node, so this logic holds up until refactor.
        node_creations = [
            f"""
            MERGE (n{i}:{parent_node_label} {{name: $node_name{i}}})
            ON CREATE SET n{i}.views = [],
                n{i}.created_by = $created_by
            ON MATCH SET n{i}.views = coalesce(n{i}.views, [])
            WITH {', '.join(f'n{j}' for j in range(i + 1))}
            """
            for i in range(len(parent_nodes))
        ]

        # Build the relationships with created_by property only on creation
        relationships = [
            f"""
            MERGE (n{i-1})-[:CONTAINS]->(n{i})
            ON CREATE SET n{i}.created_by = $created_by
            WITH {', '.join(f'n{j}' for j in range(i + 1))}
            """
            for i in range(1, len(parent_nodes))
        ]

        # Combine all parts into the final query
        query = f"""
        {"".join(node_creations)}
        {"".join(relationships)}
        RETURN {', '.join(f'n{i}' for i in range(len(parent_nodes)))}
        """

        # Create parameters dictionary
        params = {f"node_name{i}": name for i, name in enumerate(parent_nodes)}
        params["created_by"] = created_by

        # Execute the query
        tx.run(query, **params)

    @staticmethod
    def _create_area_node(
        tx,
        view: str,
        area_name: str,
        label: str,
        parent_nodes: Optional[List[str]] = None,
        created_by: Optional[str] = None,
    ):
        """
        Creates an area node and connects it to the last parent node in the chain.
        If there are no parent nodes, creates the node without any parent connection.

        Args:
            tx: Neo4j transaction object
            view: The view to associate with the node
            area_name: The name of the area node
            label: The label for the node
            parent_nodes: Optional list of parent nodes in hierarchical order
            created_by: Optional metric name that created this node
        """
        
        # If parent_nodes is provided and not empty, connect to the last parent
        if parent_nodes and len(parent_nodes) > 0:
            last_parent = parent_nodes[-1]
            parent_label = _get_parent_label_for_child_label(label)
            query = f"""
                MATCH (parent:`{parent_label}` {{name: $last_parent}})
                MERGE (node:`{label}` {{name: $area_name}})
                MERGE (parent)-[r:CONTAINS]->(node)
                ON CREATE SET 
                    node.views = [$view],
                    node.created_by = $created_by,
                    parent.created_by = $created_by,
                    r.created_by = $created_by
                ON MATCH SET 
                    node.views = CASE 
                        WHEN node.views IS NULL THEN [$view]
                        WHEN NOT $view IN node.views THEN node.views + $view 
                        ELSE node.views 
                    END,
                    r.created_by = CASE
                        WHEN r.created_by IS NULL THEN $created_by
                        ELSE r.created_by
                    END
                RETURN node
            """
        else:
            # Create node without parent connection
            query = f"""
                MERGE (node:`{label}` {{name: $area_name}})
                ON CREATE SET 
                    node.views = [$view],
                    node.created_by = $created_by
                ON MATCH SET 
                    node.views = CASE 
                        WHEN node.views IS NULL THEN [$view]
                        WHEN NOT $view IN node.views THEN node.views + $view 
                        ELSE node.views 
                    END
                RETURN node
            """
            last_parent = None

        tx.run(
            query,
            view=view,
            area_name=area_name,
            last_parent=last_parent,
            created_by=created_by,
        )

    def _add_metric_to_area_node(
        self, tx, view, area_name, metric_name, label, parent_nodes, metric_units="", metric_config_name=None
    ):
        """
        Adds a metric to an area node by creating a Metric node and connecting it with a HAS_METRIC relationship.
        Ensures parent nodes exist by using _create_parent_nodes.

        Args:
            tx: Neo4j transaction object
            view (str): The view to associate with the metric
            area_name (str): The name of the area node
            metric_name (str): The name of the metric
            label (str): The label of the area node
            parent_nodes (List[str]): List of parent nodes in hierarchical order
            metric_units (str): The units that a metric value represents (e.g. "totes/hour")
            metric_config_name (str): The name of the metric config that created this node
        """
        # First ensure parent nodes exist
        self._create_parent_nodes(tx, label, parent_nodes, created_by=metric_config_name)

        # Create metric node and connect to area
        metric_query = f"""
            MATCH (a:`{label}` {{name: $area_name}})
            MERGE (m:Metric {{name: $metric_name, units: $metric_units}})
            ON CREATE SET m.created_by = $created_by, a.created_by = $created_by
            WITH a, m
            CALL apoc.lock.nodes([a])
            WITH a, m
            SET a.views = CASE
                WHEN a.views IS NULL THEN [$view]
                WHEN NOT $view IN a.views THEN a.views + $view
                ELSE a.views
            END
            MERGE (a)-[r:HAS_METRIC]->(m)
            ON CREATE SET 
                r.views = [$view],
                r.created_by = $created_by
            ON MATCH SET 
                r.views = CASE 
                    WHEN NOT $view IN r.views THEN r.views + $view 
                    ELSE r.views 
                END,
                r.created_by = CASE
                    WHEN r.created_by IS NULL THEN $created_by
                    ELSE r.created_by
                END
            RETURN a, m, r
        """
        tx.run(metric_query, view=view, area_name=area_name, metric_name=metric_name, metric_units=metric_units, created_by=metric_config_name)

    @staticmethod
    def _create_edge(
        tx,
        view,
        source_area,
        destination_area,
        metric_name,
        label,
        metric_units,
        outbound_node_label,
        metric_config_name=None,
    ):
        """
        Creates a CONNECTS_TO relationship between two area nodes and stores the metric information
        directly on the relationship.

        Args:
            tx: Neo4j transaction object
            view (str): The view to associate with the edge
            source_area (str): The name of the source area node
            destination_area (str): The name of the destination area node
            metric_name (str): The name of the metric
            label (str): The label for the destination node
            metric_units (str): The units that a metric value represents
            outbound_node_label (str): The label for the source node
            metric_config_name (str): The name of the metric config that created this edge
        """
        query = f"""
            MATCH (sa:`{outbound_node_label}` {{name: $source_area}})
            MATCH (da:`{label}` {{name: $destination_area}})
            WHERE sa <> da
            CALL apoc.lock.nodes([sa, da])
            WITH sa, da  
            MERGE (sa)-[r:CONNECTS_TO]->(da)
            ON CREATE SET 
                r.metrics = [$metric_name],
                r.views = [$view],
                r.name = $metric_name,
                r.units = $metric_units,
                r.created_by = $created_by
            ON MATCH SET 
                r.metrics = CASE 
                    WHEN NOT $metric_name IN coalesce(r.metrics, []) 
                    THEN coalesce(r.metrics, []) + $metric_name 
                    ELSE r.metrics 
                END,
                r.views = CASE 
                    WHEN NOT $view IN coalesce(r.views, []) 
                    THEN coalesce(r.views, []) + $view 
                    ELSE r.views 
                END,
                r.name = $metric_name
            RETURN r
        """
        tx.run(
            query,
            view=view,
            source_area=source_area,
            destination_area=destination_area,
            metric_name=metric_name,
            label=label,
            metric_units=metric_units,
            outbound_node_label=outbound_node_label,
            created_by=metric_config_name,
        )

    @staticmethod
    def _create_metric_node(tx, metric_name, metric_units="", metric_config_name=""):
        """
        Creates a metric node if it doesn't exist.

        Args:
            tx: Neo4j transaction object
            metric_name (str): The name of the metric
            metric_units (str): The units that a metric value represents (e.g. "totes/hour")
            metric_config_name (str): The name of the metric config that created this node
        """
        query = """
            MERGE (m:Metric {name: $metric_name, units: $metric_units})
            ON CREATE SET m.created_by = $created_by
            RETURN m
        """
        tx.run(query, metric_name=metric_name, metric_units=metric_units, created_by=metric_config_name)

    @staticmethod
    def _create_or_get_aisle_level(tx, aisle_node_id, aisle_level_id, created_by=None):
        """
        Ensures the Aisle Level node exists inside an Aisle.

        - This node represents a **specific level** inside the aisle.
        - Aisle Level nodes do NOT have a views property.

        Args:
            tx: Neo4j transaction object.
            aisle_node_id (str): The parent Aisle node (e.g., "MSAI01").
            aisle_level_id (str): The Level node inside the aisle (e.g., "MSAI01_LV01").
            created_by (str): The metric config name that created this node
        """
        query = """
            MATCH (a:Aisle {name: $aisle_node_id})
            MERGE (l:Level {name: $aisle_level_id})
            ON CREATE SET l.created_by = $created_by, a.created_by = $created_by
            MERGE (a)-[r:CONTAINS]->(l)
            ON CREATE SET r.created_by = $created_by
            ON MATCH SET r.created_by = CASE
                WHEN r.created_by IS NULL THEN $created_by
                ELSE r.created_by
            END
            RETURN l
        """
        tx.run(query, aisle_node_id=aisle_node_id, aisle_level_id=aisle_level_id, created_by=created_by)

    @staticmethod
    def _create_shuttle_node(tx, view, aisle_level_id, shuttle_node_id, created_by=None):
        """
        Ensures a Shuttle node exists inside an Aisle Level.

        - This node represents a **shuttle operating inside a specific aisle level**.

        Args:
            tx: Neo4j transaction object.
            view (str): The view to associate with the shuttle node.
            aisle_level_id (str): The Aisle Level node (e.g., "MSAI01_LV01").
            shuttle_node_id (str): The Shuttle node (e.g., "MSAI01_LV01_SH01").
            created_by (str): The metric name that created this node
        """
        query = """
            MATCH (l:Level {name: $aisle_level_id})
            MERGE (s:Shuttle {name: $shuttle_node_id})
            ON CREATE SET 
                s.views = [$shuttle_node_id],
                s.created_by = $created_by,
                l.created_by = $created_by
            ON MATCH SET s.views = CASE 
                WHEN NOT $shuttle_node_id IN s.views THEN s.views + $shuttle_node_id 
                ELSE s.views 
            END
            MERGE (l)-[r:CONTAINS]->(s)
            ON CREATE SET r.created_by = $created_by
            ON MATCH SET r.created_by = CASE
                WHEN r.created_by IS NULL THEN $created_by
                ELSE r.created_by
            END
            RETURN s
        """
        tx.run(query, view=view, aisle_level_id=aisle_level_id, shuttle_node_id=shuttle_node_id, created_by=created_by)

    @staticmethod
    def _add_metric_to_shuttle_node(
        tx, view, aisle_level_id, shuttle_node_id, metric_name, metric_units="", metric_config_name=None
    ):
        """
        Adds a metric to an existing Shuttle node.

        - Ensures metrics are added only if they do not already exist.
        - Uses APOC to lock nodes for concurrent safety.
        - The HAS_METRIC relationship has a views property set to the passed view.

        Args:
            tx: Neo4j transaction object.
            view (str): The view to associate with the metric relationship.
            aisle_level_id (str): The Aisle Level node (e.g., "MSAI1_LV01").
            shuttle_node_id (str): The Shuttle node (e.g., "MSAI1_LV01_SH01").
            metric_name (str): The metric name to associate with the shuttle node.
            metric_units (str): The units that a metric value represents (e.g. "totes/hour")
            metric_config_name (str): The name of the metric config that created this node
        """

        # First ensure the metric node exists
        Neo4jService._create_metric_node(tx, metric_name, metric_units, metric_config_name=metric_config_name)

        # Then connect the metric to the shuttle node with more detailed logging
        query = """
            MATCH (s:Shuttle {name: $shuttle_node_id})
            MATCH (m:Metric {name: $metric_name})
            CALL apoc.lock.nodes([s])
            WITH s, m
            WHERE NOT $metric_name IN coalesce(s.metrics, [])
            SET s.metrics = apoc.coll.toSet(coalesce(s.metrics, []) + $metric_name)
            WITH s, m
            MERGE (s)-[r:HAS_METRIC]->(m)
            ON CREATE SET 
                r.views = [$view],
                r.created_by = $created_by,
                s.created_by = $created_by,
                m.created_by = $created_by
            ON MATCH SET 
                r.views = CASE 
                    WHEN NOT $view IN coalesce(r.views, []) 
                    THEN coalesce(r.views, []) + $view 
                    ELSE r.views 
                END,
                r.created_by = CASE
                    WHEN r.created_by IS NULL THEN $created_by
                    ELSE r.created_by
                END
            RETURN s, m, r
        """

        tx.run(
            query,
            view=view,
            shuttle_node_id=shuttle_node_id,
            metric_name=metric_name,
            metric_units=metric_units,
            created_by=metric_config_name,
        )
