metrics = {
    ## Receiving Node Metrics ##
    # Receiving Inbound Node Config 
    # Does this need to use ConnectionMovementFact instead?
    # "receiving_inbound_units_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "destination_location_code": "RE.*NP01",
    #         "source_location_code": "RE.*IS01",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:receiving:cases_per_hour:60m_set:count",
    #     "redis_operation": "event_set",
    # },
}