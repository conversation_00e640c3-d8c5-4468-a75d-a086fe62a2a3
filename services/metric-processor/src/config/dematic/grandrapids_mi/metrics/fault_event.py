# TODO: Do we need this?  I don't think it's used (RT)
metrics = {
    # Multishuttle Faulted Locations
    # "multishuttle_faulted_locations_start": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "mts_type": "(?i)^multishuttle$",  # (?i) makes the match case-insensitive
    #         "event_type": "^fault_start$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:faulted_locations",
    #     "redis_operation": "fault_start",
    #     "params": {
    #         "fault_id": "{fault_id_code}"
    #     },
    #     "units": "faulted_locations",
    # },
    # "multishuttle_faulted_locations_end": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "mts_type": "(?i)^multishuttle$",  # (?i) makes the match case-insensitive
    #         "event_type": "^fault_end$"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:faulted_locations",
    #     "redis_operation": "fault_end",
    #     "params": {
    #         "fault_id": "{fault_id_code}"
    #     },
    #     "units": "faulted_locations",
    # },
    # ### Multishuttle Active Devices
    # "multishuttle_active_devices_lift_incr": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "availability_status": "^AU$",
    #         "device_id_code": "^MSAI.*E[RL].*LO.*$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:active_devices:lift:count",
    #     "redis_operation": "incr",
    # },
    # "multishuttle_active_devices_lift_decr": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "availability_status": "^(?!AU$).*",
    #         "device_id_code": "^MSAI.*E[RL].*LO.*$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:active_devices:lift:count",
    #     "redis_operation": "decr",
    # },
    # "multishuttle_active_devices_shuttle_incr": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "availability_status": "^AU$",
    #         "device_id_code": "^MSAI.*LV.*SH.*$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:active_devices:shuttle:count",
    #     "redis_operation": "incr",
    # },
    # "multishuttle_active_devices_shuttle_decr": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "availability_status": "^(?!AU$).*",
    #         "device_id_code": "^MSAI.*LV.*SH.*$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:active_devices:shuttle:count",
    #     "redis_operation": "decr",
    # },
    # ### TODO:
    # # Shuttle Device Status is a Module level metric, should not have been added yet, but we will save for later.
    # # When this metric is added, should be changed to store the device_id inside of the metric name.
    # # Each device will have its own device-status metric (1:1)
    # # "shuttle_device_status": {
    # #     "views": ["{area_code}"],
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "device_id_code": "^MSAI.*LV.*SH.*$",
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:{view}:{device_id_code}:device_status:status",
    # #     "redis_params": {
    # #         "key": "{device_id_code}",
    # #         "value": "{availability_status}",
    # #     },
    # #     "redis_operation": "key_value_pair",
    # # },
    # # "lift_device_status": {
    # #     "views": ["{area_code}"],
    # #     "metric_type": "node",
    # #     "match_conditions": {
    # #         "device_id_code": "^MSAI.*(ER|EL).*(LO|LI).*$", # I believe we need to include both LO and LI (lift outbound and lift inbound), but double check codes in data to verify
    # #     },
    # #     "graph_operation": "area_node",
    # #     "name_formula": "{tenant}:{facility}:{view}:{device_id_code}:device_status:status",
    # #     "redis_params": {
    # #         "key": "{device_id_code}",
    # #         "value": "{availability_status}",
    # #     },
    # #     "redis_operation": "key_value_pair",
    # # },
}
