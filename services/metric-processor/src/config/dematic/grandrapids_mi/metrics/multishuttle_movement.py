metrics = {
    # Multishuttle Node Metrics
    # "multishuttle_inbound_totes_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "MSAI.*PS10",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:inbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # "multishuttle_outbound_totes_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "MSAI.*DS10",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:outbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # "multishuttle_movements_per_fault_increment": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "movement_type_code": "^(ByPass|Retrieval|Shuffle|Storage)$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:movements_per_fault:movements",
    #     "redis_operation": "movements_per_fault_increment",
    # },
    # ### Retrieval Movements
    # "multishuttle_movement_retrieval_rate": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "movement_type_code": "Retrieval",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #         "ttl": 900,
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:retrieval_rate:15m_set:hourly_rate",
    #     "redis_operation": "event_set",
    # },
    # "multishuttle_movement_retrieval_total_movements": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "movement_type_code": "Retrieval",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:total_retrieval_movements:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # ### Storage Movements
    # "multishuttle_movement_storage_movement_rate": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "movement_type_code": "Storage",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:storage_rate:15m_set:hourly_rate",
    #     "params": {
    #         "message_uid": "{message_uid}",
    #         "ttl": 900,
    #     },
    #     "redis_operation": "event_set",
    # },
    # "multishuttle_movement_storage_total_movements": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "movement_type_code": "Storage",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:total_storage_movements:60m_set:count",
    #     "redis_operation": "event_set",
    # },
}
