metrics = {
    ## Multishuttle Node Metrics ##
    # Multishuttle Inbound Node Config
    # "multishuttle_inbound_totes_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "AI.*PS",
    #         "load_unit_code": "^.+$"
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:inbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # # Multishuttle Outbound Node Config
    # "multishuttle_outbound_totes_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "AI.*SL01",
    #         "load_unit_code": "^.+$"
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:outbound_totes_count:60m_set:count",
    #     "redis_operation": "event_set",
    # },
    # ## Multishuttle Edge Metric Configs ##
    # # Multishuttle Inbound Edge Config
    # "multishuttle_inbound_totes_edge": {
    #     "metric_type": "edge",
    #     "match_conditions": {
    #         "destination_location_code": "MSAI.*PS",
    #         "load_unit_code": "^.+$"
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_edge",
    #     "redis_operation": "event_set",
    #     "inbound_area": "multishuttle",
    #     "hu_id": "load_unit_code",
    # },
    # # Multishuttle Outbound Edge Config
    # "multishuttle_outbound_totes_edge": {
    #     "metric_type": "edge",
    #     "match_conditions": {
    #         "source_location_code": "AI.*SL01",
    #         "load_unit_code": "^.+$"
    #     },
    #     "outbound_area": "multishuttle",
    #     "units": "handling_unit",
    #     "hu_id": "load_unit_code",
    # },
    # # Workstations Inbound from Multishuttle Outbound Edge Config
    # "workstations_inbound_totes_edge": {
    #     "metric_type": "edge",
    #     "match_conditions": {
    #         "source_location_code": "CCAI.*PS.*",
    #         "load_unit_code": "^.+$",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_edge",
    #     "redis_operation": "event_set",
    #     "inbound_area": "workstations",
    #     "hu_id": "load_unit_code",
    # },
    # # Workstations Outbound to Multishuttle Inbound Edge Config
    # "workstations_outbound_totes_edge": {
    #     "metric_type": "edge",
    #     "match_conditions": {
    #         "source_location_code": "CCAI.*GR01NP07",
    #         "load_unit_code": "^.+$",
    #     },
    #     "outbound_area": "workstations",
    #     "units": "handling_unit",
    #     "hu_id": "load_unit_code",
    # },
    # ##############################################################
    # ## Receiving Area Metrics ##
    # "receiving_stock_time_start": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "RE.*IS01",
    #         "load_unit_code": "^.+$"
    #     },
    #     "params": {
    #         "instance_id": "{load_unit_code}"
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:receiving:stock_time:60m_set:average",
    #     "redis_operation": "cycle_time_start",
    #     "units": "case",
    # },
    # "receiving_stock_time_end": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "source_location_code": "RE.*NP01",
    #         "load_unit_code": "^.+$"
    #     },
    #     "params": {
    #         "instance_id": "{load_unit_code}",
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:receiving:stock_time:60m_set:average",
    #     "redis_operation": "cycle_time_stop",
    #     "units": "case",
    # },
    # # Receiving Outbound Edge Config - when item leaves last location in Receiving Area?:
    # "receiving_outbound_units_edge": {
    #     "metric_type": "edge",
    #     "match_conditions": {
    #         "source_location_code": "RE.*NP01",
    #         "load_unit_code": "^.+$"
    #     },
    #     "hu_id": "load_unit_code",
    #     "outbound_area": "receiving",
    #     "units": "handling_unit",
    # },
    # # TODO: We could config this metric here instead of in TransportTaskConfirmationFact.py, need to decide which is best.
    # # "receiving_inbound_unit_count": {
    # #     "match_conditions": {
    # #         "source_location_code": "RE.*IS01",
    # #         "load_unit_code": "^.+$"
    # #     },
    # #     "params": {
    # #         "message_uid": "{message_uid}",
    # #     },
    # #     "name_formula": "{tenant}:{facility}:receiving:inbound:60m_set:count",
    # #     "redis_operation": "event_set",
    # #     "graph_operation": "area_node",
    # # },
}