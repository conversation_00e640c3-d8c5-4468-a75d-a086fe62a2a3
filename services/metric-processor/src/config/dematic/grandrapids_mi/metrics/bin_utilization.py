metrics = {
    # # Multishuttle Storage Available Location Distribution 
    # "multishuttle_storage_location_distribution_available": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "empty_location_count": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:location_distribution_available:count",
    #     "redis_operation": "storage_location_distribution_available",
    #     "params": {
    #         "empty_location_count": "{empty_location_count}",
    #     },
    # },
    # # Multishuttle Storage Occupied Location Distribution
    # "multishuttle_storage_location_distribution_occupied": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "empty_location_count": "^.+$",
    #         "total_location_count": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:location_distribution_occupied:count",
    #     "redis_operation": "storage_location_distribution_occupied",
    #     "params": {
    #         "empty_location_count": "{empty_location_count}",
    #         "total_location_count": "{total_location_count}",
    #     },
    # },
    # ### Multishuttle Storage Utilization
    # "multishuttle_storage_utilization": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "empty_location_count": "^.+$",
    #         "total_location_count": "^.+$",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:multishuttle:storage_utilization:percent",
    #     "params": {
    #         "empty_location_count": "{empty_location_count}",
    #         "total_location_count": "{total_location_count}",
    #     },
    #     "redis_operation": "storage_utilization",
    # }
}