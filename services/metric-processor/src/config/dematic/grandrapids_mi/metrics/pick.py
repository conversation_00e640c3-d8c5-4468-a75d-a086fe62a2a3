metrics = {
    ## Workstations Node Metrics ##
    # Workstations Node Picks Config
    # "workstations_picks_count": {
    #     "metric_type": "node",
    #     "match_conditions": {
    #         "event_code": "PICKED",
    #     },
    #     "params": {
    #         "message_uid": "{message_uid}",
    #     },
    #     "graph_operation": "area_node",
    #     "name_formula": "{tenant}:{facility}:workstations:picks:60m_set:count",
    #     "redis_operation": "event_set",
    # },
}