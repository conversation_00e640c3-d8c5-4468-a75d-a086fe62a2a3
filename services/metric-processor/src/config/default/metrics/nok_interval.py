metrics = {
    
    # Workstations - Faults - used to calculate arrivals_per_fault
    "workstations_arrivals_per_fault_denominator": {
        "views": ["facility"],
        "graph_operation": "area_node",
        "match_conditions": {
            "equipment_code": ".*GTP-\\d{2}.*",  # Matches any string containing GTP-XX where XX are digits
        },
        "config_type": "node",
        "node_name": "workstations",
        "metric_type": "arrivals_per_fault_denominator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
    },
    # # Workstation - Faults - used to calculate arrivals_per_fault
    "workstation_arrivals_per_fault_denominator": {
        "views": ["workstations"],
        "graph_operation": "area_node",
        "label": "Station",
        "match_conditions": {
            "equipment_code": "M11-GTP-\\d{2}.*",  # Matches any string containing GTP-XX where XX are digits
        },
        "config_type": "node",
        "node_name": "{equipment_code}",  # Fallback if name fails
        "name_formula": {
            "source": "{equipment_code}",
            "pattern": ".*(?P<gtp_part>GTP-\\d{2}).*",  # Captures GTP-XX from anywhere in the string
            "template": "M11-{gtp_part}"  # Prepends M11- to the captured GTP-XX
        },
        "metric_type": "arrivals_per_fault_denominator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
        "parent_nodes": "workstations",
        
    },
    ######################################################################
    # Multishuttle - Faults
    "multishuttle_movements_per_fault_denominator": {
        "views": ["facility"],
        "graph_operation": "area_node",
        "match_conditions": {
            "equipment_code": "^MSAI.*$",
        },
        "config_type": "node",
        "node_name": "multishuttle",
        "metric_type": "movements_per_fault_denominator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
    },
    # Aisle - Faults - used to calculate aisle movements_per_fault
    "aisle_movements_per_fault_denominator": {
        "aggregation": "ratio",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Aisle",  # Add label to ensure proper node creation
        "match_conditions": {
            "equipment_code": "^MSAI.*(?:E[RL].*LO01|LV.*SH01)$",
        },
        "metric_type": "movements_per_fault_denominator",
        "name_formula": {
            "source": "{equipment_code}",
            "pattern": "(?P<aisle_code>MSAI\\d{2}).*",
            "template": "{aisle_code}"
        },
        "redis_operation": "event_set",
        "time_window": "60m_set",
        "views": ["multishuttle"],
    },
}
