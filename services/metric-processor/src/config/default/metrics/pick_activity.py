metrics = {
    # Workstations - Source Container Arrivals Total Count
    "workstations_source_container_arrival_count": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "source_container_arrival_count",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
    },
    # Workstations - Source Container Arrival Rate
    "workstations_source_container_arrival_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "source_container_arrival_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
    },    
    # Workstations - Destination Container Arrivals Total Count
    "workstations_destination_container_arrival_count": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "destination_container_arrival_count",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
    },
    # Workstations - Destination Container Arrival Rate
    "workstations_destination_container_arrival_rate": {
        "views": ["facility"],
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "destination_container_arrival_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
    },
    # Workstations - Total Arrivals - Used to calculate arrivals_per_fault
    "workstations_arrivals_per_fault_numerator": {
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6]|D1)$",
        },
        "config_type": "node",
        "node_name": "workstations",
        "metric_type": "arrivals_per_fault_numerator",
        "time_window": "60m_set",
        "aggregation": "ratio",
        "redis_operation": "event_set",
    },
    # Workstations - Active Operators - Count
    "workstations_active_operators_increment": {
        "config_type": "node",
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "workstation_code": "-GTP-",
            "event_code": "^Logon$",
        },
        "node_name": "workstations",
        "metric_type": "active_operators",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
    },
    "workstations_active_operators_decrement": {
        "config_type": "node",
        "graph_operation": "area_node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^Logoff$",
        },
        "node_name": "workstations",
        "metric_type": "active_operators",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_subtract",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
    },
    # Workstations - Occupied Source Locations - Count
    "workstations_occupied_source_locations_increment": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "occupied_source_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
    },
    "workstations_occupied_source_locations_decrement": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "occupied_source_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_subtract",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
    },
    # Workstations - Occupied Destination Locations - Count
    "workstations_occupied_destination_locations_increment": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "occupied_destination_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
    },
    "workstations_occupied_destination_locations_decrement": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
        },
        "graph_operation": "area_node",
        "node_name": "workstations",
        "metric_type": "occupied_destination_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_subtract",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
    },
    # Workstations - Logged In Time Start - Used to calculate logged_in_time
    "workstations_logged_in_time_start": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^Logon$",
            "workstation_code": "^M.*GTP-\d{2}$",
        },
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "workstations",
        "metric_type": "logged_in_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "metric_units": "mins",
        "graph_operation": "area_node",
    },
    # Workstations - Logged In Time Stop - Used to calculate logged_in_time
    "workstations_logged_in_time_stop": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {
            "event_code": "^Logoff$",
            "workstation_code": "^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "workstations",
        "metric_type": "logged_in_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
    },
    # Workstations - Occupied Time Start - Used to calculate occupied_time
    "workstations_occupied_time_start": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # start the cycle when an arrival event occurs
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "occupied_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
    },
    # Workstations - Occupied Time Stop - Used to calculate occupied_time
    "workstations_occupied_time_stop": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # stop the cycle for departure and release events only
            "or_condition": [
                {"event_code": "^Departure$"},
                {"event_code": "^Release$"},
            ],
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "occupied_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
    },
    # Workstations - Active Time Start - Used to calculate active_time
    "workstations_active_time_start": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # start the cycle when an arrival event occurs and an operator is logged on
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "operator_code": "^.+$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "active_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
    },
    # Workstations - Active Time Stop - Used to calculate active_time
    "workstations_active_time_stop": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # stop the cycle for release, departure, and logoff events
            "event_code": "^(Release|Departure|Logoff)$",
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "active_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
    },
    # Workstations - Idle Time Start - Used to calculate idle_time
    "workstations_idle_time_start": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # start the cycle for release and departure events when an operator is logged on
            "or_condition": [
                {"event_code": "^Departure$"},
                {"event_code": "^Release$"},
            ],
            "operator_code": "^.+$",
            "induction_zone_code": "^M.*GTP.*(D1|(B[1-5]|F[1-6]))$",  # includes source and destination locations
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "idle_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
    },
    # Workstations - Idle Time Stop - Used to calculate idle_time
    "workstations_idle_time_stop": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # stop the cycle for arrival and logoff events
            "or_condition": [
                {"event_code": "^Arrival$"},
                {"event_code": "^Logoff$"},
            ],
            "induction_zone_code": "^M.*GTP.*(D1|(B[1-5]|F[1-6]))$",  # includes source and destination locations
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "idle_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
    },
    # Workstations - Starved Time Start - Used to calculate starved_time
    "workstations_starved_time_start": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # start the cycle when handling_unit_code is null and operator is logged on
            "handling_unit_code": "^None$",
            "operator_code": "^.+$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "workstations",
        "metric_type": "starved_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
    },
    # Workstations - Starved Time Stop - Used to calculate starved_time
    "workstations_starved_time_stop": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # stop the cycle when handling_unit_code is not null or operator logs off
            "or_condition": [
                {"handling_unit_code": "^.+$"},
                {"event_code": "^Logoff$"},
            ],
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "workstations",
        "metric_type": "starved_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
    },
    # Workstations - Blocked Time Start - Used to calculate blocked_time
    "workstations_blocked_time_start": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # start the cycle when a release event occurs and an operator is logged on
            "event_code": "^Release$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "operator_code": "^.+$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "blocked_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
    },
    # Workstations - Blocked Time Stop - Used to calculate blocked_time
    "workstations_blocked_time_stop": {
        "config_type": "node",
        "views": ["facility"],
        "match_conditions": {  # stop the cycle when there is a logoff or departure event
            "or_condition": [
                {"event_code": "^Logoff$"},
                {"event_code": "^Departure$"},
            ],
            "induction_zone_code": "^M.*GTP.*D1$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "workstations",
        "metric_type": "blocked_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
    },
    #########################################
    # Work Station Node Metrics
    ## Workstation - Source Container Arrival Rate
    "workstation_source_container_arrival_rate": {
        "views": ["workstations"],
        "label": "Station",
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": "^M.*GTP-\d{2}$",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "source_container_arrival_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "parent_nodes": ["workstations"],
    },
    "workstation_source_container_arrival_count": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": "^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "source_container_arrivals",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
    },
    ## Workstation Occupied Source Locations
    "workstation_occupied_source_locations_increment": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "occupied_source_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
        "parent_nodes": ["workstations"],
    },
    "workstation_occupied_source_locations_decrement": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "occupied_source_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_subtract",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
        "parent_nodes": ["workstations"],
    },
    # Workstation - Occupied Destination Locations - Count
    "workstation_occupied_destination_locations_increment": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "occupied_destination_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
        "parent_nodes": ["workstations"],
    },
    "workstation_occupied_destination_locations_decrement": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "occupied_destination_locations",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_subtract",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
        "parent_nodes": ["workstations"],
    },
    # Workstation - Active Operators - Count
    "workstation_active_operators_increment": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "graph_operation": "area_node",
        "match_conditions": {
            "event_code": "^Logon$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "node_name": "{workstation_code}",
        "metric_type": "active_operators",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_count",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
        "parent_nodes": ["workstations"],
    },
    "workstation_active_operators_decrement": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "graph_operation": "area_node",
        "match_conditions": {
            "event_code": "^Logoff$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "node_name": "{workstation_code}",
        "metric_type": "active_operators",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "distinct_item_subtract",
        "redis_params": {
            "identifier": "{workstation_code}",
        },
        "parent_nodes": ["workstations"],
    },
    # Workstation - Destination Container Arrivals Total Count
    "workstation_destination_container_arrival_count": {
        "views": ["workstations"],
        "label": "Station",
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "destination_container_arrival_count",
        "time_window": "60m_set",
        "aggregation": "count",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
    },
    # Workstation - Destination Container Arrival Rate
    "workstation_destination_container_arrival_rate": {
        "views": ["workstations"],
        "label": "Station",
        "config_type": "node",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "/hr",
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "destination_container_arrival_rate",
        "time_window": "15m_set",
        "aggregation": "hourly_rate",
        "redis_operation": "event_set",
        "redis_params": {
            "ttl": 900,
        },
        "parent_nodes": ["workstations"],
    },
    ####################################
    # Workstation Time Based Metrics
    ## Work Station - Logged In Time Start - Used to calculate logged_in_time
    "workstation_logged_in_time_start": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {
            "event_code": "^Logon$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "logged_in_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Logged In Time Stop - Used to calculate logged_in_time
    "workstation_logged_in_time_stop": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {
            "event_code": "^Logoff$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "logged_in_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Occupied Time Start - Used to calculate occupied_time
    "workstation_occupied_time_start": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # start the cycle when an arrival event occurs
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "occupied_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Occupied Time Stop - Used to calculate occupied_time
    "workstation_occupied_time_stop": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # stop the cycle for departure and release events only
            "or_condition": [
                {"event_code": "^Departure$"},
                {"event_code": "^Release$"},
            ],
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "occupied_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Active Time Start - Used to calculate active_time
    "workstation_active_time_start": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # start the cycle when an arrival event occurs and an operator is logged on
            "event_code": "^Arrival$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "operator_code": "^.+$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "active_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Active Time Stop - Used to calculate active_time
    "workstation_active_time_stop": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # stop the cycle for release, departure, and logoff events
            "event_code": "^(Release|Departure|Logoff)$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "active_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Idle Time Start - Used to calculate idle_time
    "workstation_idle_time_start": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # start the cycle for release and departure events when an operator is logged on
            "event_code": "^(Departure|Release)$",
            "operator_code": "^.+$",
            "induction_zone_code": "^M.*GTP.*(D1|(B[1-5]|F[1-6]))$",  # includes source and destination locations
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "idle_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Idle Time Stop - Used to calculate idle_time
    "workstation_idle_time_stop": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # stop the cycle for arrival and logoff events
            "event_code": "^(Arrival|Logoff)$",
            "induction_zone_code": "^M.*GTP.*(D1|(B[1-5]|F[1-6]))$",  # includes source and destination locations
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "idle_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Starved Time Start - Used to calculate starved_time
    "workstation_starved_time_start": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # start the cycle when handling_unit_code is null and operator is logged on
            "handling_unit_code": "^None$",
            "operator_code": "^.+$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "starved_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Starved Time Stop - Used to calculate starved_time
    "workstation_starved_time_stop": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # stop the cycle when handling_unit_code is not null or operator logs off
            "or_condition": [
                {"handling_unit_code": "^.+$"},
                {"event_code": "^Logoff$"},
            ],
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{workstation_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "starved_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Blocked Time Start - Used to calculate blocked_time
    "workstation_blocked_time_start": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # start the cycle when a release event occurs and an operator is logged on
            "event_code": "^Release$",
            "induction_zone_code": "^M.*GTP.*D1$",
            "operator_code": "^.+$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "blocked_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_start",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    ## Work Station - Blocked Time Stop - Used to calculate blocked_time
    "workstation_blocked_time_stop": {
        "config_type": "node",
        "views": ["workstations"],
        "label": "Station",
        "match_conditions": {  # stop the cycle when there is a logoff or departure event
            "or_condition": [
                {"event_code": "^Logoff$"},
                {"event_code": "^Departure$"},
            ],
            "induction_zone_code": "^M.*GTP.*D1$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_units": "mins",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "node_name": "{workstation_code}",
        "metric_type": "blocked_time",
        "time_window": "60m_set",
        "aggregation": "sum",
        "redis_operation": "cycle_time_stop",
        "graph_operation": "area_node",
        "parent_nodes": ["workstations"],
    },
    # Work Station Destination Position - Pick Order Lines Ratio - Numerator
    "workstation_destination_position_pick_lines_ratio_numerator": {
        "views": ["workstations"],
        "config_type": "node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "zone_code": "^.+$",
            "workflow_code": "PICK",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "graph_operation": "area_node",
        "node_name": "{workstation_code}",
        "metric_type": "{induction_zone_code}_destination_position_ratio",
        "time_window": "60m_set",
        "aggregation": "destination_position_ratio",
        "redis_operation": "event_set",
        "parent_nodes": ["workstations"],
    },
    # Work Station Destination Position - Pick Order Lines Ratio - Denominator
    "workstation_destination_position_pick_lines_ratio_denominator": {
        "aggregation": "destination_position_ratio",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "Station",
        "match_conditions": {
            "induction_zone_code": "^M.*GTP.*(B[1-5]|F[1-6])$",
            "zone_code": "^.+$",
            "workflow_code": "PICK",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "metric_type": "destination_position_ratio_denominator",
        "node_name": "{workstation_code}",
        "parent_nodes": ["workstations"],
        "redis_operation": "event_set",
        "time_window": "60m_set",
        "views": ["workstations"],
    },
    ##############################################
    # Source & Destination Location Edge Metrics
    ##############################################
    "edge_from_workstation_source_location_to_destination_location": {
        "config_type": "complete-edge",
        "graph_operation": "area_edge",
        "inbound_area": "{induction_zone_code}",
        "inbound_parent_nodes": ["{workstation_code}"],
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6])$",
            "workstation_code": r"^M.*GTP-\d{2}$",
        },
        "outbound_area": "{workstation_code}D1",
        "outbound_node_label": "StationLocation",
        "outbound_parent_nodes": ["{workstation_code}"],
        "redis_operation": "event_set",
        "views": ["{workstation_code}"],
    },
    # Destination Location - Occupied
    "destination_location_occupied": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6])$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "occupied_location",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "Yes",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Destination Location - Unoccupied
    "destination_location_unoccupied": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6])$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "occupied_location",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "No",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    
    # Destination Location - Arrival Rate
    "destination_location_arrival_rate": {
        "aggregation": "hourly_rate",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "{workstation_code}(B[1-5]|F[1-6])$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "destination_container_arrival_rate",
        "metric_units": "/hr",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "event_set",
        "time_window": "15m_set",
        "views": ["{workstation_code}"],
    },
    # Destination Location Arrivals Total Count
    "destination_location_arrival_count": {
        "aggregation": "count",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6])$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "destination_container_arrivals",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "event_set",
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },  
    # Work Station Destination Position - Pick Order Lines Ratio - Numerator
    # NOTE: There is no config for a denominator because it uses the same denominator as the workstation metric config.
    "destination_location_destination_position_ratio_numerator": {
        "aggregation": "destination_position_ratio",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6])$",
            "zone_code": "^.+$",
            "workflow_code": "PICK",
            "workstation_code": "^M.*-GTP-\\d{2}$",
        },
        "metric_type": "destination_position_ratio",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "event_set",
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    ##################################################
    # Source Location & Destination Location Metrics
    ##################################################
    # Source Location & Destination Location - Occupied
    "station_location_occupied": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "occupied_location",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "Yes",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Unoccupied
    "station_location_unoccupied": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "occupied_location",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "No",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Active Operator
    "station_location_active_operator": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "operator_code": "^.+$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "active_operator",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "Yes",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Inactive Operator
    "station_location_inactive_operator": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "operator_code": "^None$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "active_operator",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "No",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Container ID
    "station_location_container_id": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
            "handling_unit_code": "^.+$",
        },
        "metric_type": "container_id",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "{handling_unit_code}",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    "station_location_container_id_remove": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "container_id",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Set Load Unit Type
    "station_location_load_unit_type": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "handling_unit_type": "^.+$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "load_unit_type",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "{handling_unit_type}",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Unset Load Unit Type
    "station_location_load_unit_type_remove": {
        "aggregation": "static",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^(?!Arrival$).+$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "load_unit_type",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "store_static_value",
        "redis_params": {
            "value": "",
        },
        "time_window": "value",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Occupied Time Start
    "station_location_occupied_time_start": {
        "aggregation": "sum",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "occupied_time",
        "metric_units": "mins",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "cycle_time_start",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    ## Source Location & Destination Location - Occupied Time Stop
    "station_location_occupied_time_stop": {
        "aggregation": "sum",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^(Release|Departure)$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "occupied_time",
        "metric_units": "mins",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "cycle_time_stop",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Active Time - Start
    "station_location_active_time_start": {
        "aggregation": "sum",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "operator_code": "^.+$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "active_time",
        "metric_units": "mins",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "cycle_time_start",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    ## Source Location & Destination Location - Active Time - Stop
    "station_location_active_time_stop": {
        "aggregation": "sum",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "or_condition": [
                {"operator_code": "^None$",},
                {"event_code": "^(Release|Departure)$"},
            ],
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "active_time",
        "metric_units": "mins",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "cycle_time_stop",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    # Source Location & Destination Location - Idle Time Start
    "station_location_idle_time_start": {
        "aggregation": "sum",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": { 
            "event_code": "^(Departure|Release)$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "operator_code": "^.+$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "idle_time",
        "metric_units": "mins",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "cycle_time_start",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    ## Source Location & Destination Location - Idle Time Stop
    "station_location_idle_time_stop": {
        "aggregation": "sum",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": { 
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}(B[1-5]|F[1-6]|D1)$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "idle_time",
        "metric_units": "mins",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "cycle_time_stop",
        "redis_params": {
            "instance_id": "{induction_zone_code}",
        },
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
    ###########################################
    # Source Location Metrics
    ###########################################
    # Source Location - Source Container Arrival Rate
    "source_location_arrival_rate": {
        "aggregation": "hourly_rate",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}D1$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "source_container_arrival_rate",
        "metric_units": "/hr",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "event_set",
        "time_window": "15m_set",
        "views": ["{workstation_code}"],
    },
    # Source Location Arrivals Total Count
    "source_location_arrival_count": {
        "aggregation": "count",
        "config_type": "node",
        "graph_operation": "area_node",
        "label": "StationLocation",
        "match_conditions": {
            "event_code": "^Arrival$",
            "induction_zone_code": "^{workstation_code}D1$",
            "workstation_code": "^M.*GTP-\\d{2}$",
        },
        "metric_type": "source_container_arrivals",
        "node_name": "{induction_zone_code}",
        "parent_nodes": ["{workstation_code}"],
        "redis_operation": "event_set",
        "time_window": "60m_set",
        "views": ["{workstation_code}"],
    },
}
