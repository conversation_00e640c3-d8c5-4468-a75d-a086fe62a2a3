from dataclasses import dataclass, field
from typing import Any


@dataclass
class ConfigurationResult:
    """
    Represents the result of processing a metric within the `MetricProcessor`.

    This class stores information about whether a metric was successfully processed,
    the resolved name of the metric, and the associated configuration name.

    Attributes:
        resolved_name (str): The fully resolved name of the metric after applying 
            dynamic substitutions or transformations specified by the name_formula.
        success (bool): Indicates whether the metric was successfully processed.
        config_name (str): The name of the configuration associated with the metric. 
            These config names are keys of the metrics dictionary in a configuration file.
    """
    resolved_name: str = ""
    success: bool = False  
    config_name: str = ""
