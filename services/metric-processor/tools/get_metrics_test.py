"""Test file for getting metrics from /metrics/ endpoint.

This is a script file that is used when developing and running locally.

It runs a test the /metrics/ endpoint by sending a POST request to
the endpoint with a list of metricIds that should be returned.

The Tenant and Facility arguments are required and
will be prepended to the metric name.
"""

import json
import subprocess
import sys
import os
from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env.

if len(sys.argv) < 3:
    raise ValueError("Command requires two arguments")

# Initialize tenant.
tenant_name = sys.argv[1]
facility_name = sys.argv[2]
graph_level_name = sys.argv[3]
# TODO: Add an optional parameter for "node name" so that you can test only metrics in one node if you want.

# List of metrics for each graph level that this script will send as part of the request to the /metrics/ endpoint.
# The metrics sent in the request are the only metrics that will be returned by /metrics/.
metrics = {
    "facility": [
        # Multishuttle
        "multishuttle:inbound_totes_count:60m_set:count",
        "multishuttle:location_distribution_available",
        "multishuttle:location_distribution_occupied",
        "multishuttle:movements_per_fault:60m_set:ratio",
        "multishuttle:outbound_totes_count:60m_set:count",
        "multishuttle:retrieval_rate:15m_set:hourly_rate",
        "multishuttle:storage_rate:15m_set:hourly_rate",
        "multishuttle:storage_utilization",
        "multishuttle:total_retrieval_movements:60m_set:count",
        "multishuttle:total_storage_movements:60m_set:count",
        # Receiving
        "receiving:stock_time:60m_set:avg",
        # Workstations
        "workstations:arrivals_per_fault:60m_set:ratio",
        "workstations:destination_container_arrival_count:60m_set:count",
        "workstations:destination_container_arrival_rate:15m_set:hourly_rate",
        "workstations:order_lines_picked_count:60m_set:count",
        "workstations:order_quantity_picked:60m_set:sum",
        "workstations:order_quantity_throughput:15m_set:sum",
        "workstations:source_container_arrival_count:60m_set:count",
        "workstations:source_container_arrival_rate:15m_set:hourly_rate",
        # Miniload Tests
        "miniload:aisle_total_movements:60min_set:count",
        "miniload:aisle_storage_movements:60min_set:count",
        "miniload:aisle_retrieval_movements:60min_set:count",
        "miniload:aisle_bypass_movements:60min_set:count",
        "miniload:aisles_available:60m_set:count"
        "miniload:bypass_rate:15m_set:hourly_rate",
        "miniload:miniloads_available:60m_set:count",
        "miniload:movements_per_fault:60m_set:ratio",
        "miniload:retrieval_rate:15m_set:hourly_rate",
        "miniload:storage_rate:15m_set:hourly_rate",
        "miniload:total_locations_available:60m_set:sum_item_values",
        "miniload:total_locations_occupied:60m_set:sum_item_values",
    ],
    "miniload": [
        # Miniload Aisles
        "MLAI1:locations_available:value:static",
        "MLAI1:locations_occupied:value:static",
        "MLAI2:locations_available:value:static",
        "MLAI2:locations_occupied:value:static",
        "MLAI3:locations_available:value:static",
        "MLAI3:locations_occupied:value:static",
    ],
    # Workstations
    "workstations": [
        "M11-GTP-01:M11-GTP-01B1_destination_position_ratio:60m_set:destination_position_ratio",
        "M11-GTP-01:M11-GTP-01B2_destination_position_ratio:60m_set:destination_position_ratio",
    ],
    # Workstation GTP-01
    "M11-GTP-01": [
        "M11-GTP-01B5:container_id:value:static",
        "M11-GTP-01B1:active_operator:value:static",
        "M11-GTP-01B1:destination_position_ratio:60m_set:destination_position_ratio",
        "M11-GTP-01B1:occupied_source_locations:60m_set:destination_position_ratio",
        "M11-GTP-01B2:occupied_time:60m_set:sum",
    ],
}

# Just prepend tenant and facility to the metrics
processed_metric_names = [
    f"{tenant_name}:{facility_name}:{metric}" for metric in metrics[graph_level_name]
]

print(processed_metric_names)


def send_get_metrics_request():
    """Sends a POST request to the Metric Processor's /metrics/ endpoint.

    Args:
        attributes: dictionary)
    """

    port = os.getenv("PORT", "8080")

    curl_command = [
        "curl",
        "-X",
        "POST",
        f"http://localhost:{port}/metrics/",
        "-H",
        "Content-Type: application/json",
        "-d",
        json.dumps(
            {
                "tenant_id": tenant_name,
                "facility_id": facility_name,
                "metric_ids": processed_metric_names,
            }
        ),
    ]

    # Execute the curl command using subprocess
    result = subprocess.run(curl_command, capture_output=True, text=True)

    # Print the output from curl
    print("Status:", result.returncode)
    print("Output:", result.stdout)
    if result.stderr:
        print("Error:", result.stderr)


# Loop through imported test data and handle each message batch.
send_get_metrics_request()
