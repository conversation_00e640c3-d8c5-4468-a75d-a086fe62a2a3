from generate_timestamps import update_timestamp

messages = [
    # Batch 1: Valid Miniload Metrics for Bin Utilization and Movements
    {
        "messageId": "test-multishuttle-aisle-level-shuttle-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Multishuttle movements generate data for Aisle Area view w/ AisleLevels and Shuttles.",
            "row_hash": "700",
        },
        "data": {
            "aisle_code": "01",
            "movement_end_timestamp_utc": update_timestamp(900),
            "movement_type_code": "Retrieval",
            "shuttle_code": "MSAI01LV01SH01",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Multishuttle movements generate data for Aisle Area view w/ AisleLevels and Shuttles.",
            "row_hash": "701",
        },
        "data": {
            "aisle_code": "01",
            "movement_end_timestamp_utc": update_timestamp(840),
            "movement_type_code": "Storage",
            "shuttle_code": "MSAI01LV02SH01",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Multishuttle movements generate data for Aisle Area view w/ AisleLevels and Shuttles.",
            "row_hash": "702",
        },
        "data": {
            "aisle_code": "01",
            "movement_end_timestamp_utc": update_timestamp(820),
            "movement_type_code": "Shuffle",
            "shuttle_code": "MSAI01LV03SH01",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Multishuttle movements generate data for Aisle Area view w/ AisleLevels and Shuttles.",
            "row_hash": "703",
        },
        "data": {
            "aisle_code": "02",
            "movement_end_timestamp_utc": update_timestamp(760),
            "movement_type_code": "Storage",
            "shuttle_code": "MSAI02LV03SH01",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Multishuttle movements generate data for Aisle Area view w/ AisleLevels and Shuttles.",
            "row_hash": "704",
        },
        "data": {
            "aisle_code": "02",
            "movement_end_timestamp_utc": update_timestamp(720),
            "movement_type_code": "Retrieval",
            "shuttle_code": "MSAI02LV06SH01",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Multishuttle movements generate data for Aisle Area view w/ AisleLevels and Shuttles.",
            "row_hash": "705",
        },
        "data": {
            "aisle_code": "02",
            "movement_end_timestamp_utc": update_timestamp(900),
            "movement_type_code": "Retrieval",
            "shuttle_code": "MSAI02LV02SH01",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Generates edges and edge metrics for Aisle View between Lifts and Aisles.",
            "row_hash": "706",
        },
        "data": {
            "aisle_code": "01",
            "handling_unit_code": "123",
            "lift_code": "MSAI01EL01LO01",
            "message_uid": "120",
            "movement_end_timestamp_utc": update_timestamp(900),
            "movement_type_code": "Retrieval",
            "shuttle_code": "",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Generates edges and edge metrics for Aisle View between Lifts and Aisles.",
            "row_hash": "707",
        },
        "data": {
            "aisle_code": "01",
            "handling_unit_code": "123",
            "lift_code": "MSAI01ER01LO01",
            "message_uid": "120",
            "movement_end_timestamp_utc": update_timestamp(880),
            "movement_type_code": "Storage",
            "shuttle_code": "",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Generates edges and edge metrics for Aisle View between Lifts and Aisles.",
            "row_hash": "708",
        },
        "data": {
            "aisle_code": "02",
            "handling_unit_code": "123",
            "lift_code": "MSAI02ER01LO01",
            "message_uid": "120",
            "movement_end_timestamp_utc": update_timestamp(840),
            "movement_type_code": "Storage",
            "shuttle_code": "",
            "sku_code": "123",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "multishuttle_movement",
            "description": "Generates edges and edge metrics for Aisle View between Lifts and Aisles.",
            "row_hash": "709",
        },
        "data": {
            "aisle_code": "02",
            "handling_unit_code": "123",
            "lift_code": "MSAI02EL01LO01",
            "message_uid": "120",
            "movement_end_timestamp_utc": update_timestamp(830),
            "movement_type_code": "Retrieval",
            "shuttle_code": "",
            "sku_code": "123",
        },
    },
]
