from generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

messages = [
    {
        "messageId": "test-workstations-pick-activity-batch-5",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Log on to station.",
            "row_hash": "600",
        },
        "data": {
            "workstation_code": "M11-GTP-01",
            "event_code": "Logon",  # An operator is logging into a station.
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(1500),
            "operator_code": "XYZ",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Logon to station",
            "row_hash": "601",
        },
        "data": {
            "workstation_code": "M11-GTP-02",
            "event_code": "Logon",  # An operator is logging into a station.
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(1250),
            "operator_code": "XYZ",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "602",
        },
        "data": {
            "event_code": "Arrival",  # Matches "Arrival"
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(710),
            "handling_unit_code": "ABCDEFG",
            "handling_unit_type": "Pallet",
            "induction_zone_code": "M11-GTP-01D1",  # Matches ^M.*GTP.*D1$ (is a source location)
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "603",
        },
        "data": {
            "event_code": "Arrival",
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(650),
            "handling_unit_code": "ABCDEFG",
            "handling_unit_type": "Case",
            "induction_zone_code": "M11-GTP-01B5",  # Matches ^M.*GTP.*D1$
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "604",
        },
        "data": {
            "event_code": "Arrival",
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(550),
            "handling_unit_code": "ABCDEFG",
            "handling_unit_type": "Pallet",
            "induction_zone_code": "M11-GTP-02B4",
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-02",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "This has mismatched workstation codes and should be ignored by Metric Processor.",
            "row_hash": "604",
        },
        "data": {
            "event_code": "Arrival",
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(550),
            "handling_unit_code": "ABCDEFG",
            "handling_unit_type": "Pallet",
            "induction_zone_code": "M11-GTP-03D1",  # Mismatch of station id
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",  # Mismatch of station id
        },
    },
    {
        "messageId": "test-workstations-pick-order-line-throughput-batch-3",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick",
            "description": "Create workstations node metrics",
            "row_hash": "605",
        },
        "data": {
            "event_timestamp_utc": update_timestamp(525),
            "induction_zone_code": "M11-GTP-01B5",  # Matches ^M.*GTP.*(B[1-5]|F[1-6])
            "operator_code": "XYZ",
            "picked_qty": 33,
            "workflow_code": "PICK",  # Matches PICK
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "606",
        },
        "data":
        ### Cycle Count and Cycle Count Rate
        {
            "event_type": "pick",
            "event_timestamp_utc": update_timestamp(500),
            "induction_zone_code": "M11-GTP-01F2",  # Matches ^M.*GTP.*(B[1-5]|F[1-6])
            "operator_code": "XYZ",
            "picked_qty": 10,
            "workflow_code": "CycleCount",  # Matches CycleCount
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "607",
        },
        "data": {
            "event_timestamp_utc": update_timestamp(485),
            "induction_zone_code": "M11-GTP-01F2",  # Matches ^M.*GTP.*(B[1-5]|F[1-6])
            "operator_code": "XYZ",
            "picked_qty": 20,
            "workflow_code": "CycleCount",  # Matches CycleCount
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "608",
        },
        "data": {
            "event_code": "Departure",  # Matches "Arrival"
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(450),
            "handling_unit_code": "ABCDEFG",
            "induction_zone_code": "M11-GTP-01D1",  # Matches ^M.*GTP.*D1$ (is a source location)
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-viz_event_log-batch-4",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "viz_event_log",
            "description": "A fault within a GTP Station, used to calculate arrivals_per_fault metric.",
            "row_hash": "609",
        },
        "data": {
            "event_type": "viz_event_log",
            "classification": "manual",
            "area": "GTP78-GTP-07_LS",
            "event_timestamp_utc": update_timestamp(410),
            "fault_duration_milliseconds": "2000",
            "active_timestamp_utc": update_timestamp(410),
            "fault_description": "M:DMSRackOutfeedDevi",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "610",
        },
        "data": {
            "workstation_code": "M11-GTP-03",
            "event_code": "Logon",
            "event_timestamp_utc": update_timestamp(380),
            "event_type": "pick_activity",
            "operator_code": "XYZ",
            "workflow_code": "PICK",
            "zone_code": "M11-GTP-03",
        },
    },
    {
        "messageId": "test-nok-interval-batch-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "nok_interval",
            "description": "Creates NOK interval metrics for containerizing and workstation-specific faults",
            "row_hash": "611",
        },
        "data": {
            "event_type": "nok_interval",
            "fault_start_timestamp_utc": update_timestamp(360),
            "fault_end_timestamp_utc": update_timestamp(360),
            "fault_duration_milliseconds": "10000",
            "equipment_code": "M11-GTP-01",  # Matches .*GTP-\d{2}.* pattern
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-4",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "612",
        },
        "data": {
            "event_code": "Release",
            "event_timestamp_utc": update_timestamp(300),
            "event_type": "pick_activity",
            "handling_unit_code": "ABCDEFG",
            "induction_zone_code": "M11-GTP-02B2",
            "operator_code": "XYZ",
            "workflow_code": "PICK",
            "workstation_code": "M11-GTP-02",
            "zone_code": "M11-GTP-02",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-4",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "613",
        },
        "data": {
            "event_code": "Release",
            "event_timestamp_utc": update_timestamp(250),
            "event_type": "pick_activity",
            "handling_unit_code": "ABCDEFG",
            "induction_zone_code": "M11-GTP-01B5",
            "operator_code": "XYZ",
            "workflow_code": "PICK",
            "workstation_code": "M11-GTP-01",
            "zone_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-3",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Log off station",
            "row_hash": "614",
        },
        "data": {
            "event_code": "Logoff",  # An operator is logging out of a station.
            "event_timestamp_utc": update_timestamp(230),
            "event_type": "pick_activity",
            "operator_code": "XYZ",
            "workflow_code": "PICK",
            "workstation_code": "M11-GTP-01",
            "zone_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Log off station",
            "row_hash": "615",
        },
        "data": {
            "workstation_code": "M11-GTP-02",
            "event_code": "Logoff",  # An operator is logging out of a station.
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(150),
            "operator_code": "XYZ",
        },
    },
    {
        "messageId": "test-workstations-pick-activity-batch-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Creates workstations node metrics",
            "row_hash": "616",
        },
        "data": {
            "event_code": "Arrival",  # Matches "Arrival"
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(110),
            "handling_unit_code": "XYZ",
            "handling_unit_type": "Pallet",
            "induction_zone_code": "M11-GTP-01D1",  # Matches ^M.*GTP.*D1$ (is a source location)
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-recontainerizing-pick-activity-batch-2-arrival-b2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Arrival at M11-GTP-01B2",
            "row_hash": "617",
        },
        "data": {
            "event_code": "Arrival",
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(800),
            "handling_unit_code": "TEST123",
            "handling_unit_type": "Pallet",
            "induction_zone_code": "M11-GTP-01B2",
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",
        },
    },
    {
        "messageId": "test-recontainerizing-pick-activity-batch-2-release-b2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "pick_activity",
            "description": "Release from M11-GTP-01B2",
            "row_hash": "618",
        },
        "data": {
            "event_code": "Release",
            "event_type": "pick_activity",
            "event_timestamp_utc": update_timestamp(600),
            "handling_unit_code": "TEST123",
            "induction_zone_code": "M11-GTP-01B2",
            "operator_code": "XYZ",
            "workstation_code": "M11-GTP-01",
        },
    },
]
