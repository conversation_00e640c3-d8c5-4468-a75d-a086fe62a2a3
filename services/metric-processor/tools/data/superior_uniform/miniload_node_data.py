from generate_timestamps import update_timestamp

recent_timestamp = update_timestamp()

messages = [
    # Batch 1: Valid Miniload Metrics for Bin Utilization and Movements
    {
        "messageId": "test-miniload-metrics-1",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Testing Miniload bin utilization and movements match conditions for batch 1.",
            "row_hash": "200",
        },
        "data": {
            # Miniload Locations Available
            "area_code": "ML - Storage",
            "empty_location_position_count": "10",
            "total_location_position_count": "20",
            "aisle_code": "01",
            "event_timestamp_utc": update_timestamp(900),  
        },
    },
    {
        "messageId": "test-miniload-metrics-2",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Testing Miniload bin utilization and movements match conditions for batch 1.",
            "row_hash": "201",
        },
        "data": {
            # Miniload Locations Occupied
            "area_code": "ML - Storage",
            "empty_location_position_count": "5",
            "total_location_position_count": "30",
            "aisle_code": "02",
            "event_timestamp_utc": update_timestamp(800),
        },
    },
    {
        "messageId": "test-miniload-metrics-3",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "vehicle_movement",
            "description": "Testing Miniload vehicle movement metrics for batch 2.",
            "row_hash": "202",
        },
        "data": {
            # Total OK Aisle Movements and Movements Per Fault numerator
            "source_location_code": "MLA01",
            "destination_location_code": "Test",
            "movement_status_code": "OK",
            "movement_type_code": "IAT",
            "movement_end_timestamp_utc": update_timestamp(810),
            "equipment_code": "ML01",
        },
    },
    {
        "messageId": "test-miniload-metrics-4",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Testing Miniload bin utilization and movements match conditions for batch 1.",
            "row_hash": "203",
        },
        "data": {
            # Total OK Storage Movements and Movements Per Fault numerator 
            "source_location_code": "MLA02",
            "destination_location_code": "MLA03",
            "event_timestamp_utc": update_timestamp(750),
            "movement_status_code": "OK",
            "movement_type_code": "Storage",
            "event_timestamp_utc": update_timestamp(750),
            "equipment_code": "ML02",
        },
    },
    {
        "messageId": "test-miniload-metrics-5",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Testing Miniload bin utilization and movements match conditions for batch 1.",
            "row_hash": "204",
        },
        "data": {
            # Total OK Retrieval Movements and Movements Per Fault numerator
            "source_location_code": "MLA04",
            "destination_location_code": "MLA05",
            "movement_status_code": "OK",
            "movement_type_code": "Retrieval",
            "event_timestamp_utc": update_timestamp(610),
            "equipment_code": "ML04",
        },
    },
    {
        "messageId": "test-miniload-metrics-6",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Testing Miniload bin utilization and movements match conditions for batch 1.",
            "row_hash": "205",
        },
        "data": {        # Total OK Bypass Movements and Movements Per Fault Numinator
            "source_location_code": "MLB01",
            "destination_location_code": "MLB02",
            "movement_status_code": "OK",
            "movement_type_code": "Bypass",
            "event_timestamp_utc": update_timestamp(510),
            "equipment_code": "ML01",
        },
    },
    {
        "messageId": "test-miniload-metrics-batch-7",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "viz_event_log",
            "description": "Testing Miniload fault metrics for batch 3.",
            "row_hash": "206",
        },
        "data": {
            # Miniload Fault Movements Per Fault Denominator
            "area": "ML001",
            "event_timestamp_utc": update_timestamp(810),
            "fault_duration_milliseconds":"2000",
        },
    },
    {
        "messageId": "test-miniload-metrics-8",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "bin_utilization",
            "description": "Testing Miniload bin utilization and movements match conditions for batch 1.",
            "row_hash": "207",
        },
        "data": {
            # Another  Fault Movements Per Fault Denominator
            "area": "ML002",
            "fault_duration_milliseconds":"2000",
            "event_timestamp_utc": update_timestamp(500),
        },
    },
]
