import random
from datetime import datetime, timedelta, timezone

def update_timestamp(offset=None):
    # Generate a timestamp `offset` seconds before the current time if `offset` is supplied.
    # Otherwise, generates a timestamp based on a random offset.
    now = datetime.now(timezone.utc)
    if not offset:
        offset = random.randint(0, 3600)  # 3600 seconds = 1 hour
    event_timestamp = now - timedelta(seconds=offset)
    return event_timestamp.isoformat()
