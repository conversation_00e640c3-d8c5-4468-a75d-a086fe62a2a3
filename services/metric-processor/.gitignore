# Byte-compiled / optimized Python files
__pycache__/
*.py[cod]
*$py.class

# Environment-related files
.env
.env.yaml
venv/
env/
ENV/

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg
*.whl

# PyInstaller build artifacts
__pycache__/build/
*.spec
build/
dist/

# IDE-specific files and directories
.vscode/
*.vscode/
*.code-workspace
.idea

# Compiled Python files
*.pyo

# Logs and temporary files
*.log
*.swp
*.tmp

# Miscellaneous
.DS_Store
thumbs.db

# Coverage reports
.coverage
pytest-report.xml
.pytest_cache
htmlcov

# cloud storage emulator
.cloudstorage
cloudstorage
coverage.xml
results.xml
/temp/temp_runId.txt
diagnostic_logs
/test_results.txt

# .tfstate files
*.tfstate
*.tfstate.*
*.terraform
*.terraform.lock.hcl

# output files
*/output/*

# test results
test_results
