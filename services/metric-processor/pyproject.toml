[tool.poetry]
name = "ict-metric-processor"
version = "0.1.0"
description = ""
authors = ["Control Tower"]
packages = [{ include = "src", from = "." }]

[tool.poetry.dependencies]
python = "^3.11"
functions-framework = "^3"
neo4j = "5.20.0"
psycopg2-binary = "2.9.9"
pytz = "2024.1"
redis = "5.0.4"
python-dotenv = "1.0.1"
google-cloud-secret-manager = "*"
structlog = "24.4.0"
google-cloud-bigquery = "*"
google-cloud-bigquery-storage = "*"
google-cloud-pubsub = "*"
google-api-core = "*"
pandas = "*"
db-dtypes = "*"
rstr = "^3.2.2"
pydantic = "^2.10.6"

[tool.poetry.group.docs]
optional = true
dependencies = { mkdocs = "1.6.1", mkdocstrings = "0.26.1", mkdocstrings-python = "1.11.1", mkdocs-macros-plugin = "^1.2.0" }

[tool.poetry.group.dev.dependencies]
poetry-plugin-shell = "^1.0.1"

# TODO: To run all unit tests: python3 -m unittest discover src/tests/unit_tests -v
# TODO: To run a specific test file: python3 -m unittest src/tests/unit_tests/test_postgres_factory.py -v
[tool.poetry.scripts]
test = "src.tests.config_tests.run_tests:run_tests"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"