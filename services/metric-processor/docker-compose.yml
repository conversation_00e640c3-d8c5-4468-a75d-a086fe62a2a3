services:
  metric-processor:
    build:
      context: .
      dockerfile: Dockerfile
    develop:
      watch:
        - action: rebuild # or sync for specific use cases
          path: ./src # path to watch for changes
          ignore:
            - ./src/tests
            - ./**/__pycache__
    ports:
      - "${PORT:-8080}:${PORT:-8080}" # Dynamically bind to the PORT environment variable
    env_file:
      - .env
    volumes:
      - .:/usr/app/metric-processor
    depends_on:
      - redis
      - neo4j
    networks:
      - control_tower_network

  redis:
    image: "redis:alpine"
    networks:
      - control_tower_network
    ports:
      - "6379:6379"

  neo4j:
    image: "neo4j:5.2"
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      NEO4J_AUTH: ${NEO4J_USERNAME}/${NEO4J_PASSWORD}
      NEO4J_PLUGINS: '["apoc"]'
    networks:
      - control_tower_network
    tmpfs:
      - /var/lib/neo4j/run

networks:
  control_tower_network:
    external: true
