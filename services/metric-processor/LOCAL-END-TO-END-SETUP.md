# How To: Link up ETL and API Docker Containers and UI

This walks you through setting up an end to end local test:

- Process test data through the Metric Processor in docker.
- Fetch that processed data from the Control Tower API docker container which in turn hits the MP /metrics/ endpoint.
- Display that data as nodes and edges in the Control Tower UI.

This is done by creating a Docker Shared Network, making sure that every docker container is in that network, and then a few other small temporary changes that we don't actually want to check in to GIT.
(TODO: Base this off of ENV vars instead of making temporary changes)

## Step 1: Create the Docker Shared Network.

- Run this command in terminal:
  `docker network create control_tower_network`
- Inspect the network, confirm it exists and is empty:
  `docker network inspect control_tower_network`

## Step 2: Control Tower ETL - Metric Processor

- In `/metric-processor/docker-compose.yml`: \* At the very bottom of the file, make sure that the network definition for `control_tower_network` is uncommented:
<pre><code>
networks:
    control_tower_network:
        external: true
</code></pre>
- Make sure that every container in `docker-compose.yml` is assigned to the shared network.
<pre><code>
networks:
    - control_tower_network
</code></pre>
- Start up metric processor docker compose: `docker-compose up --build`
- Run `docker network inspect control_tower_network` to verify that all of the MP containers are in the shared network.

## Step 3: Control Tower API

- Update the following vars in `.env.local`.

  - METRIC_PROCESSOR_METRICS_API_URL=http://ict-metric-processor-metric-processor-1:8085
  - NEO4J_URI=bolt://host.docker.internal:7687
  - NEO4J_USERNAME=neo4j
  - NEO4J_PASSWORD=<Your Password> (Must match the password that is set for in your Metric Processor `.env`)
  - REDISHOST=ict-metric-processor-redis-1 (use `docker ps` to verify the name of your metric processor container)
  - REDIS_REQUEST_CACHE_OVERRIDE=true

- Temporarily update CT API `docker-compose.yml`

  - Comment out the entire redis `cache` container block.
    - API container will be using the redis container in Metric Processor instead.
  - Update API container variables:
    - REDISHOST=ict-metric-processor-redis-1
    - Comment out this line: `depends_on: cache`
  - At the very bottom of the file, check that the network definition for `control_tower_network` is uncommented. (same as you did in step one for MP)
  - Check that every container in `docker-compose.yml` is assigned to the shared network. (same as you did in step one for MP)

- Temporarily update redis-middleware.ts path to MP Redis Container

  - Uncomment second url path for local dev with redis:
    - const urlPath = `redis://default:${REDISAUTHSTRING}@${REDISHOST}:${REDISPORT}`;
    - const urlPath = `redis://${REDISHOST}:${REDISPORT}`;

- Start up API w/ docker compose: `docker compose --profile=api up`
- Run `docker network inspect control_tower_network` to verify that all of the API containers are in the shared network.

## Step 4: Control Tower UI

- Follow the steps in the UI project's README.md to run the UI with your API docker container.

- Feature Flags
  - At the time of this writing, I had issues trying to update feature flags when running locally. Feature flags default to OFF when not found, which means you will not have any of the process flow UI features enabled. There are a couple that you can update temporarily in code, but obviously the long term solution would be to get this working.
  - Enable Detail Panel - set to true:
    - IctFacilityProcessFlow.ts
      - `this.detailPanelEnabled = Feature.isEnabled('ict-facility-process-flow-detail-panel');`
  - Enabled Node Metric Display
    - IctNode.ts - set to true:
      - `const nodeMetricsEnabled = nodePropsData.nodeMetricsEnabled ?? false;`
