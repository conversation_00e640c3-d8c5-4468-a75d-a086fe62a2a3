import type {
  OpenAPIClient,
  Parameters,
  UnknownParamsObject,
  OperationResponse,
  AxiosRequestConfig,
} from 'openapi-client-axios';

declare namespace Components {
    namespace Schemas {
        export interface AIMLResponse {
            Error?: string;
            KnownDB?: string;
            ResponseCode: number; // double
            echart_code?: null;
            generated_sql_query?: string;
            question?: string;
            sql_query_output?: string;
            summary_response?: string;
        }
        export interface AdviceDetailsData {
            itemsReceived: {
                packagingLevel: string;
                quantity: number; // double
                sku: string;
                adviceLine: string;
                handlingUnitType: string;
                handlingUnit: string;
            }[];
            supplierId: string;
        }
        export interface AdvicesCycleTimeData {
            cycleTime: number; // double
        }
        export interface AdvicesFinishedData {
            finishedAdvices: number; // double
        }
        export interface AdvicesList {
            adviceList: AdvicesListData[];
        }
        export interface AdvicesListData {
            adviceId: string;
            adviceStatus: string;
            ownerId: string;
            adviceType: string;
            supplierId: string;
            createdTime: string;
            startTime: string | null;
            finishedTime: string | null;
            adviceLines: number; // double
            deliveredLines: number; // double
            overdeliveredLines: number; // double
            underdeliveredLines: number; // double
        }
        export interface AdvicesOutstandingData {
            outstandingAdvices: number; // double
        }
        export interface AisleLevel {
            id: string;
            label: string;
            shuttles: Shuttle[];
        }
        export interface AisleMetricConfiguration {
            id: string;
            label: string;
            type: string;
            format: string;
            display: boolean;
        }
        /**
         * DTO of the necessary data to display an Alert Status.
         */
        export interface AlertStatus {
            /**
             * Status of area.
             */
            status: HealthStatusValue;
            /**
             * Identifies the equipment, area, or location related to this alert
             * TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory
             */
            identifier?: string;
        }
        export interface ApiResponseAreasFacilityAreaArrayCycleTimeAreasConfig {
            areas: /* DTO of the necessary base data to display a Facility Area. */ FacilityArea[];
            metadata: CycleTimeAreasConfig;
        }
        export interface ApiResponseArrayEquipmentFaultsListItemArrayEquipmentFaultsPaginationInfo {
            metadata: EquipmentFaultsPaginationInfo;
            data: EquipmentFaultsListItem[];
        }
        export interface ApiResponseArrayFlattenedInventoryContainerDataArrayInventoryContainerPaginationInfo {
            metadata: InventoryContainerPaginationInfo;
            data: FlattenedInventoryContainerData[];
        }
        export interface ApiResponseArrayFlattenedInventoryContainerEventsDataItemArrayInventoryContainerEventsPaginationInfo {
            metadata: InventoryContainerEventsPaginationInfo;
            data: FlattenedInventoryContainerEventsDataItem[];
        }
        export interface ApiResponseArrayInventoryForecastListingArrayInventoryForecastListingConfig {
            metadata: InventoryForecastListingConfig;
            data: InventoryForecastListing[];
        }
        export interface ApiResponseArrayInventoryHighImpactSKUArrayInventoryHighImpactSkuPaginationInfo {
            metadata: InventoryHighImpactSkuPaginationInfo;
            data: InventoryHighImpactSKU[];
        }
        export interface ApiResponseArrayInventorySkuArrayInventorySkuPaginationInfo {
            metadata: InventorySkuPaginationInfo;
            data: InventorySku[];
        }
        export interface ApiResponseArrayRecentEventQueryResponseArrayRecentEventQueryConfig {
            metadata: RecentEventQueryConfig;
            data: RecentEventQueryResponse[];
        }
        export interface ApiResponseArraySimulationJobArray {
            metadata: {
                [key: string]: any;
            };
            data: SimulationJob[];
        }
        export interface ApiResponseArrayWorkstationContainersListItemArrayPageNumberLimitNumberTotalResultsNumber {
            metadata: {
                totalResults: number; // double
                limit: number; // double
                page: number; // double
            };
            data: WorkstationContainersListItem[];
        }
        export interface ApiResponseArrayWorkstationOrdersListItemArrayPageNumberLimitNumberTotalResultsNumber {
            metadata: {
                totalResults: number; // double
                limit: number; // double
                page: number; // double
            };
            data: WorkstationOrdersListItem[];
        }
        /**
         * DTO that describes the equipment faults data.
         */
        export interface ApiResponseEquipmentFaultsDefinitionEquipmentFaultsConfigDefinition {
            faults: number; // double
            status: string;
            metadata: /* DTO that describes the equipment faults config data. */ EquipmentFaultsConfigDefinition;
        }
        export interface ApiResponseEquipmentOutboundRateEquipmentOutboundRateConfig {
            outboundDivertsHour: number; // double
            status: string;
            metadata: EquipmentOutboundRateConfig;
        }
        export interface ApiResponseEquipmentSummaryAisleDefinitionEquipmentSummaryAisleConfigDefinition {
            status: /* DTO of the necessary data to display a health status. */ HealthStatus;
            totalMovements: number; // double
            storageUtilization: number; // double
            storedTotesPerHour: number; // double
            inventoryTotes: number; // double
            retrievedTotesPerHour: number; // double
            orderTotes: number; // double
            metadata: EquipmentSummaryAisleConfigDefinition;
        }
        export interface ApiResponseEquipmentSummaryWorkstationDefinitionEquipmentSummaryWorkstationConfigDefinition {
            status: /* DTO of the necessary data to display a health status. */ HealthStatus;
            operatorId: number; // double
            iatDonorTotes: number; // double
            linesPerHour: number; // double
            activeTime: string;
            idleTime: string;
            starvedTime: string;
            metadata: EquipmentSummaryWorkstationConfigDefinition;
        }
        export interface ApiResponseEquipmentWorkstationStarvedBlockedTimeSeriesDataEquipmentWorkstationStarvedBlockedTimeSeriesConfig {
            blockedTime: /* Base chart series data */ ChartSeriesData[];
            starvedTime: /* Base chart series data */ ChartSeriesData[];
            metadata: EquipmentWorkstationStarvedBlockedTimeSeriesConfig;
        }
        /**
         * Definition of the necessary data to display the Estimated Completion
         */
        export interface ApiResponseEstimatedOrderCompletionTimesEstimatedOrderCompletionConfig {
            completionTime: string;
            metadata: EstimatedOrderCompletionConfig;
        }
        /**
         * Type that describes the return structure of the data contract response.
         */
        export interface ApiResponseFaultsAreasResponseFaultsAreasConfig {
            areas: /* DTO that describes the fault areas data. */ FaultsFacilityArea[];
            metadata: /* DTO that describes the fault areas config data. */ FaultsAreasConfig;
        }
        /**
         * DTO that defines the relevant details of the inventory accuracy object.
         */
        export interface ApiResponseInventoryAccuracyInventoryAccuracyConfig {
            accuracy: number; // double
            status: string;
            areaFilter: string;
            metadata: InventoryAccuracyConfig;
        }
        /**
         * DTO of the necessary data to display total number of operators at a given time.
         */
        export interface ApiResponseOperatorCountDataOperatorCountsConfig {
            operatorCount: number; // double
            alertStatus: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
            metadata: OperatorCountsConfig;
        }
        export interface ApiResponseOperatorsChartSeriesDataBaseChartConfig {
            operators: /* Base chart series data */ ChartSeriesData[];
            metadata: BaseChartConfig;
        }
        export interface ApiResponseOrderCycleTimeChartDataBaseChartConfig {
            orderCycleTimeChart: /* Base chart series data */ ChartSeriesData[];
            metadata: BaseChartConfig;
        }
        export interface ApiResponseOrderCycleTimeDataOrderCycleTimeConfig {
            orderCycleTimeMinutes: number; // double
            status: string;
            metadata: OrderCycleTimeConfig;
        }
        export interface ApiResponseOrderLineProgressOrderLineProgressConfig {
            lineProgressPercent: number; // double
            metadata: OrderLineProgressConfig;
        }
        export interface ApiResponseOrderLineProgressSeriesDataBaseChartConfig {
            trend: /* Base chart series data */ ChartSeriesData[];
            progress: /* Base chart series data */ ChartSeriesData[];
            metadata: BaseChartConfig;
        }
        /**
         * DTO that defines the relevant details of the projected order fulfillment percentage.
         */
        export interface ApiResponseOrderProgressOrderProgressConfig {
            orderProgressPercentage: number; // double
            metadata: OrderProgressConfig;
        }
        export interface ApiResponseOrderShippedData {
            total: number; // double
            current: number; // double
            past: number; // double
            change: number; // double
            metadata: {
                [key: string]: any;
            };
        }
        export interface ApiResponseOrderThroughputChartSeriesResponseDataBaseChartConfig {
            throughput: /* Base chart series data */ ChartSeriesData[];
            metadata: BaseChartConfig;
        }
        /**
         * DTO that describes the order throughput data.
         */
        export interface ApiResponseOrderThroughputDataOrderThroughputConfig {
            throughputRateLinesPerHour: number; // double
            metadata: /* DTO that describes the order config data. */ OrderThroughputConfig;
        }
        /**
         * DTO that defines the relevant details of the orders outstanding.
         */
        export interface ApiResponseOrdersOutstandingData {
            incompletedTotal: number; // double
            metadata: {
                [key: string]: any;
            };
        }
        /**
         * DTO that defines the relevant details for the current number of orders shipped.
         */
        export interface ApiResponseOrdersShipped {
            total: number; // double
            shipped: number; // double
            metadata: {
                [key: string]: any;
            };
        }
        export interface AppConfig {
            api: AppConfigApi;
            dataUrls?: AppConfigDataUrls;
            settings?: AppConfigSettings;
            site: AppConfigSite;
            packages?: /* Interface used to set the menu group index (order) for the menu. */ AppConfigPackage[];
            featureFlags?: /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting[];
        }
        export interface AppConfigApi {
            baseUrl: string;
            overrides?: AppConfigApiEndpointConfig[];
        }
        export interface AppConfigApiEndpointConfig {
            operationId: string;
            baseUrl: string;
        }
        export interface AppConfigDataUrls {
            tableauProxy: string;
            tableauMovementsAndFaultsDashboard: string;
            tableauMovementsAndFaultsDetailsDashboard: string;
            tableauMovementsDashboard: string;
            tableauHistoricalMovementsAndFaultsDashboard: string;
            tableauShuttleHardwareAnalysis: string;
            mockApi: string;
            insightsSiteName: string;
        }
        /**
         * Interface used to set the menu group index (order) for the menu.
         */
        export interface AppConfigPackage {
            /**
             * The name of the package, which is used to dynamically load the package as it must match the folder name.
             */
            name: string;
            /**
             * lag to set if the package is enabled.
             */
            enabled: boolean;
            /**
             * an array of roles that match auth0 roles
             */
            requiredRoles?: string[];
        }
        /**
         * DTO for a setting with its value set to the most relevant value for the user.
         */
        export interface AppConfigSetting {
            id: string;
            name: AppConfigSettingName | string;
            group?: AppConfigSettingGroupName | string | null;
            description?: string | null;
            dataType: AppConfigSettingDataType;
            source?: AppConfigSettingSource;
            value?: any;
            tags?: {
                [name: string]: string;
            } | null;
            parentSetting?: string | null;
        }
        export type AppConfigSettingArrayOrAppConfigSetting = /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting[] | /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting;
        export type AppConfigSettingDataType = "json" | "string" | "number" | "boolean";
        export type AppConfigSettingGroupName = {
            [key: string]: any;
        } | ("system" | "feature-flags" | "custom-dashboards" | "inventory" | "orders" | "workstations" | "data-explorer" | "tableau");
        /**
         * Relevant informaiton about a setting from the setting log
         */
        export interface AppConfigSettingLog {
            changedBy: string;
            timestamp: string; // date-time
            source: string;
            newValue?: string;
        }
        export interface AppConfigSettingLogsData {
            data: /* Relevant informaiton about a setting from the setting log */ AppConfigSettingLog[];
        }
        export type AppConfigSettingName = {
            [key: string]: any;
        } | ("facility-maps" | "menu" | "selected-facility-id" | "site-time-zone" | "shift-start-end-times" | "user-favorites" | "file-upload-dashboard" | "inbound-overview-dashboard" | "inventory-overview-dashboard" | "outbound-overview-dashboard" | "picking-buffer-area-details-dashboard" | "workstation-overview-dashboard" | "inventory-replenishment-dashboard" | "ict-nav-help-content" | "ict-facility-process-flow-admin-controls" | "ict-facility-process-flow-config-management" | "ict-facility-process-flow-detail-panel" | "ict-facility-process-flow-detail-panel-layout" | "ict-facility-process-flow-edge-animation-effect" | "ict-facility-process-flow-edge-label-alerts" | "ict-facility-process-flow-edge-label-status-indicators" | "ict-facility-process-flow-edge-labels" | "ict-facility-process-flow-graph-config" | "ict-facility-process-flow-node-alerts" | "ict-facility-process-flow-node-drilldown-button" | "ict-facility-process-flow-node-metrics" | "ict-facility-process-flow-polling" | "ict-facility-process-flow-polling-interval" | "data-explorer-debug-data" | "inventory-high-impact-sku-percentage" | "inventory-sku-list-column-order" | "inventory-storage-utilization-aisle-filter-list" | "inventory-storage-utilization-area-filter-list" | "inventory-work-area-code-filter-list" | "inventory-zone-mapping" | "cycle-time-state-range" | "order-complete-event-codes" | "excluded-workstation-list" | "workstation-destination-locations-suffixes" | "workstation-order-delayed-time" | "data-explorer-default-recommendations" | "tableau-top-level-project-name");
        export type AppConfigSettingSource = "default" | "tenant" | "facility" | "user";
        export interface AppConfigSettings {
            logLevel: LogLevel;
        }
        export interface AppConfigSite {
            timezone: string;
        }
        export interface Area {
            id: string;
            alerts?: NodeAlert[];
            label: string;
            metrics: Metric[];
            position: AreaPosition;
            nodeType: "Area" | "Aisle" | "Lift";
            aisleLevels?: AisleLevel[];
            view?: string;
        }
        /**
         * DTO of the necessary base data to display a Facility Area.
         */
        export interface AreaOperators {
            /**
             * Number of operators actively working in area.
             */
            activeOperators: number; // double
            /**
             * Lowest number of recommended operators for the area.
             */
            lowRecOperators: number; // double
            /**
             * Highest number of recommended operators for the area.
             */
            highRecOperators: number; // double
            /**
             * Max number of recommended operators for the area.
             */
            maxOperators?: number; // double
            /**
             * Alert status object.
             */
            alertStatus?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
            /**
             * Number of orders in queue.
             */
            queuedOrders?: number; // double
            /**
             * Orders picked per operator per hour.
             */
            pickRate?: number; // double
            operatorStatus?: /* DTO of the necessary data to display a health status. */ HealthStatus;
        }
        export interface AreaPosition {
            x: number; // double
            y: number; // double
        }
        export interface Attributes {
            randomSeeds: number /* double */[];
            uploadId: string;
            runnerBackend: string;
        }
        export interface AuthTicket {
            userId: string;
            ticket: string;
        }
        export interface AvailableAisles {
            availableAisles: string[];
        }
        export interface AvailableLevels {
            availableLevels: string[];
        }
        export interface BaseChartConfig {
            /**
             * Min value for the y-axis
             */
            yMin?: number; // double
            /**
             * Max value for the y-axis
             */
            yMax?: number; // double
            /**
             * Value for the high range
             */
            yHighBand?: number; // double
            /**
             * Value for the low range
             */
            yLowBand?: number; // double
            /**
             * What the X-axis value should be formatted for display in the chart
             */
            xAxisValueFormatType?: string;
            /**
             * What the Y-axis value should be formatted for display in the chart
             */
            yAxisValueFormatType?: string;
        }
        export interface BaseFilterType {
            type: "multiple" | "single";
        }
        /**
         * Timestamp class for BigQuery.
         */
        export interface BigQueryTimestamp {
            value: string;
        }
        export interface Boolean {
        }
        /**
         * Base chart series data
         */
        export interface ChartSeriesData {
            name: string;
            value: number; // double
        }
        /**
         * Chart series data with percentage validation
         */
        export interface ChartSeriesPercentageData {
            name: string;
            value: number; // double
            isValidPercentage: boolean;
        }
        export interface ClearCacheRequest {
            scope: "all" | "metrics" | "graph";
        }
        export interface ClearCacheResponse {
            success: Boolean;
        }
        export interface ComputeResources {
            maxDuration: string | null;
            preemptible: boolean;
            instanceType: string | null;
            memoryPerTaskMb: number | null; // double
            cpuPerTask: number | null; // double
        }
        export interface CuratedTableColumn {
            name: string;
            renderType: CuratedTableColumnValueRenderType;
            value: any;
        }
        export type CuratedTableColumnValueRenderType = "number" | "string" | "boolean" | "null";
        export interface CuratedTableRow {
            columns: CuratedTableColumn[];
        }
        export interface CurrentInventory {
            forwardPick: number; // double
            reserveStorage: number; // double
        }
        export type CustomerOrderEstimatedCompletion = number; // double
        export interface CustomerOrderEstimatedCompletionData {
            estimatedCompletionMinutes: CustomerOrderEstimatedCompletion /* double */;
        }
        export interface CustomerOrderLineThroughputSeriesData {
            throughput: /* Base chart series data */ ChartSeriesData[];
        }
        export interface CustomerOrderProgress {
            orderProgressPercentage: number; // double
        }
        /**
         * DTO that describes the order throughput data.
         */
        export interface CustomerOrderThroughputData {
            throughputRateOrdersPerHour: number; // double
        }
        export interface CycleTimeAreasConfig {
            targetTime: string;
        }
        export interface DataExplorerAgentSearchResponse {
            sessionId: string;
            status: string;
            messages: string[];
        }
        export interface DataExplorerRecommendations {
            recommendations: DataExplorerRecommendationsData[];
        }
        export interface DataExplorerRecommendationsData {
            prompt: string;
        }
        export interface DataExplorerResult {
            debugData?: AIMLResponse;
            eChartCode?: null;
            timestamp: string; // date-time
            queryResults?: any[];
            isRecommended: Recommended;
            isBookmarked?: boolean;
            answer?: string;
            prompt: string;
            id: string;
        }
        export interface DataExplorerResults {
            data: DataExplorerResult[];
        }
        export interface DefaultConfigSeedingResult {
            successful: /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting[];
            unsuccessful: /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting[];
            existing: /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting[];
        }
        export interface DeletedRecordsResult {
            recordsDeleted: number; // double
            message?: string;
        }
        export interface DerivedStructData {
            extractive_answers: ExtractiveAnswer[];
            link: string;
        }
        export type DiagnosticsInfrastructureContract = /* DTO that describes the Diagnostics Infrastructure Object. */ DiagnosticsInfrastructureObject[];
        /**
         * DTO that describes the Diagnostics Infrastructure Object.
         */
        export interface DiagnosticsInfrastructureObject {
            name: string;
            lastUpdate: string;
            status: string | /* DTO that describes the Diagnostics Infrastructure Object Status. */ DiagnosticsInfrastructureObjectStatus;
            cpuPercentage: number; // double
            memoryUsagePercentage: number; // double
            diskUsagePercentage: number; // double
            labels: string[];
        }
        /**
         * DTO that describes the Diagnostics Infrastructure Object Status.
         */
        export type DiagnosticsInfrastructureObjectStatus = "Online" | "Offline";
        export interface Edge {
            id: string;
            source: string;
            target: string;
            direction: EdgeDirection;
            metrics: Metric[];
        }
        export type EdgeDirection = "upstream" | "downstream";
        export interface EnterpriseSearchDocument {
            derivedStructData: DerivedStructData;
            id: string;
            name: string;
        }
        export interface EnterpriseSearchResponse {
            document: EnterpriseSearchDocument;
            id: string;
        }
        /**
         * DTO that defines the relevant details of the inventory recent activities.
         */
        export interface EquipmentActiveFaults {
            activeFaultsTable: EquipmentActiveFaultsDefinition[];
        }
        export interface EquipmentActiveFaultsDefinition {
            area: string;
            duration: number; // double
            deviceReason: string;
        }
        /**
         * DTO that describes the equipment faults config data.
         */
        export interface EquipmentFaultsConfigDefinition {
            lowRange?: number; // double
            highRange?: number; // double
            max: number; // double
        }
        /**
         * DTO that describes the equipment faults data.
         */
        export interface EquipmentFaultsDefinition {
            faults: number; // double
            status: string;
        }
        export interface EquipmentFaultsDeviceIdRequest {
            filters?: any;
            end_date?: string; // date-time
            start_date?: string; // date-time
        }
        export interface EquipmentFaultsListItem {
            timestamp: string;
            durationMinutes: number; // double
            status: string;
            aisle: string;
            level: string;
            device: string;
            deviceType: string;
        }
        export interface EquipmentFaultsPaginationInfo {
            page: number; // double
            limit: number; // double
            totalResults?: number; // double
        }
        /**
         * Describes the contract for the data returned by the api.
         */
        export interface EquipmentFaultsSeriesContractData {
            faultsOrderData: /* Describes the equipment faults series data returned by the service. */ EquipmentFaultsSeriesData[];
            orderData: /* Describes the throughput rates series data returned by the service. */ EquipmentThroughputSeriesData[];
        }
        /**
         * Describes the equipment faults series data returned by the service.
         */
        export interface EquipmentFaultsSeriesData {
            /**
             * Hour of the day that the throughput is measured. Eg: "16".
             */
            name: string;
            /**
             * Number of faults for that hour.
             */
            value: number; // double
        }
        export interface EquipmentOutboundRate {
            outboundDivertsHour: number; // double
            status: string;
        }
        /**
         * Data contract for the response from the api
         */
        export type EquipmentOutboundRateApiResponse = ApiResponseEquipmentOutboundRateEquipmentOutboundRateConfig;
        export interface EquipmentOutboundRateConfig {
            max: number; // double
            rangeColor: string;
        }
        export interface EquipmentSummaryAisleConfigDefinition {
            metrics: AisleMetricConfiguration[];
        }
        export interface EquipmentSummaryAisleDefinition {
            status: /* DTO of the necessary data to display a health status. */ HealthStatus;
            totalMovements: number; // double
            storageUtilization: number; // double
            storedTotesPerHour: number; // double
            inventoryTotes: number; // double
            retrievedTotesPerHour: number; // double
            orderTotes: number; // double
        }
        /**
         * DTO that describes the areas summary data.
         */
        export interface EquipmentSummaryAreaContract {
            areas: /* DTO that describes the equipment summary area data. */ EquipmentSummaryAreaDefinition[];
        }
        /**
         * DTO that describes the equipment summary area data.
         */
        export interface EquipmentSummaryAreaDefinition {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
            alertStatus?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
            status: /* DTO of the necessary data to display a health status. */ HealthStatus;
            movementsPerFault: number; // double
            outboundRatePerHour: number; // double
            downTimeMinutes: number; // double
            qualityPercentage: number; // double
            alerts?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus[];
        }
        export interface EquipmentSummaryWorkstationConfigDefinition {
            metrics: WorkstationMetricConfiguration[];
        }
        export interface EquipmentSummaryWorkstationDefinition {
            status: /* DTO of the necessary data to display a health status. */ HealthStatus;
            operatorId: number; // double
            iatDonorTotes: number; // double
            linesPerHour: number; // double
            activeTime: string;
            idleTime: string;
            starvedTime: string;
        }
        /**
         * Describes the throughput rates series data returned by the service.
         */
        export interface EquipmentThroughputSeriesData {
            /**
             * Hour of the day that the throughput is measured. Eg: "16".
             */
            name: string;
            /**
             * Throughput for that hour.
             */
            value: number; // double
        }
        /**
         * Definiton of the data for the data contract for workstation active faults
         */
        export interface EquipmentWorkstationActiveFaultsData {
            /**
             * Description of the fault
             */
            description: string;
            /**
             * Start time of the fault
             */
            startTime: string;
            /**
             * How long the fault has been active, in seconds
             */
            duration: number; // double
            /**
             * Fault ID
             */
            faultId: string;
        }
        export interface EquipmentWorkstationAisleMovementData {
            retrieval: number; // double
            storage: number; // double
            positioning: number; // double
            iat: number; // double
            shuffle: number; // double
            bypass: number; // double
        }
        export interface EquipmentWorkstationLineRatesSeriesData {
            lineRates: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * Expected return data object format for the response
         */
        export interface EquipmentWorkstationOperatorActivityData {
            /**
             * Id of the operator
             */
            operatorId: string;
            /**
             * Time the operator started working at the workstation
             */
            startTime: string;
            /**
             * Time the operator stopped working at the workstation, null if operator is still active
             */
            endTime: string | null;
            /**
             * How long the operator was/currently has been at the workstation, in seconds
             */
            totalTimeDuration: number; // double
            /**
             * Time the workstation was idle, in seconds
             */
            idleTime: number; // double
            /**
             * Time the workstation was starved for picks, in seconds
             */
            starvedTime: number; // double
            /**
             * Time the workstation was blocked for picks, in seconds
             */
            blockedTime: number; // double
            /**
             * The workstations per hour line rates
             */
            lineRatePerHour: number; // double
            /**
             * The workstations per hour weighted line rates
             */
            weightedLineRatePerHour: number; // double
        }
        export type EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse = ApiResponseEquipmentWorkstationStarvedBlockedTimeSeriesDataEquipmentWorkstationStarvedBlockedTimeSeriesConfig;
        export interface EquipmentWorkstationStarvedBlockedTimeSeriesConfig {
            /**
             * Min value for the y-axis
             */
            yMin?: number; // double
            /**
             * Max value for the y-axis
             */
            yMax?: number; // double
            /**
             * Value for the high range
             */
            yHighBand?: number; // double
            /**
             * Value for the low range
             */
            yLowBand?: number; // double
            /**
             * What the X-axis value should be formatted for display in the chart
             */
            xAxisValueFormatType?: string;
            /**
             * What the Y-axis value should be formatted for display in the chart
             */
            yAxisValueFormatType?: string;
            blockedTime: /* Config for a series in chart */ SeriesConfig;
            starvedTime: /* Config for a series in chart */ SeriesConfig;
        }
        export interface EquipmentWorkstationStarvedBlockedTimeSeriesData {
            blockedTime: /* Base chart series data */ ChartSeriesData[];
            starvedTime: /* Base chart series data */ ChartSeriesData[];
        }
        export interface EstimatedOrderCompletionConfig {
            targetTime: string;
        }
        /**
         * Definition of the necessary data to display the Estimated Completion
         */
        export interface EstimatedOrderCompletionTimes {
            completionTime: string;
        }
        export interface Event {
            eventType: string;
            state: string;
            timestamp: string;
            taskIndex: number | null; // double
            jobId: string;
            randomSeeds?: number /* double */[];
            tags?: string[];
            computeResources?: ComputeResources;
            extraArgs?: {
                simulationTime?: string;
                variant: string;
                runWithDocker: boolean;
                solution: string;
                type: string;
                version: string;
            } | null;
            taskCount?: number; // double
            userId?: string;
            uploadId?: string;
            runEnv?: string | null;
            runnerBackend?: string;
            unifiedRunnerVersion?: null;
            taskExitCode?: number; // double
        }
        export interface ExtraArgs {
            simulationTime?: string;
            variant: string;
            runWithDocker: boolean;
            solution: string;
            type: string;
            version: string;
        }
        export interface ExtractiveAnswer {
            content: string;
            pageNumber: string;
        }
        /**
         * DTO of the necessary base data to display a Facility Area.
         */
        export interface FacilityArea {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
            alertStatus?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
        }
        /**
         * DTO for a the facility config settings with its value set to the most relevant value for the user.
         */
        export type FacilityConfigSettings = /* DTO for a setting with its value set to the most relevant value for the user. */ AppConfigSetting[];
        export interface FaultAvgDurationByStatusRequest {
            filters?: any;
            isAscendingOrder?: boolean;
            endDate: string; // date-time
            startDate: string; // date-time
        }
        export interface FaultAvgDurationSeriesData {
            avgDuration: /* Base chart series data */ ChartSeriesData[];
        }
        export interface FaultCountGroupedByRequest {
            filters?: any;
            isAscendingOrder?: boolean;
            groupByColumn: "aisle" | "level" | "device_functional_type" | "device_code" | "reason_name";
            endDate: string; // date-time
            startDate: string; // date-time
        }
        export interface FaultCountGroupedSeriesData {
            faultCounts: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * DTO for a Fault Event
         */
        export interface FaultEvent {
            /**
             * Time the event happened. UTC, ISO 8601.
             */
            time: string;
            /**
             * Area that the fault occured in
             */
            area: string;
            /**
             * Desciption of the fault event
             */
            description: string;
        }
        export type FaultEventContract = /* DTO for a Fault Event */ FaultEvent[];
        export interface FaultsAisleListRequest {
            filters?: any;
            end_date?: string; // date-time
            start_date?: string; // date-time
        }
        export interface FaultsArea {
            totalFaults: number; // double
            maxFaultsAllowed: number; // double
            downtimeMinutes: number; // double
            status: string;
        }
        /**
         * DTO that describes the fault areas config data.
         */
        export interface FaultsAreasConfig {
            min: number; // double
            max: number; // double
        }
        /**
         * Type that describes the return structure of the data contract response.
         */
        export interface FaultsAreasResponse {
            areas: /* DTO that describes the fault areas data. */ FaultsFacilityArea[];
        }
        export interface FaultsDeviceTypeListRequest {
            start_date?: string; // date-time
            end_date?: string; // date-time
            filters?: any;
        }
        /**
         * DTO that describes the fault areas data.
         */
        export interface FaultsFacilityArea {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
            alertStatus?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
            faults: FaultsArea;
        }
        export interface FaultsLevelListRequest {
            filters?: any;
            end_date?: string; // date-time
            start_date?: string; // date-time
        }
        export interface FaultsMovementsSeriesData {
            movementCounts: /* Base chart series data */ ChartSeriesData[];
            movementsPerFault: /* Base chart series data */ ChartSeriesData[];
        }
        export interface FaultsMovementsSeriesRequest {
            start_date: string; // date-time
            end_date: string; // date-time
            filters?: any;
        }
        export interface FaultsStatusListRequest {
            filters?: any;
            end_date?: string; // date-time
            start_date?: string; // date-time
        }
        export interface FlattenedInventoryContainerData {
            free_cycle_count: string | null;
            data_updated: string | null;
            last_cycle_count: string | null;
            last_activity_date: string | null;
            quantity: number; // double
            sku: string;
            zone: string;
            location_id: string;
            container_id: string;
        }
        export interface FlattenedInventoryContainerEventsDataItem {
            timestamp: string;
            event: string;
            destinationContainer: string;
            operator: string;
            quantity: number; // double
            workstationCode: string;
            sku: string;
        }
        export interface ForecastedInventory {
            twoDayForwardPick: number | null; // double
            twoDayDemand: number | null; // double
            forwardPickTomorrow: number | null; // double
            knownDemand: number | null; // double
            demandTomorrow: number | null; // double
            averageDemand: number; // double
            averageReplenishment: number; // double
        }
        export interface FromArea {
            area_id: string;
            number_to_move: number; // double
        }
        export type GetRolesResponse = Role[];
        export type HealthCheckStatus = "ONLINE" | "OFFLINE" | "NOT_IN_USE" | "UNHEALTHY" | "UNKOWN";
        /**
         * DTO of the necessary data to display a health status.
         */
        export type HealthStatus = "ok" | "caution" | "critical" | "warning";
        export type HealthStatusValue = "ok" | "caution" | "critical" | "warning";
        /**
         * DTO for objects related to the excel export.
         */
        export interface IncludedColumns {
            [name: string]: boolean;
        }
        /**
         * DTO that defines the relevant details of the inventory accuracy object.
         */
        export interface InventoryAccuracy {
            accuracy: number; // double
            status: string;
            areaFilter: string;
        }
        export interface InventoryAccuracyConfig {
            seg1: number; // double
            seg2: number; // double
            seg3: number; // double
        }
        export interface InventoryAdvicesInProgressData {
            inProgressAdvices: number; // double
        }
        /**
         * DTO that defines the relevant details the inventory area dropdown.
         */
        export interface InventoryAreaFilterDefinition {
            areaFilterTable: string[];
        }
        /**
         * DTO that defines the relevant details of the bin location objects
         */
        export interface InventoryBinLocation {
            binLocation: string;
            locationSide: "Left" | "Right";
            status: "Occupied" | "Empty";
            containerType?: string;
            skus: {
                quantity: number; // double
                sku: string;
            }[];
        }
        export interface InventoryBinLocationsResponse {
            data: /* DTO that defines the relevant details of the bin location objects */ InventoryBinLocation[];
        }
        export interface InventoryContainerEventsKpiContract {
            events: InventoryContainerEventsKpiData[];
        }
        export interface InventoryContainerEventsKpiData {
            averageDailyPickEvents: number; // double
            pickEventsToday: number; // double
            averageDailyCycleCount: number; // double
            cycleCountEventsToday: number; // double
            dataUpdated: string | null;
            lastCycleCount: string | null;
            lastActivityDate: string | null;
            quantity: number; // double
            sku: string;
            zone: string;
            locationId: string;
            containerId: string;
        }
        export interface InventoryContainerEventsPaginationInfo {
            page: number; // double
            limit: number; // double
            totalResults?: number; // double
        }
        export interface InventoryContainerPaginationInfo {
            page: number; // double
            limit: number; // double
            totalResults?: number; // double
        }
        /**
         * DTO that defines the relevant details of the inventory forecast objects.
         */
        export interface InventoryForecastDataAnalysisTimestamp {
            dataUpdateTimestamp?: string;
            analysisPerformedTimestamp: string;
        }
        export interface InventoryForecastListing {
            forecast: ForecastedInventory;
            projected: ProjectedInventory;
            current: CurrentInventory;
            sku: string;
        }
        export interface InventoryForecastListingConfig {
            page: number; // double
            limit: number; // double
            totalResults?: number; // double
        }
        export type InventoryForecastListingData = ApiResponseArrayInventoryForecastListingArrayInventoryForecastListingConfig;
        export interface InventoryForecastSkuLocationAreas {
            data: InventoryForecastSkuLocationByArea[];
        }
        export interface InventoryForecastSkuLocationByArea {
            details: InventoryForecastSkuLocationDetails[];
            area: string;
        }
        export interface InventoryForecastSkuLocationDetails {
            lastActivityDate: string;
            zone: string;
            conditionCode: string;
            containerDimensions?: string;
            skuSize?: string;
            uom?: string;
            containerQuantity?: number; // double
            quantity: number; // double
            containerCount: number; // double
            containerId: string | null;
            locationType: string;
            locationId: string;
        }
        export interface InventoryForecastSkuOrderDetails {
            allocatedQty: number; // double
            orderLines: number; // double
            shipDate: string;
            allocationDate: string;
            priority: string;
            orderId: string;
            skuId: string;
        }
        export interface InventoryForecastSkuOrders {
            skuId: string;
            openOrderCount: number; // double
            data: InventoryForecastSkuOrderDetails[];
        }
        /**
         * Configuration to hold in a contract for Handling Units Trayed.
         */
        export interface InventoryHandlingUnitsTrayedConfig {
            timePeriodInHours: number; // double
        }
        /**
         * Contract for Handling Units Trayed.
         */
        export interface InventoryHandlingUnitsTrayedContract {
            metadata: /* Configuration to hold in a contract for Handling Units Trayed. */ InventoryHandlingUnitsTrayedConfig;
            data: /* Data to hold in a contract for Handling Units Trayed. */ InventoryHandlingUnitsTrayedData;
        }
        /**
         * Data to hold in a contract for Handling Units Trayed.
         */
        export interface InventoryHandlingUnitsTrayedData {
            handlingUnitsTrayed: number; // double
        }
        export interface InventoryHighImpactSKU {
            sku: string;
            accuracy: number; // double
            storageArea: string;
            quantity: number; // double
            cubeUtilization: number; // double
            daysOnHand: number; // double
        }
        export type InventoryHighImpactSKUContract = ApiResponseArrayInventoryHighImpactSKUArrayInventoryHighImpactSkuPaginationInfo;
        export interface InventoryHighImpactSkuPaginationInfo {
            page: number; // double
            limit: number; // double
            totalResults?: number; // double
        }
        export interface InventoryKnownDemand {
            percentage: number; // double
            quantity: number; // double
        }
        export type InventoryPerformanceSeriesContract = InventoryPerformanceSeriesData[];
        export interface InventoryPerformanceSeriesData {
            name: string;
            series: /* Base chart series data */ ChartSeriesData[];
        }
        export interface InventoryReplenishmentDetails {
            dailyReplenishments: /* Base chart series data */ ChartSeriesData[];
            dailyPendingOrders: /* Base chart series data */ ChartSeriesData[];
            dailyCycleTimes: /* Base chart series data */ ChartSeriesData[];
            shiftData: /* DTO that defines the relevant details of the inventory replenishment details. */ ShiftData[];
            shiftTimes: {
                [key: string]: any;
            };
        }
        export interface InventorySku {
            sku: string;
            description: string;
            daysOnHand: number; // double
            status: string;
            averageDailyQuantity: number; // double
            averageDailyOrders: number; // double
            targetMultiplicity: number; // double
            velocityClassification: string;
            quantityAvailable: number; // double
            quantityAllocated: number; // double
            totalQuantity: number; // double
            locations: number; // double
            skuPositions: number; // double
            maxContainers: number; // double
            contOverage: number; // double
            latestInventorySnapshotTimestamp?: string;
            latestCycleCountTimestamp: string;
            latestActivityDateTimestamp: string;
        }
        export interface InventorySkuForecastDetails {
            confidence: number; // double
            knownDemand: InventoryKnownDemand;
            shortTermDaily: number; // double
            shortTermDailyDays: number; // double
            longTermDaily: number; // double
            longTermDailyDays: number; // double
            nonZeroDemand: number; // double
            zeroDemandIntermittency: number; // double
            predictedDemandSeries: /* Base chart series data */ ChartSeriesData[];
            actualDemandSeries: /* Base chart series data */ ChartSeriesData[];
            confidenceLowSeries: /* Base chart series data */ ChartSeriesData[];
            confidenceHighSeries: /* Base chart series data */ ChartSeriesData[];
        }
        export interface InventorySkuPaginationInfo {
            page: number; // double
            limit: number; // double
            totalResults?: number; // double
        }
        export interface InventoryStockDistributionAtData {
            atInventory: /* Chart series data with percentage validation */ ChartSeriesPercentageData[];
        }
        export interface InventoryStockDistributionNoData {
            noInventory: /* Chart series data with percentage validation */ ChartSeriesPercentageData[];
        }
        export interface InventoryStockDistributionOverData {
            overInventory: /* Chart series data with percentage validation */ ChartSeriesPercentageData[];
        }
        export interface InventoryStockDistributionUnderData {
            underInventory: /* Chart series data with percentage validation */ ChartSeriesPercentageData[];
        }
        /**
         * DTO that defines the relevant details the inventory storage utilization tile.
         */
        export interface InventoryStorageUtilization {
            utilizationPercentage: number; // double
        }
        /**
         * DTO that defines the relevant details of the inventory upload history object.
         */
        export interface InventoryUploadRecentActivity {
            date: string;
            knownOrderCount: number; // double
            knownOrderLineCount: number; // double
        }
        export interface LineProgressFacilityArea {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
            alertStatus?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
            /**
             * Orderline progress area data
             */
            orderLines: /* DTO that defines the relevant details for the current orderline progress per area. */ OrderAreasLineProgress;
        }
        export type LogLevel = 20 | 30 | 40 | 50 | 60 | 100 | 1000;
        export interface Metric {
            id: string;
            type: string;
            value: number /* double */ | string | null;
            panelGroup?: string;
            units?: string;
        }
        /**
         * Represents a metric configuration
         */
        export interface MetricConfig {
            /**
             * Unique identifier for the metric configuration
             */
            id: string;
            /**
             * Name of the metric
             */
            metricName: string;
            /**
             * Type of configuration (e.g., 'node', 'edge')
             */
            configType: string;
            /**
             * Name of the node this metric is associated with
             */
            nodeName?: string;
            /**
             * Type of fact this metric represents
             */
            factType?: string;
            /**
             * Whether the metric is enabled
             * For default configurations, this is an hstore where keys are facility IDs and values are booleans
             * For custom configurations, this is a boolean indicating if the metric is enabled for that facility
             * Can be undefined for default configurations if no facilities have enabled/disabled the metric
             * Can be null for custom configurations where enabled status is not applicable
             */
            enabled?: /**
             * Whether the metric is enabled
             * For default configurations, this is an hstore where keys are facility IDs and values are booleans
             * For custom configurations, this is a boolean indicating if the metric is enabled for that facility
             * Can be undefined for default configurations if no facilities have enabled/disabled the metric
             * Can be null for custom configurations where enabled status is not applicable
             */
            boolean | /* Construct a type with a set of properties K of type T */ RecordStringBoolean | null;
            /**
             * Whether the metric is active
             * For default configurations, this is an hstore where keys are facility IDs and values are booleans
             * For custom configurations, this is a boolean indicating if the metric is active for that facility
             * Can be undefined for default configurations if no facilities have active/inactive status
             */
            active?: /**
             * Whether the metric is active
             * For default configurations, this is an hstore where keys are facility IDs and values are booleans
             * For custom configurations, this is a boolean indicating if the metric is active for that facility
             * Can be undefined for default configurations if no facilities have active/inactive status
             */
            boolean | /* Construct a type with a set of properties K of type T */ RecordStringBoolean;
            /**
             * Whether this is a custom configuration (true) or default configuration (false)
             */
            isCustom: boolean;
            /**
             * The facility ID this configuration applies to
             * For default configurations, this is 'default'
             * For custom configurations, this is the specific facility ID
             */
            facilityId: string;
        }
        export interface MetricGroup {
            title: string;
            metrics: Metric[];
        }
        export interface NodeAlert {
            color: string;
            value: string;
        }
        export interface OperatorAlertStatus {
            /**
             * Identifies the equipment, area, or location related to this alert
             * TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory
             */
            identifier?: string;
            status: HealthStatusValue;
            message?: string;
        }
        /**
         * DTO of the necessary data to display total number of operators at a given time.
         */
        export interface OperatorCountData {
            operatorCount: number; // double
            alertStatus: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
        }
        export interface OperatorCountsConfig {
            startDateTime: string;
            endDateTime: string;
            lowRecOperators: number; // double
            highRecOperators: number; // double
            maxOperators: number; // double
        }
        export interface OperatorFacilityArea {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
            alertStatus: OperatorAlertStatus;
        }
        export type OperatorsChartSeriesApiResponse = ApiResponseOperatorsChartSeriesDataBaseChartConfig;
        export interface OperatorsChartSeriesData {
            operators: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * DTO that defines the relevant details for the current orderline progress per area.
         */
        export interface OrderAreasLineProgress {
            orderLineProgress: number; // double
            orderLinesCompleted: number; // double
            totalOrderLines: number; // double
        }
        export interface OrderCustomerLineProgressSeriesData {
            progress: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * DTO that describes the order line throughput data.
         */
        export interface OrderCustomerLineThroughputData {
            throughputRateOrderLinesPerHour: number; // double
        }
        export interface OrderCycleTime {
            orderCycleTimeMinutes: number; // double
        }
        export type OrderCycleTimeChartApiResponse = ApiResponseOrderCycleTimeChartDataBaseChartConfig;
        export interface OrderCycleTimeChartData {
            orderCycleTimeChart: /* Base chart series data */ ChartSeriesData[];
        }
        export interface OrderCycleTimeConfig {
            max: number; // double
            highRange: number; // double
        }
        export interface OrderCycleTimeData {
            orderCycleTimeMinutes: number; // double
            status: string;
        }
        export interface OrderLineProgress {
            lineProgressPercent: number; // double
        }
        export interface OrderLineProgressAreasData {
            areas: LineProgressFacilityArea[];
        }
        export type OrderLineProgressChartApiResponse = ApiResponseOrderLineProgressSeriesDataBaseChartConfig;
        export interface OrderLineProgressConfig {
            lowRange: number; // double
            highRange: number; // double
            max: number; // double
        }
        export interface OrderLineProgressSeriesData {
            trend: /* Base chart series data */ ChartSeriesData[];
            progress: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * DTO that describes the order line throughput data.
         */
        export interface OrderLineThroughputData {
            totalCompletedOrderLines: number; // double
        }
        export interface OrderPerformanceFulfillmentData {
            orderFulfillment: number; // double
        }
        export interface OrderPickLineThroughputSeriesData {
            throughput: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * DTO that defines the relevant details of the projected order fulfillment percentage.
         */
        export interface OrderProgress {
            orderProgressPercentage: number; // double
        }
        export interface OrderProgressConfig {
            seg1: number; // double
            seg2: number; // double
            seg3: number; // double
        }
        export interface OrderProgressSeriesData {
            progress: /* Base chart series data */ ChartSeriesData[];
        }
        export interface OrderShippedData {
            total: number; // double
            current: number; // double
            past: number; // double
            change: number; // double
        }
        /**
         * Describes the contract for the data returned by the api.
         */
        export type OrderThroughputChartApiResponse = ApiResponseOrderThroughputChartSeriesResponseDataBaseChartConfig;
        export interface OrderThroughputChartSeriesResponseData {
            throughput: /* Base chart series data */ ChartSeriesData[];
        }
        /**
         * DTO that describes the order config data.
         */
        export interface OrderThroughputConfig {
            lowRange: number; // double
            max: number; // double
        }
        /**
         * DTO that describes the order throughput data.
         */
        export interface OrderThroughputData {
            throughputRateLinesPerHour: number; // double
        }
        /**
         * DTO that defines the relevant details of the orders outstanding.
         */
        export interface OrdersOutstandingData {
            incompletedTotal: number; // double
        }
        export interface OrdersPickCycleCountData {
            cycleCount: number; // double
        }
        /**
         * DTO that defines the relevant details for the current number of orders shipped.
         */
        export interface OrdersShipped {
            total: number; // double
            shipped: number; // double
        }
        export interface PaginatedRequest {
            start_date: string; // date-time
            end_date: string; // date-time
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
        }
        export interface PaginatedRequestNoDates {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
        }
        /**
         * From T, pick a set of properties whose keys are in the union K
         */
        export interface PickAlertStatusExcludeKeyofAlertStatusStatusOrMessage {
            /**
             * Identifies the equipment, area, or location related to this alert
             * TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory
             */
            identifier?: string;
        }
        /**
         * From T, pick a set of properties whose keys are in the union K
         */
        export interface PickFacilityAreaExcludeKeyofFacilityAreaAlertStatus {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
        }
        export type PickOrderArea = "shipping" | "packing" | "picking";
        export type PostEquipmentFaultsListResponse = ApiResponseArrayEquipmentFaultsListItemArrayEquipmentFaultsPaginationInfo;
        export type PostInventoryContainerEventsListResponse = ApiResponseArrayFlattenedInventoryContainerEventsDataItemArrayInventoryContainerEventsPaginationInfo;
        export type PostInventoryContainersListResponse = ApiResponseArrayFlattenedInventoryContainerDataArrayInventoryContainerPaginationInfo;
        export type PostInventorySkusListResponse = ApiResponseArrayInventorySkuArrayInventorySkuPaginationInfo;
        export interface ProcessFlowDetailsResponse {
            metricGroups: MetricGroup[];
        }
        export interface ProcessFlowResponse {
            areas: Area[];
            edges: Edge[];
            lastProcessedTime: string;
        }
        export interface ProjectedInventory {
            projectedForwardPick: number; // double
            allocatedOrders: number; // double
            pendingPicks: number; // double
            pendingReplenishment: number; // double
        }
        /**
         * DTO that defines the relevant details of the projected order fulfillment percentage.
         */
        export interface ProjectedOrderFulfillment {
            projectedOrderFulfillmentPercentage: number; // double
        }
        export interface RecentEventQueryConfig {
            page: number; // double
            limit: number; // double
            totalResults: number; // double
        }
        export interface RecentEventQueryResponse {
            time: /* Timestamp class for BigQuery. */ BigQueryTimestamp;
            tenant: string;
            facility: string;
            description: string;
            eventType: string;
            equipmentId: string;
            eventStartTime: /* Timestamp class for BigQuery. */ BigQueryTimestamp;
            totalTime: number; // double
            faultCode: string;
        }
        export type Recommended = "is_recommended" | "not_recommended" | "not_provided";
        /**
         * Construct a type with a set of properties K of type T
         */
        export interface RecordStringBoolean {
            [name: string]: boolean;
        }
        export interface RequestBody {
            from_areas: FromArea[];
            to_area_id: string;
        }
        export interface ResendEmailVerificationResponse {
            success: boolean;
            message: string;
            isSocialAuth?: boolean;
        }
        export interface Role {
            id: string;
            name: string;
            description?: string;
            roleType: RoleType;
        }
        export type RoleType = "internal" | "facility" | "admin";
        /**
         * Config for a series in chart
         */
        export interface SeriesConfig {
            /**
             * Min value for the y-axis
             */
            yMin?: number; // double
            /**
             * Max value for the y-axis
             */
            yMax?: number; // double
            /**
             * Value for the high range
             */
            yHighBand?: number; // double
            /**
             * Value for the low range
             */
            yLowBand?: number; // double
            /**
             * What the X-axis value should be formatted for display in the chart
             */
            xAxisValueFormatType?: string;
            /**
             * What the Y-axis value should be formatted for display in the chart
             */
            yAxisValueFormatType?: string;
            type?: string;
            color?: string;
            icon?: string;
        }
        /**
         * DTO that defines the relevant details of the inventory replenishment details.
         */
        export interface ShiftData {
            firstShift: /* Base chart series data */ ChartSeriesData[];
            secondShift?: /* Base chart series data */ ChartSeriesData[];
            thirdShift?: /* Base chart series data */ ChartSeriesData[];
        }
        export interface Shuttle {
            id: string;
            name: string;
            metrics: Metric[];
        }
        export interface SimulationJob {
            jobId: string;
            events: Event[];
            tags: string[];
            lastUpdate: string;
            tasks: Tasks;
            createTime: string;
            userId: string;
            taskCount: number; // double
            state: string;
            outputReady: boolean;
            attributes: Attributes;
            timeTaken: number | null; // double
            uploadId: string;
        }
        /**
         * Defines a column to add to a ORDER BY clause in a SQL statement.
         */
        export interface SortField {
            columnName: string;
            isDescending: boolean;
        }
        export interface Task {
            taskIndex: number; // double
            lastUpdate: string;
            state: string;
            timeTaken: number | null; // double
            visState: string;
            exitCode: number; // double
        }
        export interface TaskTypeData {
            demand?: /* Base chart series data */ ChartSeriesData[];
            topOff?: /* Base chart series data */ ChartSeriesData[];
            relocation?: /* Base chart series data */ ChartSeriesData[];
        }
        export interface Tasks {
            [name: string]: Task;
        }
        /**
         * DTO that describes the order throughput data by area.
         */
        export interface ThroughputAreaData {
            throughputRate: number; // double
            maxThroughputCapacity?: number; // double
            minThroughputTarget?: number; // double
            maxThroughputTarget?: number; // double
            status?: /* DTO of the necessary data to display a health status. */ HealthStatus;
        }
        export interface ThroughputFacilityArea {
            /**
             * Id of area.
             */
            id: string;
            /**
             * Name of the area.
             */
            name: string;
            /**
             * Operator area data.
             */
            operators?: /* DTO of the necessary base data to display a Facility Area. */ AreaOperators;
            alertStatus?: /* DTO of the necessary data to display an Alert Status. */ AlertStatus;
            /**
             * Order throughput area data
             */
            throughput: /* DTO that describes the order throughput data by area. */ ThroughputAreaData;
        }
        export interface UnitsRemainingData {
            unitsRemaining: number; // double
        }
        export interface UpdatePositionPayload {
            id: string;
            position: {
                y: number; // double
                x: number; // double
            };
        }
        export interface UpdateSettingData {
            id?: string;
            name: string;
            group?: string | null;
            dataType: AppConfigSettingDataType;
            value: any;
            levelToUpdate: AppConfigSettingSource;
            description?: string | null;
        }
        export interface UpdatedArea {
            identity: number; // double
            labels: string[];
            properties: {
                [name: string]: string | string[] | number /* double */;
            };
            elementId: string;
        }
        export type WMSCustomerOrderArea = "shipping" | "picking";
        export interface Workstation {
            orderTotesPerHour: number; // double
            donorTotesPerHour: number; // double
            weightedLinesPerHour: number; // double
            linesPerHour: number; // double
            weightedQuantityPerHour: number; // double
            quantityPerHour: number; // double
            blockedTime: number; // double
            idleTime: number; // double
            starvedTime: number; // double
            activeTime: number; // double
            operatorId: string;
            workflowStatus: WorkstationWorkflowStatus;
            workMode: WorkstationMode;
            status: WorkstationStatus;
            workstation: string;
        }
        export type WorkstationContainersListData = ApiResponseArrayWorkstationContainersListItemArrayPageNumberLimitNumberTotalResultsNumber;
        export interface WorkstationContainersListItem {
            container: string;
            status: string;
            transport: string;
            quantity: number; // double
            lastLocation: string;
            eventTime: string;
        }
        export interface WorkstationDailyPerformanceData {
            date: string;
            totalLoggedInHours?: number; // double
            idlePercentage?: number; // double
            starvedPercentage?: number; // double
            starvedHours?: number; // double
            donorContainers?: number; // double
            gtpContainers?: number; // double
            linesPicked?: number; // double
            qtyPerLine?: number; // double
            pickLineQty?: number; // double
            linesPerHour?: number; // double
            avgLinesPickedPerHr1stShiftPercentage?: number; // double
            avgLinesPickedPerHr2ndShiftPercentage?: number; // double
            retrievalFromDMS?: number; // double
            storageToDMS?: number; // double
            retrievalFromASRS?: number; // double
            storageToASRS?: number; // double
        }
        export type WorkstationDailyPerformanceList = WorkstationDailyPerformanceData[];
        export interface WorkstationDailyPerformanceListRequestBody {
            workstations?: string[];
        }
        export interface WorkstationData {
            data: /* Base chart series data */ ChartSeriesData[];
            id: string;
        }
        export interface WorkstationHealthStats {
            totalDowntime: number; // double
        }
        export interface WorkstationList {
            workstationList: Workstation[];
        }
        export interface WorkstationMetricConfiguration {
            id: string;
            label: string;
            type: string;
            format: string;
            display: boolean;
        }
        export type WorkstationMode = "Consolidation" | "Counting" | "Picking" | "Unknown";
        export interface WorkstationModeStats {
            count: number; // double
            type: string;
        }
        export interface WorkstationOperatorStats {
            targetLinesPerHour: number; // double
            actualLinesPerHour: number; // double
            totalOperators: number; // double
        }
        export interface WorkstationOrdersDetailsPicksListData {
            metadata: {
                totalResults: number; // double
                limit: number; // double
                page: number; // double
                orderId: string;
            };
            data: {
                tableData: {
                    containers: number; // double
                    qtyPicked: number; // double
                    qtyOrdered: number; // double
                    size: string;
                    style: string;
                    orderLine: string;
                    status: string;
                    container: string;
                }[];
                deliveryNumber: string;
            };
        }
        export type WorkstationOrdersListData = ApiResponseArrayWorkstationOrdersListItemArrayPageNumberLimitNumberTotalResultsNumber;
        export interface WorkstationOrdersListItem {
            arrivalTime: string;
            totalPicks: number; // double
            completedPicks: number; // double
            container: string;
            orderId: string;
            pickTask: string;
            orderStatus: string;
            /**
             * Minutes
             */
            dwellTime: number; // double
            position: string;
            station: string;
        }
        export interface WorkstationOrdersStatusResponse {
            activeOrders: number; // double
            delayedOrders: number; // double
        }
        export interface WorkstationPerformanceStats {
            totalStarvationTime: number; // double
            totalActiveTime: number; // double
        }
        export type WorkstationSeriesCharts = "LinesPerHour" | "QuantityPerHour" | "SourceContainersPerHour" | "DestinationContainersPerHour" | "WeightedLinesPerHour" | "WeightedQuantityPerHour" | "WeightedSourceContainersPerHour" | "WeightedDestinationContainersPerHour";
        export interface WorkstationSeriesData {
            data: WorkstationData[];
        }
        export interface WorkstationStats {
            workstationMode: WorkstationModeStats[];
            status: WorkstationStatusStats[];
            totalWorkstations: number; // double
        }
        export type WorkstationStatus = "Available" | "Closed" | "Paused" | "Unknown";
        export interface WorkstationStatusStats {
            count: number; // double
            type: string;
        }
        export type WorkstationStringArray = string[];
        export type WorkstationWorkflowStatus = "Disabled" | "Enabled" | "Unknown";
    }
}
declare namespace Paths {
    namespace ClearInventoryProcessFlowCache {
        export type RequestBody = Components.Schemas.ClearCacheRequest;
        namespace Responses {
            export type $200 = Components.Schemas.ClearCacheResponse;
        }
    }
    namespace DeleteConfigSetting {
        namespace Parameters {
            export type LevelToDelete = Components.Schemas.AppConfigSettingSource;
            export type SettingId = string;
        }
        export interface PathParameters {
            settingId: Parameters.SettingId;
        }
        export interface QueryParameters {
            levelToDelete?: Parameters.LevelToDelete;
        }
        namespace Responses {
            export type $200 = Components.Schemas.DeletedRecordsResult;
        }
    }
    namespace DeleteDataExplorerResult {
        namespace Parameters {
            export type ResultId = string;
        }
        export interface PathParameters {
            resultId: Parameters.ResultId;
        }
        namespace Responses {
            export interface $200 {
            }
            export interface $404 {
            }
        }
    }
    namespace GetAdminBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetAdminFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetAiBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetAiEnterpriseSearch {
        namespace Parameters {
            export type SearchText = string;
        }
        export interface QueryParameters {
            searchText: Parameters.SearchText;
        }
        namespace Responses {
            export type $200 = Components.Schemas.EnterpriseSearchResponse;
        }
    }
    namespace GetAiFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetAssignableRoles {
        namespace Responses {
            export type $200 = Components.Schemas.GetRolesResponse;
        }
    }
    namespace GetConfigAppConfig {
        namespace Parameters {
            export type Config = string;
        }
        export interface QueryParameters {
            config?: Parameters.Config;
        }
        namespace Responses {
            export type $200 = Components.Schemas.AppConfig;
        }
    }
    namespace GetConfigBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetConfigCuratedData {
        namespace Parameters {
            export type Table = string;
        }
        export interface QueryParameters {
            table: Parameters.Table;
        }
        namespace Responses {
            export type $200 = any;
        }
    }
    namespace GetConfigCuratedDataV2 {
        namespace Parameters {
            export type Table = string;
        }
        export interface QueryParameters {
            table: Parameters.Table;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CuratedTableRow[];
        }
    }
    namespace GetConfigCuratedTablesList {
        namespace Responses {
            export type $200 = string[];
        }
    }
    namespace GetConfigFacilityConfig {
        namespace Responses {
            export type $200 = /* DTO for a the facility config settings with its value set to the most relevant value for the user. */ Components.Schemas.FacilityConfigSettings;
        }
    }
    namespace GetConfigFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetConfigSettingLogs {
        namespace Parameters {
            export type Limit = number; // double
            export type SettingId = string;
        }
        export interface QueryParameters {
            setting_id: Parameters.SettingId;
            limit?: Parameters.Limit /* double */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.AppConfigSettingLogsData;
        }
    }
    namespace GetConfigSettings {
        namespace Parameters {
            export type AllStoredValues = boolean;
            export type Group = string;
            export type SettingId = string;
            export type SettingName = string;
            export type SettingType = Components.Schemas.AppConfigSettingSource;
        }
        export interface QueryParameters {
            settingId?: Parameters.SettingId;
            settingName?: Parameters.SettingName;
            settingType?: Parameters.SettingType;
            allStoredValues?: Parameters.AllStoredValues;
            group?: Parameters.Group;
        }
        namespace Responses {
            export type $200 = Components.Schemas.AppConfigSettingArrayOrAppConfigSetting;
        }
    }
    namespace GetDataAnalysisTimestampData {
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the inventory forecast objects. */ Components.Schemas.InventoryForecastDataAnalysisTimestamp;
        }
    }
    namespace GetDataExplorerAgentSearch {
        namespace Parameters {
            export type SearchText = string;
            export type SessionId = string;
        }
        export interface QueryParameters {
            searchText: Parameters.SearchText;
            sessionId?: Parameters.SessionId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.DataExplorerAgentSearchResponse;
        }
    }
    namespace GetDataExplorerBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetDataExplorerFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetDataExplorerRecommendations {
        namespace Responses {
            export type $200 = Components.Schemas.DataExplorerRecommendations;
        }
    }
    namespace GetDataExplorerResult {
        namespace Parameters {
            export type ResultId = string; // ^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$
        }
        export interface PathParameters {
            resultId: Parameters.ResultId /* ^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$ */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.DataExplorerResult;
        }
    }
    namespace GetDataExplorerResults {
        namespace Parameters {
            export type Bookmark = boolean;
            export type EndDate = string; // date-time
            export type Limit = number; // int32
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            limit?: Parameters.Limit /* int32 */;
            bookmark?: Parameters.Bookmark;
        }
        namespace Responses {
            export type $200 = Components.Schemas.DataExplorerResults;
        }
    }
    namespace GetDataExplorerSearch {
        namespace Parameters {
            export type SearchText = string;
        }
        export interface QueryParameters {
            searchText: Parameters.SearchText;
        }
        namespace Responses {
            export type $200 = Components.Schemas.DataExplorerResult;
        }
    }
    namespace GetDiagnosticsBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetDiagnosticsFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetDiagnosticsInfrastructureList {
        namespace Responses {
            export type $200 = Components.Schemas.DiagnosticsInfrastructureContract;
        }
    }
    namespace GetEquipmentBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetEquipmentFaults {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that describes the equipment faults data. */ Components.Schemas.ApiResponseEquipmentFaultsDefinitionEquipmentFaultsConfigDefinition;
        }
    }
    namespace GetEquipmentFaultsActiveList {
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the inventory recent activities. */ Components.Schemas.EquipmentActiveFaults;
        }
    }
    namespace GetEquipmentFaultsArea {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* Type that describes the return structure of the data contract response. */ Components.Schemas.ApiResponseFaultsAreasResponseFaultsAreasConfig;
        }
    }
    namespace GetEquipmentFaultsEvents {
        namespace Responses {
            export type $200 = Components.Schemas.FaultEventContract;
        }
    }
    namespace GetEquipmentFaultsSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* Describes the contract for the data returned by the api. */ Components.Schemas.EquipmentFaultsSeriesContractData;
        }
    }
    namespace GetEquipmentFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetEquipmentOutboundRate {
        namespace Responses {
            export type $200 = /* Data contract for the response from the api */ Components.Schemas.EquipmentOutboundRateApiResponse;
        }
    }
    namespace GetEquipmentSummaryAisle {
        namespace Parameters {
            export type AisleId = string;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface PathParameters {
            aisleId: Parameters.AisleId;
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseEquipmentSummaryAisleDefinitionEquipmentSummaryAisleConfigDefinition;
        }
    }
    namespace GetEquipmentSummaryAreas {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that describes the areas summary data. */ Components.Schemas.EquipmentSummaryAreaContract;
        }
    }
    namespace GetEquipmentSummaryWorkstations {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
            export type WorkstationId = string;
        }
        export interface PathParameters {
            workstationId: Parameters.WorkstationId;
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseEquipmentSummaryWorkstationDefinitionEquipmentSummaryWorkstationConfigDefinition;
        }
    }
    namespace GetEquipmentWorkstationAisleActiveFaults {
        namespace Parameters {
            export type WorkstationId = string;
        }
        export interface PathParameters {
            workstationId: Parameters.WorkstationId;
        }
        namespace Responses {
            export type $200 = /* Definiton of the data for the data contract for workstation active faults */ Components.Schemas.EquipmentWorkstationActiveFaultsData[];
        }
    }
    namespace GetEquipmentWorkstationLineRatesSeries {
        namespace Parameters {
            export type WorkstationId = string;
        }
        export interface PathParameters {
            workstationId: Parameters.WorkstationId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentWorkstationLineRatesSeriesData;
        }
    }
    namespace GetEquipmentWorkstationMovementsDetail {
        namespace Parameters {
            export type WorkstationId = string;
        }
        export interface PathParameters {
            workstationId: Parameters.WorkstationId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentWorkstationAisleMovementData;
        }
    }
    namespace GetEquipmentWorkstationOperatorActivity {
        namespace Parameters {
            export type WorkstationId = string;
        }
        export interface PathParameters {
            workstationId: Parameters.WorkstationId;
        }
        namespace Responses {
            export type $200 = /* Expected return data object format for the response */ Components.Schemas.EquipmentWorkstationOperatorActivityData[];
        }
    }
    namespace GetEquipmentWorkstationStarvedBlockedTimeSeries {
        namespace Parameters {
            export type WorkstationId = string;
        }
        export interface PathParameters {
            workstationId: Parameters.WorkstationId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseEquipmentWorkstationStarvedBlockedTimeSeriesDataEquipmentWorkstationStarvedBlockedTimeSeriesConfig;
        }
    }
    namespace GetFaultAvgDurationByStatus {
        export type RequestBody = Components.Schemas.FaultAvgDurationByStatusRequest;
        namespace Responses {
            export type $200 = Components.Schemas.FaultAvgDurationSeriesData;
        }
    }
    namespace GetFaultGroupedByCounts {
        export type RequestBody = Components.Schemas.FaultCountGroupedByRequest;
        namespace Responses {
            export type $200 = Components.Schemas.FaultCountGroupedSeriesData;
        }
    }
    namespace GetInventoryAccuracy {
        namespace Parameters {
            export type AreaFilter = string;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area_filter?: Parameters.AreaFilter;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the inventory accuracy object. */ Components.Schemas.ApiResponseInventoryAccuracyInventoryAccuracyConfig;
        }
    }
    namespace GetInventoryAdvicesCycleTime {
        namespace Responses {
            export type $200 = Components.Schemas.AdvicesCycleTimeData;
        }
    }
    namespace GetInventoryAdvicesDetails {
        namespace Parameters {
            export type AdviceId = string;
        }
        export interface PathParameters {
            adviceId: Parameters.AdviceId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.AdviceDetailsData;
        }
    }
    namespace GetInventoryAdvicesFinished {
        namespace Responses {
            export type $200 = Components.Schemas.AdvicesFinishedData;
        }
    }
    namespace GetInventoryAdvicesInProgress {
        namespace Responses {
            export type $200 = Components.Schemas.InventoryAdvicesInProgressData;
        }
    }
    namespace GetInventoryAdvicesList {
        namespace Responses {
            export type $200 = Components.Schemas.AdvicesList;
        }
    }
    namespace GetInventoryAdvicesOutstanding {
        namespace Responses {
            export type $200 = Components.Schemas.AdvicesOutstandingData;
        }
    }
    namespace GetInventoryBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetInventoryBinLocations {
        namespace Parameters {
            export type Aisle = string;
            export type Level = string;
        }
        export interface QueryParameters {
            aisle: Parameters.Aisle;
            level: Parameters.Level;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryBinLocationsResponse;
        }
    }
    namespace GetInventoryContainerEventsDetails {
        namespace Parameters {
            export type ContainerId = string;
        }
        export interface PathParameters {
            containerId: Parameters.ContainerId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryContainerEventsKpiContract;
        }
    }
    namespace GetInventoryFilter {
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details the inventory area dropdown. */ Components.Schemas.InventoryAreaFilterDefinition;
        }
    }
    namespace GetInventoryForecastSkuLocations {
        namespace Parameters {
            export type SkuId = string;
        }
        export interface PathParameters {
            skuId: Parameters.SkuId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryForecastSkuLocationAreas;
        }
    }
    namespace GetInventoryForecastSkuOrders {
        namespace Parameters {
            export type SkuId = string;
        }
        export interface PathParameters {
            skuId: Parameters.SkuId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryForecastSkuOrders;
        }
    }
    namespace GetInventoryFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetInventoryHandlingUnitsTrayed {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* Contract for Handling Units Trayed. */ Components.Schemas.InventoryHandlingUnitsTrayedContract;
        }
    }
    namespace GetInventoryPerformanceSeries {
        namespace Parameters {
            export type DepartmentFilter = string;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            department_filter: Parameters.DepartmentFilter;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryPerformanceSeriesContract;
        }
    }
    namespace GetInventoryProcessFlowAreaById {
        namespace Parameters {
            export type AreaId = string;
        }
        export interface PathParameters {
            areaId: Parameters.AreaId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ProcessFlowResponse;
        }
    }
    namespace GetInventoryProcessFlowAreas {
        namespace Responses {
            export type $200 = Components.Schemas.ProcessFlowResponse;
        }
    }
    namespace GetInventoryProcessFlowGraphDetails {
        namespace Parameters {
            export type ElementId = string;
            export type Type = string;
        }
        export interface PathParameters {
            type: Parameters.Type;
            elementId: Parameters.ElementId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ProcessFlowDetailsResponse;
        }
    }
    namespace GetInventoryReplenishmentDetails {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryReplenishmentDetails;
        }
    }
    namespace GetInventoryReplenishmentTaskTypeSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.TaskTypeData;
        }
    }
    namespace GetInventorySkuForecast {
        namespace Parameters {
            export type SkuId = string;
        }
        export interface PathParameters {
            skuId: Parameters.SkuId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventorySkuForecastDetails;
        }
    }
    namespace GetInventoryStockDistributionAtPercentageSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryStockDistributionAtData;
        }
    }
    namespace GetInventoryStockDistributionNoPercentageSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryStockDistributionNoData;
        }
    }
    namespace GetInventoryStockDistributionOverPercentageSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryStockDistributionOverData;
        }
    }
    namespace GetInventoryStockDistributionUnderPercentageSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.InventoryStockDistributionUnderData;
        }
    }
    namespace GetInventoryStorageUtilization {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date?: Parameters.StartDate /* date-time */;
            end_date?: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details the inventory storage utilization tile. */ Components.Schemas.InventoryStorageUtilization;
        }
    }
    namespace GetInventoryUploadRecentActivity {
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the inventory upload history object. */ Components.Schemas.InventoryUploadRecentActivity[];
        }
    }
    namespace GetMetricConfigs {
        namespace Parameters {
            export type Active = boolean;
            export type ConfigType = string;
            export type Enabled = boolean;
            export type FactType = string;
            export type MetricId = string;
            export type MetricName = string;
            export type NodeName = string;
        }
        export interface QueryParameters {
            metricName?: Parameters.MetricName;
            metricId?: Parameters.MetricId;
            factType?: Parameters.FactType;
            nodeName?: Parameters.NodeName;
            active?: Parameters.Active;
            enabled?: Parameters.Enabled;
            configType?: Parameters.ConfigType;
        }
        namespace Responses {
            export type $200 = /* Represents a metric configuration */ Components.Schemas.MetricConfig[];
            export interface $404 {
            }
            export interface $500 {
            }
        }
    }
    namespace GetOperatorsActive {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO of the necessary data to display total number of operators at a given time. */ Components.Schemas.ApiResponseOperatorCountDataOperatorCountsConfig;
        }
    }
    namespace GetOperatorsActiveAreas {
        namespace Responses {
            export type $200 = Components.Schemas.OperatorFacilityArea[];
        }
    }
    namespace GetOperatorsActiveSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseOperatorsChartSeriesDataBaseChartConfig;
        }
    }
    namespace GetOperatorsBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetOperatorsFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetOrderLinesCustomerProgressSeries {
        namespace Parameters {
            export type Area = Components.Schemas.WMSCustomerOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderCustomerLineProgressSeriesData;
        }
    }
    namespace GetOrdersBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetOrdersCompletion {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date?: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* Definition of the necessary data to display the Estimated Completion */ Components.Schemas.ApiResponseEstimatedOrderCompletionTimesEstimatedOrderCompletionConfig;
        }
    }
    namespace GetOrdersCustomerCycleTime {
        namespace Parameters {
            export type Area = Components.Schemas.WMSCustomerOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderCycleTime;
        }
    }
    namespace GetOrdersCustomerEstimatedCompletion {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CustomerOrderEstimatedCompletionData;
        }
    }
    namespace GetOrdersCustomerLineProgress {
        namespace Parameters {
            export type Area = Components.Schemas.WMSCustomerOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderLineProgress;
        }
    }
    namespace GetOrdersCustomerLineThroughput {
        namespace Parameters {
            export type Area = Components.Schemas.WMSCustomerOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = /* DTO that describes the order line throughput data. */ Components.Schemas.OrderCustomerLineThroughputData;
        }
    }
    namespace GetOrdersCustomerLineThroughputSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CustomerOrderLineThroughputSeriesData;
        }
    }
    namespace GetOrdersCustomerProgress {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CustomerOrderProgress;
        }
    }
    namespace GetOrdersCustomerProgressSeries {
        namespace Parameters {
            export type Area = Components.Schemas.WMSCustomerOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderProgressSeriesData;
        }
    }
    namespace GetOrdersCustomerShipped {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details for the current number of orders shipped. */ Components.Schemas.ApiResponseOrdersShipped;
        }
    }
    namespace GetOrdersCustomerThroughput {
        namespace Parameters {
            export type Area = Components.Schemas.WMSCustomerOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = /* DTO that describes the order throughput data. */ Components.Schemas.CustomerOrderThroughputData;
        }
    }
    namespace GetOrdersCustomerThroughputSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export interface $200 {
                throughput: /* Base chart series data */ Components.Schemas.ChartSeriesData[];
            }
        }
    }
    namespace GetOrdersCycleTime {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseAreasFacilityAreaArrayCycleTimeAreasConfig;
        }
    }
    namespace GetOrdersCycleTimeSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseOrderCycleTimeChartDataBaseChartConfig;
        }
    }
    namespace GetOrdersFacilityProgress {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the projected order fulfillment percentage. */ Components.Schemas.ApiResponseOrderProgressOrderProgressConfig;
        }
    }
    namespace GetOrdersFacilityShipped {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details for the current number of orders shipped. */ Components.Schemas.ApiResponseOrdersShipped;
        }
    }
    namespace GetOrdersFulfillment {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the projected order fulfillment percentage. */ Components.Schemas.ProjectedOrderFulfillment;
        }
    }
    namespace GetOrdersFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetOrdersLineProgress {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseOrderLineProgressOrderLineProgressConfig;
        }
    }
    namespace GetOrdersLineProgressAreas {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderLineProgressAreasData;
        }
    }
    namespace GetOrdersLineProgressSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseOrderLineProgressSeriesDataBaseChartConfig;
        }
    }
    namespace GetOrdersLineThroughput {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that describes the order line throughput data. */ Components.Schemas.OrderLineThroughputData;
        }
    }
    namespace GetOrdersOutstanding {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the orders outstanding. */ Components.Schemas.ApiResponseOrdersOutstandingData;
        }
    }
    namespace GetOrdersPerformanceFulfillment {
        namespace Parameters {
            export type DepartmentFilter = string;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            department_filter: Parameters.DepartmentFilter;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderPerformanceFulfillmentData;
        }
    }
    namespace GetOrdersPickCycleCount {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrdersPickCycleCountData;
        }
    }
    namespace GetOrdersPickCycleTime {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseOrderCycleTimeDataOrderCycleTimeConfig;
        }
    }
    namespace GetOrdersPickLineThroughputSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderPickLineThroughputSeriesData;
        }
    }
    namespace GetOrdersProgress {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* DTO that defines the relevant details of the projected order fulfillment percentage. */ Components.Schemas.ApiResponseOrderProgressOrderProgressConfig;
        }
    }
    namespace GetOrdersProgressSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.OrderProgressSeriesData;
        }
    }
    namespace GetOrdersRemaining {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.UnitsRemainingData;
        }
    }
    namespace GetOrdersThroughput {
        namespace Parameters {
            export type Area = Components.Schemas.PickOrderArea;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export type $200 = /* DTO that describes the order throughput data. */ Components.Schemas.ApiResponseOrderThroughputDataOrderThroughputConfig;
        }
    }
    namespace GetOrdersThroughputAreas {
        namespace Parameters {
            export type Area = string;
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            area?: Parameters.Area;
        }
        namespace Responses {
            export interface $200 {
                areas: Components.Schemas.ThroughputFacilityArea[];
            }
        }
    }
    namespace GetOrdersThroughputSeries {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = /* Describes the contract for the data returned by the api. */ Components.Schemas.OrderThroughputChartApiResponse;
        }
    }
    namespace GetPickOrdersShipped {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseOrderShippedData;
        }
    }
    namespace GetSimulationBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetSimulationFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetSimulationJobs {
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArraySimulationJobArray;
        }
    }
    namespace GetSimulationOutputByJobId {
        namespace Parameters {
            export type FileName = string;
            export type FileType = string;
            export type JobId = string;
            export type TaskIndex = number; // double
        }
        export interface PathParameters {
            jobId: Parameters.JobId;
            taskIndex: Parameters.TaskIndex /* double */;
            fileName: Parameters.FileName;
            fileType: Parameters.FileType;
        }
        namespace Responses {
            export type $200 = string /* byte */ | any[];
        }
    }
    namespace GetTrustedTicket {
        namespace Responses {
            export type $200 = Components.Schemas.AuthTicket;
        }
    }
    namespace GetWorkstationBasicHealthCheck {
        namespace Responses {
            export interface $200 {
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetWorkstationFullHealthCheck {
        namespace Responses {
            export interface $200 {
                subSystems: {
                    redis: Components.Schemas.HealthCheckStatus;
                    bigQuery: Components.Schemas.HealthCheckStatus;
                    auth0: Components.Schemas.HealthCheckStatus;
                };
                sha: string;
                status: Components.Schemas.HealthCheckStatus;
            }
        }
    }
    namespace GetWorkstationHealthSummary {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationHealthStats;
        }
    }
    namespace GetWorkstationList {
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationList;
        }
    }
    namespace GetWorkstationOperatorSummary {
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationOperatorStats;
        }
    }
    namespace GetWorkstationOrdersStatus {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationOrdersStatusResponse;
        }
    }
    namespace GetWorkstationPerformanceSummary {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationPerformanceStats;
        }
    }
    namespace GetWorkstationSeriesData {
        namespace Parameters {
            export type Chart = Components.Schemas.WorkstationSeriesCharts;
        }
        export interface QueryParameters {
            chart: Parameters.Chart;
        }
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationSeriesData;
        }
    }
    namespace GetWorkstationSummary {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationStats;
        }
    }
    namespace GetWorkstations {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date?: Parameters.StartDate /* date-time */;
            end_date?: Parameters.EndDate /* date-time */;
        }
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationStringArray;
        }
    }
    namespace PostConfigDefaultConfig {
        namespace Responses {
            export type $200 = Components.Schemas.DefaultConfigSeedingResult;
        }
    }
    namespace PostEquipmentEventsList {
        export interface RequestBody {
            end_date: string; // date-time
            start_date: string; // date-time
            limit?: number; // double
            page?: number; // double
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            filters?: Components.Schemas.BaseFilterType;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayRecentEventQueryResponseArrayRecentEventQueryConfig;
        }
    }
    namespace PostEquipmentFaultsAisleList {
        export type RequestBody = Components.Schemas.FaultsAisleListRequest;
        namespace Responses {
            export type $200 = Components.Schemas.AvailableAisles;
        }
    }
    namespace PostEquipmentFaultsDeviceIdsList {
        export type RequestBody = Components.Schemas.EquipmentFaultsDeviceIdRequest;
        namespace Responses {
            export type $200 = string[];
        }
    }
    namespace PostEquipmentFaultsDeviceTypeList {
        export type RequestBody = Components.Schemas.FaultsDeviceTypeListRequest;
        namespace Responses {
            export interface $200 {
                availableDeviceTypes: string[];
            }
        }
    }
    namespace PostEquipmentFaultsLevelList {
        export type RequestBody = Components.Schemas.FaultsLevelListRequest;
        namespace Responses {
            export type $200 = Components.Schemas.AvailableLevels;
        }
    }
    namespace PostEquipmentFaultsList {
        export type RequestBody = Components.Schemas.PaginatedRequest;
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayEquipmentFaultsListItemArrayEquipmentFaultsPaginationInfo;
        }
    }
    namespace PostEquipmentFaultsMovementsSeries {
        export type RequestBody = Components.Schemas.FaultsMovementsSeriesRequest;
        namespace Responses {
            export type $200 = Components.Schemas.FaultsMovementsSeriesData;
        }
    }
    namespace PostEquipmentFaultsStatusList {
        export type RequestBody = Components.Schemas.FaultsStatusListRequest;
        namespace Responses {
            export interface $200 {
                availableStatuses: string[];
            }
        }
    }
    namespace PostInventoryContainerEventsList {
        namespace Parameters {
            export type ContainerId = string;
        }
        export interface PathParameters {
            containerId: Parameters.ContainerId;
        }
        /**
         * Request body with filters, sorting, pagination, and months parameter.
         */
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            months?: number; // double
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayFlattenedInventoryContainerEventsDataItemArrayInventoryContainerEventsPaginationInfo;
        }
    }
    namespace PostInventoryContainerEventsListExport {
        namespace Parameters {
            export type ContainerId = string;
        }
        export interface PathParameters {
            containerId: Parameters.ContainerId;
        }
        /**
         * Request body with filters, sorting, pagination, columns, and months parameter.
         */
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            columns: /* DTO for objects related to the excel export. */ Components.Schemas.IncludedColumns;
            months?: number; // double
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PostInventoryContainersList {
        /**
         * - Request body with filters, sorting, and pagination
         */
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            byPassConfigSetting?: boolean;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayFlattenedInventoryContainerDataArrayInventoryContainerPaginationInfo;
        }
    }
    namespace PostInventoryContainersListExport {
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            columns: /* DTO for objects related to the excel export. */ Components.Schemas.IncludedColumns;
            byPassConfigSetting?: boolean;
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PostInventoryForecastList {
        export type RequestBody = Components.Schemas.PaginatedRequestNoDates;
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayInventoryForecastListingArrayInventoryForecastListingConfig;
        }
    }
    namespace PostInventoryForecastListExport {
        /**
         * Request body with filters, sorting, and columns parameter.
         */
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            columns: /* DTO for objects related to the excel export. */ Components.Schemas.IncludedColumns;
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PostInventorySkuHighImpactList {
        export type RequestBody = Components.Schemas.PaginatedRequest;
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayInventoryHighImpactSKUArrayInventoryHighImpactSkuPaginationInfo;
        }
    }
    namespace PostInventorySkusList {
        export type RequestBody = Components.Schemas.PaginatedRequestNoDates;
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayInventorySkuArrayInventorySkuPaginationInfo;
        }
    }
    namespace PostInventorySkusListExport {
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            columns: /* DTO for objects related to the excel export. */ Components.Schemas.IncludedColumns;
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PostInventoryUploadKnownDemand {
        export interface RequestBody {
            manager: string; // binary
            details: string; // binary
        }
        namespace Responses {
            export interface $200 {
                orderLinesProcessed: number; // double
                ordersProcessed: number; // double
            }
        }
    }
    namespace PostWorkstationContainersList {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type Size = string;
            export type StartDate = string; // date-time
            export type Style = string;
            export type Zone = string;
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
            size: Parameters.Size;
            style: Parameters.Style;
            zone: Parameters.Zone;
        }
        export type RequestBody = Components.Schemas.PaginatedRequestNoDates;
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayWorkstationContainersListItemArrayPageNumberLimitNumberTotalResultsNumber;
        }
    }
    namespace PostWorkstationDailyPerformanceList {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        export type RequestBody = Components.Schemas.WorkstationDailyPerformanceListRequestBody;
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationDailyPerformanceList;
        }
    }
    namespace PostWorkstationListExport {
        export interface RequestBody {
            columns: {
                [name: string]: boolean;
            };
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PostWorkstationOrdersDetailsPicksList {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type OrderId = string;
            export type StartDate = string; // date-time
        }
        export interface PathParameters {
            orderId: Parameters.OrderId;
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        export type RequestBody = Components.Schemas.PaginatedRequestNoDates;
        namespace Responses {
            export type $200 = Components.Schemas.WorkstationOrdersDetailsPicksListData;
        }
    }
    namespace PostWorkstationOrdersDetailsPicksListExport {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type OrderId = string;
            export type StartDate = string; // date-time
        }
        export interface PathParameters {
            orderId: Parameters.OrderId;
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            columns: {
                [name: string]: boolean;
            };
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PostWorkstationOrdersList {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        export type RequestBody = Components.Schemas.PaginatedRequestNoDates;
        namespace Responses {
            export type $200 = Components.Schemas.ApiResponseArrayWorkstationOrdersListItemArrayPageNumberLimitNumberTotalResultsNumber;
        }
    }
    namespace PostWorkstationOrdersListExport {
        namespace Parameters {
            export type EndDate = string; // date-time
            export type StartDate = string; // date-time
        }
        export interface QueryParameters {
            start_date: Parameters.StartDate /* date-time */;
            end_date: Parameters.EndDate /* date-time */;
        }
        export interface RequestBody {
            filters?: any;
            sortFields?: /* Defines a column to add to a ORDER BY clause in a SQL statement. */ Components.Schemas.SortField[];
            page?: number; // int32
            limit?: number; // int32
            searchString?: string;
            columns: {
                [name: string]: boolean;
            };
        }
        namespace Responses {
            export type $200 = string; // byte
        }
    }
    namespace PutConfigSettings {
        /**
         * Identifying information about the requested Setting resource, as well as requested updates.<br><br>
         * <b>id</b> - the setting Id.  ID is required in order to update an existing setting, but not to add a new one<br>
         * <b>name</b> - the name of the setting.  Required.<br>
         * <b>dataType</b> - one of: 'json', 'string', 'boolean', or 'number'<br>
         * <b>group</b> - the Setting group.  This value is updated if it is different.<br>
         * <b>value</b> - the value to update the setting with. Can be any value<br>
         * <b>levelToUpdate</b> - one of: 'default', 'tenant', 'site', or 'user'. This determines the appropriate value to change.<br>
         * <b>description</b> - optional string description of the setting.<br>
         */
        export type RequestBody = Components.Schemas.UpdateSettingData;
        namespace Responses {
            export type $200 = /* DTO for a setting with its value set to the most relevant value for the user. */ Components.Schemas.AppConfigSetting;
            export interface $201 {
            }
        }
    }
    namespace PutDataExplorerResult {
        namespace Parameters {
            export type ResultId = string;
        }
        export interface PathParameters {
            resultId: Parameters.ResultId;
        }
        export type RequestBody = Components.Schemas.DataExplorerResult;
        namespace Responses {
            export type $200 = Components.Schemas.DataExplorerResult;
            export interface $404 {
            }
        }
    }
    namespace PutOperatorsAreas {
        export type RequestBody = Components.Schemas.RequestBody;
        namespace Responses {
            export interface $200 {
            }
        }
    }
    namespace PutUserWritable {
        /**
         * Identifying information about the requested Setting resource, as well as requested updates.
         * The setting must be in the 'user-writable' group and will always be updated at the user level.
         * If the user is a ct_configurator, they may specify a userId to update another user's setting.
         */
        export interface RequestBody {
            id?: string;
            name: string;
            group?: string | null;
            dataType: Components.Schemas.AppConfigSettingDataType;
            value: any;
            levelToUpdate: Components.Schemas.AppConfigSettingSource;
            description?: string | null;
            userId?: string;
        }
        namespace Responses {
            export type $200 = /* DTO for a setting with its value set to the most relevant value for the user. */ Components.Schemas.AppConfigSetting;
            export interface $201 {
            }
        }
    }
    namespace ResendEmailVerification {
        namespace Responses {
            export type $200 = Components.Schemas.ResendEmailVerificationResponse;
        }
    }
    namespace UpdateInventoryProcessFlowArea {
        namespace Parameters {
            export type AreaId = string;
            export type View = string;
        }
        export interface PathParameters {
            areaId: Parameters.AreaId;
        }
        export interface QueryParameters {
            view?: Parameters.View;
        }
        export type RequestBody = Components.Schemas.UpdatePositionPayload;
        namespace Responses {
            export type $200 = Components.Schemas.UpdatedArea;
        }
    }
    namespace UpdateInventoryProcessFlowEdge {
        namespace Parameters {
            export type EdgeId = string;
        }
        export interface PathParameters {
            edgeId: Parameters.EdgeId;
        }
        export type RequestBody = Components.Schemas.Edge;
        namespace Responses {
            export type $200 = Components.Schemas.Edge;
        }
    }
    namespace UpdateInventoryProcessFlowMetric {
        namespace Parameters {
            export type MetricId = string;
        }
        export interface PathParameters {
            metricId: Parameters.MetricId;
        }
        export type RequestBody = Components.Schemas.Metric;
        namespace Responses {
            export type $200 = Components.Schemas.Metric;
        }
    }
}

export interface OperationMethods {
  /**
   * GetAiBasicHealthCheck
   */
  'GetAiBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetAiBasicHealthCheck.Responses.$200>
  /**
   * GetAiFullHealthCheck
   */
  'GetAiFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetAiFullHealthCheck.Responses.$200>
  /**
   * GetAiEnterpriseSearch
   */
  'GetAiEnterpriseSearch'(
    parameters?: Parameters<Paths.GetAiEnterpriseSearch.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetAiEnterpriseSearch.Responses.$200>
  /**
   * PutUserWritable - Updates a user writable setting. This endpoint is specifically for user-level settings.
   * Any authenticated user can update their own settings, and configurators can update any user's settings.
   */
  'PutUserWritable'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PutUserWritable.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PutUserWritable.Responses.$200 | Paths.PutUserWritable.Responses.$201>
  /**
   * GetMetricConfigs - Get metric configurations based on filter criteria.
   * Without any query params, returns all metric configs that are not soft_deleted.
   */
  'GetMetricConfigs'(
    parameters?: Parameters<Paths.GetMetricConfigs.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetMetricConfigs.Responses.$200>
  /**
   * GetConfigBasicHealthCheck
   */
  'GetConfigBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigBasicHealthCheck.Responses.$200>
  /**
   * GetConfigFullHealthCheck
   */
  'GetConfigFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigFullHealthCheck.Responses.$200>
  /**
   * GetConfigSettings - Retrieves all settings and their values or a single setting based on id and/or name
   */
  'GetConfigSettings'(
    parameters?: Parameters<Paths.GetConfigSettings.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigSettings.Responses.$200>
  /**
   * PutConfigSettings - Updates the specified setting with the properties in settingInfo.  Creates a new setting if
   * the setting isn't found, and the required information is provided.
   */
  'PutConfigSettings'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PutConfigSettings.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PutConfigSettings.Responses.$200 | Paths.PutConfigSettings.Responses.$201>
  /**
   * PostConfigDefaultConfig - Inserts the default settings to seed a new tenant's database
   */
  'PostConfigDefaultConfig'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostConfigDefaultConfig.Responses.$200>
  /**
   * DeleteConfigSetting
   */
  'DeleteConfigSetting'(
    parameters?: Parameters<Paths.DeleteConfigSetting.QueryParameters & Paths.DeleteConfigSetting.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.DeleteConfigSetting.Responses.$200>
  /**
   * GetConfigSettingLogs - Retrieves logs for a setting
   */
  'GetConfigSettingLogs'(
    parameters?: Parameters<Paths.GetConfigSettingLogs.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigSettingLogs.Responses.$200>
  /**
   * GetConfigFacilityConfig
   */
  'GetConfigFacilityConfig'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigFacilityConfig.Responses.$200>
  /**
   * GetConfigCuratedData - Handle a request for the top 100 rows of a table.
   */
  'GetConfigCuratedData'(
    parameters?: Parameters<Paths.GetConfigCuratedData.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigCuratedData.Responses.$200>
  /**
   * GetConfigCuratedDataV2 - Handle a request for the top 100 rows of a table.
   */
  'GetConfigCuratedDataV2'(
    parameters?: Parameters<Paths.GetConfigCuratedDataV2.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigCuratedDataV2.Responses.$200>
  /**
   * GetConfigCuratedTablesList - Endpoint for getting a list of tables available in the curated dataset.
   */
  'GetConfigCuratedTablesList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigCuratedTablesList.Responses.$200>
  /**
   * GetConfigAppConfig
   */
  'GetConfigAppConfig'(
    parameters?: Parameters<Paths.GetConfigAppConfig.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetConfigAppConfig.Responses.$200>
  /**
   * GetDataExplorerSearch
   */
  'GetDataExplorerSearch'(
    parameters?: Parameters<Paths.GetDataExplorerSearch.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerSearch.Responses.$200>
  /**
   * GetDataExplorerResults
   */
  'GetDataExplorerResults'(
    parameters?: Parameters<Paths.GetDataExplorerResults.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerResults.Responses.$200>
  /**
   * GetDataExplorerResult
   */
  'GetDataExplorerResult'(
    parameters?: Parameters<Paths.GetDataExplorerResult.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerResult.Responses.$200>
  /**
   * PutDataExplorerResult
   */
  'PutDataExplorerResult'(
    parameters?: Parameters<Paths.PutDataExplorerResult.PathParameters> | null,
    data?: Paths.PutDataExplorerResult.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PutDataExplorerResult.Responses.$200>
  /**
   * DeleteDataExplorerResult
   */
  'DeleteDataExplorerResult'(
    parameters?: Parameters<Paths.DeleteDataExplorerResult.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.DeleteDataExplorerResult.Responses.$200>
  /**
   * GetDataExplorerRecommendations
   */
  'GetDataExplorerRecommendations'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerRecommendations.Responses.$200>
  /**
   * GetDataExplorerBasicHealthCheck
   */
  'GetDataExplorerBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerBasicHealthCheck.Responses.$200>
  /**
   * GetDataExplorerFullHealthCheck
   */
  'GetDataExplorerFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerFullHealthCheck.Responses.$200>
  /**
   * GetDataExplorerAgentSearch - Retrieves an example data explorer result.
   */
  'GetDataExplorerAgentSearch'(
    parameters?: Parameters<Paths.GetDataExplorerAgentSearch.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataExplorerAgentSearch.Responses.$200>
  /**
   * GetDiagnosticsInfrastructureList
   */
  'GetDiagnosticsInfrastructureList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDiagnosticsInfrastructureList.Responses.$200>
  /**
   * GetDiagnosticsBasicHealthCheck
   */
  'GetDiagnosticsBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDiagnosticsBasicHealthCheck.Responses.$200>
  /**
   * GetDiagnosticsFullHealthCheck
   */
  'GetDiagnosticsFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDiagnosticsFullHealthCheck.Responses.$200>
  /**
   * PostInventoryUploadKnownDemand - Receives two xls files and parses them into multiline JSON to forward to the EDP PubSub Topic
   */
  'PostInventoryUploadKnownDemand'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventoryUploadKnownDemand.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryUploadKnownDemand.Responses.$200>
  /**
   * GetInventoryUploadRecentActivity
   */
  'GetInventoryUploadRecentActivity'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryUploadRecentActivity.Responses.$200>
  /**
   * GetInventoryStorageUtilization
   */
  'GetInventoryStorageUtilization'(
    parameters?: Parameters<Paths.GetInventoryStorageUtilization.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryStorageUtilization.Responses.$200>
  /**
   * GetInventoryStockDistributionUnderPercentageSeries
   */
  'GetInventoryStockDistributionUnderPercentageSeries'(
    parameters?: Parameters<Paths.GetInventoryStockDistributionUnderPercentageSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryStockDistributionUnderPercentageSeries.Responses.$200>
  /**
   * GetInventoryStockDistributionOverPercentageSeries
   */
  'GetInventoryStockDistributionOverPercentageSeries'(
    parameters?: Parameters<Paths.GetInventoryStockDistributionOverPercentageSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryStockDistributionOverPercentageSeries.Responses.$200>
  /**
   * GetInventoryStockDistributionNoPercentageSeries
   */
  'GetInventoryStockDistributionNoPercentageSeries'(
    parameters?: Parameters<Paths.GetInventoryStockDistributionNoPercentageSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryStockDistributionNoPercentageSeries.Responses.$200>
  /**
   * GetInventoryStockDistributionAtPercentageSeries
   */
  'GetInventoryStockDistributionAtPercentageSeries'(
    parameters?: Parameters<Paths.GetInventoryStockDistributionAtPercentageSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryStockDistributionAtPercentageSeries.Responses.$200>
  /**
   * PostInventorySkusList
   */
  'PostInventorySkusList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventorySkusList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventorySkusList.Responses.$200>
  /**
   * PostInventorySkusListExport
   */
  'PostInventorySkusListExport'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventorySkusListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventorySkusListExport.Responses.$200>
  /**
   * GetInventoryReplenishmentTaskTypeSeries
   */
  'GetInventoryReplenishmentTaskTypeSeries'(
    parameters?: Parameters<Paths.GetInventoryReplenishmentTaskTypeSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryReplenishmentTaskTypeSeries.Responses.$200>
  /**
   * GetInventoryReplenishmentDetails
   */
  'GetInventoryReplenishmentDetails'(
    parameters?: Parameters<Paths.GetInventoryReplenishmentDetails.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryReplenishmentDetails.Responses.$200>
  /**
   * GetInventoryProcessFlowAreas
   */
  'GetInventoryProcessFlowAreas'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryProcessFlowAreas.Responses.$200>
  /**
   * GetInventoryProcessFlowAreaById
   */
  'GetInventoryProcessFlowAreaById'(
    parameters?: Parameters<Paths.GetInventoryProcessFlowAreaById.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryProcessFlowAreaById.Responses.$200>
  /**
   * GetInventoryProcessFlowGraphDetails
   */
  'GetInventoryProcessFlowGraphDetails'(
    parameters?: Parameters<Paths.GetInventoryProcessFlowGraphDetails.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryProcessFlowGraphDetails.Responses.$200>
  /**
   * UpdateInventoryProcessFlowArea
   */
  'UpdateInventoryProcessFlowArea'(
    parameters?: Parameters<Paths.UpdateInventoryProcessFlowArea.QueryParameters & Paths.UpdateInventoryProcessFlowArea.PathParameters> | null,
    data?: Paths.UpdateInventoryProcessFlowArea.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.UpdateInventoryProcessFlowArea.Responses.$200>
  /**
   * UpdateInventoryProcessFlowEdge
   */
  'UpdateInventoryProcessFlowEdge'(
    parameters?: Parameters<Paths.UpdateInventoryProcessFlowEdge.PathParameters> | null,
    data?: Paths.UpdateInventoryProcessFlowEdge.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.UpdateInventoryProcessFlowEdge.Responses.$200>
  /**
   * UpdateInventoryProcessFlowMetric
   */
  'UpdateInventoryProcessFlowMetric'(
    parameters?: Parameters<Paths.UpdateInventoryProcessFlowMetric.PathParameters> | null,
    data?: Paths.UpdateInventoryProcessFlowMetric.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.UpdateInventoryProcessFlowMetric.Responses.$200>
  /**
   * ClearInventoryProcessFlowCache
   */
  'ClearInventoryProcessFlowCache'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.ClearInventoryProcessFlowCache.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.ClearInventoryProcessFlowCache.Responses.$200>
  /**
   * GetInventoryPerformanceSeries
   */
  'GetInventoryPerformanceSeries'(
    parameters?: Parameters<Paths.GetInventoryPerformanceSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryPerformanceSeries.Responses.$200>
  /**
   * PostInventorySkuHighImpactList
   */
  'PostInventorySkuHighImpactList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventorySkuHighImpactList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventorySkuHighImpactList.Responses.$200>
  /**
   * GetInventoryBasicHealthCheck
   */
  'GetInventoryBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryBasicHealthCheck.Responses.$200>
  /**
   * GetInventoryFullHealthCheck
   */
  'GetInventoryFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryFullHealthCheck.Responses.$200>
  /**
   * GetInventoryHandlingUnitsTrayed
   */
  'GetInventoryHandlingUnitsTrayed'(
    parameters?: Parameters<Paths.GetInventoryHandlingUnitsTrayed.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryHandlingUnitsTrayed.Responses.$200>
  /**
   * GetDataAnalysisTimestampData
   */
  'GetDataAnalysisTimestampData'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetDataAnalysisTimestampData.Responses.$200>
  /**
   * PostInventoryForecastList
   */
  'PostInventoryForecastList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventoryForecastList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryForecastList.Responses.$200>
  /**
   * PostInventoryForecastListExport - Creates an excel export for inventory forecast data.
   */
  'PostInventoryForecastListExport'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventoryForecastListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryForecastListExport.Responses.$200>
  /**
   * GetInventoryForecastSkuLocations
   */
  'GetInventoryForecastSkuLocations'(
    parameters?: Parameters<Paths.GetInventoryForecastSkuLocations.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryForecastSkuLocations.Responses.$200>
  /**
   * GetInventoryForecastSkuOrders
   */
  'GetInventoryForecastSkuOrders'(
    parameters?: Parameters<Paths.GetInventoryForecastSkuOrders.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryForecastSkuOrders.Responses.$200>
  /**
   * GetInventorySkuForecast
   */
  'GetInventorySkuForecast'(
    parameters?: Parameters<Paths.GetInventorySkuForecast.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventorySkuForecast.Responses.$200>
  /**
   * GetInventoryFilter
   */
  'GetInventoryFilter'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryFilter.Responses.$200>
  /**
   * PostInventoryContainersList - Post inventory container data with filters
   */
  'PostInventoryContainersList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventoryContainersList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryContainersList.Responses.$200>
  /**
   * PostInventoryContainersListExport
   */
  'PostInventoryContainersListExport'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostInventoryContainersListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryContainersListExport.Responses.$200>
  /**
   * PostInventoryContainerEventsList - Post inventory container events data with filters and sorting.
   */
  'PostInventoryContainerEventsList'(
    parameters?: Parameters<Paths.PostInventoryContainerEventsList.PathParameters> | null,
    data?: Paths.PostInventoryContainerEventsList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryContainerEventsList.Responses.$200>
  /**
   * PostInventoryContainerEventsListExport - Creates an excel export for inventory container events.
   */
  'PostInventoryContainerEventsListExport'(
    parameters?: Parameters<Paths.PostInventoryContainerEventsListExport.PathParameters> | null,
    data?: Paths.PostInventoryContainerEventsListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostInventoryContainerEventsListExport.Responses.$200>
  /**
   * GetInventoryBinLocations - Endpoint for retrieving bin locations for a given DMS aisle and level.
   */
  'GetInventoryBinLocations'(
    parameters?: Parameters<Paths.GetInventoryBinLocations.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryBinLocations.Responses.$200>
  /**
   * GetInventoryAdvicesOutstanding
   */
  'GetInventoryAdvicesOutstanding'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAdvicesOutstanding.Responses.$200>
  /**
   * GetInventoryAdvicesList
   */
  'GetInventoryAdvicesList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAdvicesList.Responses.$200>
  /**
   * GetInventoryAdvicesInProgress
   */
  'GetInventoryAdvicesInProgress'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAdvicesInProgress.Responses.$200>
  /**
   * GetInventoryAdvicesFinished
   */
  'GetInventoryAdvicesFinished'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAdvicesFinished.Responses.$200>
  /**
   * GetInventoryAdvicesDetails
   */
  'GetInventoryAdvicesDetails'(
    parameters?: Parameters<Paths.GetInventoryAdvicesDetails.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAdvicesDetails.Responses.$200>
  /**
   * GetInventoryAdvicesCycleTime
   */
  'GetInventoryAdvicesCycleTime'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAdvicesCycleTime.Responses.$200>
  /**
   * GetInventoryAccuracy
   */
  'GetInventoryAccuracy'(
    parameters?: Parameters<Paths.GetInventoryAccuracy.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryAccuracy.Responses.$200>
  /**
   * GetInventoryContainerEventsDetails - Get container KPI metrics for a specific container ID.
   */
  'GetInventoryContainerEventsDetails'(
    parameters?: Parameters<Paths.GetInventoryContainerEventsDetails.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetInventoryContainerEventsDetails.Responses.$200>
  /**
   * GetEquipmentWorkstationStarvedBlockedTimeSeries
   */
  'GetEquipmentWorkstationStarvedBlockedTimeSeries'(
    parameters?: Parameters<Paths.GetEquipmentWorkstationStarvedBlockedTimeSeries.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentWorkstationStarvedBlockedTimeSeries.Responses.$200>
  /**
   * GetEquipmentWorkstationOperatorActivity
   */
  'GetEquipmentWorkstationOperatorActivity'(
    parameters?: Parameters<Paths.GetEquipmentWorkstationOperatorActivity.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentWorkstationOperatorActivity.Responses.$200>
  /**
   * GetEquipmentWorkstationLineRatesSeries
   */
  'GetEquipmentWorkstationLineRatesSeries'(
    parameters?: Parameters<Paths.GetEquipmentWorkstationLineRatesSeries.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentWorkstationLineRatesSeries.Responses.$200>
  /**
   * GetEquipmentWorkstationMovementsDetail
   */
  'GetEquipmentWorkstationMovementsDetail'(
    parameters?: Parameters<Paths.GetEquipmentWorkstationMovementsDetail.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentWorkstationMovementsDetail.Responses.$200>
  /**
   * GetEquipmentWorkstationAisleActiveFaults
   */
  'GetEquipmentWorkstationAisleActiveFaults'(
    parameters?: Parameters<Paths.GetEquipmentWorkstationAisleActiveFaults.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentWorkstationAisleActiveFaults.Responses.$200>
  /**
   * GetEquipmentSummaryWorkstations
   */
  'GetEquipmentSummaryWorkstations'(
    parameters?: Parameters<Paths.GetEquipmentSummaryWorkstations.QueryParameters & Paths.GetEquipmentSummaryWorkstations.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentSummaryWorkstations.Responses.$200>
  /**
   * GetEquipmentSummaryAreas
   */
  'GetEquipmentSummaryAreas'(
    parameters?: Parameters<Paths.GetEquipmentSummaryAreas.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentSummaryAreas.Responses.$200>
  /**
   * GetEquipmentSummaryAisle
   */
  'GetEquipmentSummaryAisle'(
    parameters?: Parameters<Paths.GetEquipmentSummaryAisle.QueryParameters & Paths.GetEquipmentSummaryAisle.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentSummaryAisle.Responses.$200>
  /**
   * PostEquipmentEventsList
   */
  'PostEquipmentEventsList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentEventsList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentEventsList.Responses.$200>
  /**
   * GetEquipmentOutboundRate
   */
  'GetEquipmentOutboundRate'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentOutboundRate.Responses.$200>
  /**
   * GetEquipmentBasicHealthCheck
   */
  'GetEquipmentBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentBasicHealthCheck.Responses.$200>
  /**
   * GetEquipmentFullHealthCheck
   */
  'GetEquipmentFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentFullHealthCheck.Responses.$200>
  /**
   * PostEquipmentFaultsStatusList
   */
  'PostEquipmentFaultsStatusList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsStatusList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsStatusList.Responses.$200>
  /**
   * GetEquipmentFaultsSeries
   */
  'GetEquipmentFaultsSeries'(
    parameters?: Parameters<Paths.GetEquipmentFaultsSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentFaultsSeries.Responses.$200>
  /**
   * GetEquipmentFaultsEvents
   */
  'GetEquipmentFaultsEvents'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentFaultsEvents.Responses.$200>
  /**
   * PostEquipmentFaultsMovementsSeries
   */
  'PostEquipmentFaultsMovementsSeries'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsMovementsSeries.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsMovementsSeries.Responses.$200>
  /**
   * PostEquipmentFaultsList
   */
  'PostEquipmentFaultsList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsList.Responses.$200>
  /**
   * PostEquipmentFaultsLevelList
   */
  'PostEquipmentFaultsLevelList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsLevelList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsLevelList.Responses.$200>
  /**
   * getFaultGroupedByCounts
   */
  'getFaultGroupedByCounts'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.GetFaultGroupedByCounts.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetFaultGroupedByCounts.Responses.$200>
  /**
   * PostEquipmentFaultsDeviceIdsList
   */
  'PostEquipmentFaultsDeviceIdsList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsDeviceIdsList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsDeviceIdsList.Responses.$200>
  /**
   * PostEquipmentFaultsDeviceTypeList
   */
  'PostEquipmentFaultsDeviceTypeList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsDeviceTypeList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsDeviceTypeList.Responses.$200>
  /**
   * GetEquipmentFaults
   */
  'GetEquipmentFaults'(
    parameters?: Parameters<Paths.GetEquipmentFaults.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentFaults.Responses.$200>
  /**
   * getFaultAvgDurationByStatus - Endpoint to get the average duration of faults by status for a given date range.
   */
  'getFaultAvgDurationByStatus'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.GetFaultAvgDurationByStatus.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetFaultAvgDurationByStatus.Responses.$200>
  /**
   * GetEquipmentFaultsArea
   */
  'GetEquipmentFaultsArea'(
    parameters?: Parameters<Paths.GetEquipmentFaultsArea.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentFaultsArea.Responses.$200>
  /**
   * PostEquipmentFaultsAisleList
   */
  'PostEquipmentFaultsAisleList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostEquipmentFaultsAisleList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostEquipmentFaultsAisleList.Responses.$200>
  /**
   * GetEquipmentFaultsActiveList
   */
  'GetEquipmentFaultsActiveList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetEquipmentFaultsActiveList.Responses.$200>
  /**
   * GetOperatorsBasicHealthCheck
   */
  'GetOperatorsBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOperatorsBasicHealthCheck.Responses.$200>
  /**
   * GetOperatorsFullHealthCheck
   */
  'GetOperatorsFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOperatorsFullHealthCheck.Responses.$200>
  /**
   * PutOperatorsAreas
   */
  'PutOperatorsAreas'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PutOperatorsAreas.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PutOperatorsAreas.Responses.$200>
  /**
   * GetOperatorsActiveSeries
   */
  'GetOperatorsActiveSeries'(
    parameters?: Parameters<Paths.GetOperatorsActiveSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOperatorsActiveSeries.Responses.$200>
  /**
   * GetOperatorsActive
   */
  'GetOperatorsActive'(
    parameters?: Parameters<Paths.GetOperatorsActive.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOperatorsActive.Responses.$200>
  /**
   * GetOperatorsActiveAreas
   */
  'GetOperatorsActiveAreas'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOperatorsActiveAreas.Responses.$200>
  /**
   * GetPickOrdersShipped
   */
  'GetPickOrdersShipped'(
    parameters?: Parameters<Paths.GetPickOrdersShipped.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetPickOrdersShipped.Responses.$200>
  /**
   * GetOrdersPickCycleCount
   */
  'GetOrdersPickCycleCount'(
    parameters?: Parameters<Paths.GetOrdersPickCycleCount.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersPickCycleCount.Responses.$200>
  /**
   * GetOrdersFacilityShipped - Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
   * the total number of orders open between start_date and end_date
   */
  'GetOrdersFacilityShipped'(
    parameters?: Parameters<Paths.GetOrdersFacilityShipped.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersFacilityShipped.Responses.$200>
  /**
   * GetOrdersFacilityProgress
   */
  'GetOrdersFacilityProgress'(
    parameters?: Parameters<Paths.GetOrdersFacilityProgress.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersFacilityProgress.Responses.$200>
  /**
   * GetOrdersCustomerShipped - Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
   * the total number of orders open between start_date and end_date
   */
  'GetOrdersCustomerShipped'(
    parameters?: Parameters<Paths.GetOrdersCustomerShipped.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerShipped.Responses.$200>
  /**
   * GetOrdersThroughput
   */
  'GetOrdersThroughput'(
    parameters?: Parameters<Paths.GetOrdersThroughput.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersThroughput.Responses.$200>
  /**
   * GetOrdersThroughputSeries
   */
  'GetOrdersThroughputSeries'(
    parameters?: Parameters<Paths.GetOrdersThroughputSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersThroughputSeries.Responses.$200>
  /**
   * GetOrdersRemaining
   */
  'GetOrdersRemaining'(
    parameters?: Parameters<Paths.GetOrdersRemaining.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersRemaining.Responses.$200>
  /**
   * GetOrdersFulfillment
   */
  'GetOrdersFulfillment'(
    parameters?: Parameters<Paths.GetOrdersFulfillment.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersFulfillment.Responses.$200>
  /**
   * GetOrdersProgressSeries - Gets a series of order progress percentage, averaging by each hour incrementing.
   */
  'GetOrdersProgressSeries'(
    parameters?: Parameters<Paths.GetOrdersProgressSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersProgressSeries.Responses.$200>
  /**
   * GetOrdersPickLineThroughputSeries
   */
  'GetOrdersPickLineThroughputSeries'(
    parameters?: Parameters<Paths.GetOrdersPickLineThroughputSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersPickLineThroughputSeries.Responses.$200>
  /**
   * GetOrdersPerformanceFulfillment
   */
  'GetOrdersPerformanceFulfillment'(
    parameters?: Parameters<Paths.GetOrdersPerformanceFulfillment.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersPerformanceFulfillment.Responses.$200>
  /**
   * GetOrdersOutstanding
   */
  'GetOrdersOutstanding'(
    parameters?: Parameters<Paths.GetOrdersOutstanding.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersOutstanding.Responses.$200>
  /**
   * GetOrdersLineThroughput
   */
  'GetOrdersLineThroughput'(
    parameters?: Parameters<Paths.GetOrdersLineThroughput.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersLineThroughput.Responses.$200>
  /**
   * GetOrdersLineProgress
   */
  'GetOrdersLineProgress'(
    parameters?: Parameters<Paths.GetOrdersLineProgress.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersLineProgress.Responses.$200>
  /**
   * GetOrdersLineProgressSeries
   */
  'GetOrdersLineProgressSeries'(
    parameters?: Parameters<Paths.GetOrdersLineProgressSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersLineProgressSeries.Responses.$200>
  /**
   * GetOrdersBasicHealthCheck
   */
  'GetOrdersBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersBasicHealthCheck.Responses.$200>
  /**
   * GetOrdersFullHealthCheck
   */
  'GetOrdersFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersFullHealthCheck.Responses.$200>
  /**
   * GetOrdersPickCycleTime
   */
  'GetOrdersPickCycleTime'(
    parameters?: Parameters<Paths.GetOrdersPickCycleTime.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersPickCycleTime.Responses.$200>
  /**
   * GetOrdersCycleTimeSeries
   */
  'GetOrdersCycleTimeSeries'(
    parameters?: Parameters<Paths.GetOrdersCycleTimeSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCycleTimeSeries.Responses.$200>
  /**
   * GetOrdersProgress
   */
  'GetOrdersProgress'(
    parameters?: Parameters<Paths.GetOrdersProgress.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersProgress.Responses.$200>
  /**
   * GetOrdersCustomerLineThroughput
   */
  'GetOrdersCustomerLineThroughput'(
    parameters?: Parameters<Paths.GetOrdersCustomerLineThroughput.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerLineThroughput.Responses.$200>
  /**
   * GetOrdersThroughputAreas
   */
  'GetOrdersThroughputAreas'(
    parameters?: Parameters<Paths.GetOrdersThroughputAreas.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersThroughputAreas.Responses.$200>
  /**
   * GetOrdersLineProgressAreas
   */
  'GetOrdersLineProgressAreas'(
    parameters?: Parameters<Paths.GetOrdersLineProgressAreas.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersLineProgressAreas.Responses.$200>
  /**
   * GetOrdersCycleTime
   */
  'GetOrdersCycleTime'(
    parameters?: Parameters<Paths.GetOrdersCycleTime.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCycleTime.Responses.$200>
  /**
   * GetOrdersCompletion
   */
  'GetOrdersCompletion'(
    parameters?: Parameters<Paths.GetOrdersCompletion.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCompletion.Responses.$200>
  /**
   * GetOrdersCustomerThroughput - Calculates the number of orders per hour that were shipped in the given time frame.
   * If an area param is used, then the matching status is used in the calculation instead of 'shipped'.
   * If the end_date is in the future, then the current datetime is used instead.
   */
  'GetOrdersCustomerThroughput'(
    parameters?: Parameters<Paths.GetOrdersCustomerThroughput.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerThroughput.Responses.$200>
  /**
   * GetOrdersCustomerThroughputSeries
   */
  'GetOrdersCustomerThroughputSeries'(
    parameters?: Parameters<Paths.GetOrdersCustomerThroughputSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerThroughputSeries.Responses.$200>
  /**
   * GetOrdersCustomerProgressSeries - Gets a series of order progress percentage, averaging by each hour incrementing.
   */
  'GetOrdersCustomerProgressSeries'(
    parameters?: Parameters<Paths.GetOrdersCustomerProgressSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerProgressSeries.Responses.$200>
  /**
   * GetOrdersCustomerProgress
   */
  'GetOrdersCustomerProgress'(
    parameters?: Parameters<Paths.GetOrdersCustomerProgress.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerProgress.Responses.$200>
  /**
   * GetOrdersCustomerLineThroughputSeries
   */
  'GetOrdersCustomerLineThroughputSeries'(
    parameters?: Parameters<Paths.GetOrdersCustomerLineThroughputSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerLineThroughputSeries.Responses.$200>
  /**
   * GetOrderLinesCustomerProgressSeries - Gets a series of order progress percentage, averaging by each hour incrementing.
   */
  'GetOrderLinesCustomerProgressSeries'(
    parameters?: Parameters<Paths.GetOrderLinesCustomerProgressSeries.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrderLinesCustomerProgressSeries.Responses.$200>
  /**
   * GetOrdersCustomerLineProgress
   */
  'GetOrdersCustomerLineProgress'(
    parameters?: Parameters<Paths.GetOrdersCustomerLineProgress.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerLineProgress.Responses.$200>
  /**
   * GetOrdersCustomerEstimatedCompletion
   */
  'GetOrdersCustomerEstimatedCompletion'(
    parameters?: Parameters<Paths.GetOrdersCustomerEstimatedCompletion.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerEstimatedCompletion.Responses.$200>
  /**
   * GetOrdersCustomerCycleTime
   */
  'GetOrdersCustomerCycleTime'(
    parameters?: Parameters<Paths.GetOrdersCustomerCycleTime.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetOrdersCustomerCycleTime.Responses.$200>
  /**
   * GetWorkstations
   */
  'GetWorkstations'(
    parameters?: Parameters<Paths.GetWorkstations.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstations.Responses.$200>
  /**
   * GetWorkstationSeriesData
   */
  'GetWorkstationSeriesData'(
    parameters?: Parameters<Paths.GetWorkstationSeriesData.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationSeriesData.Responses.$200>
  /**
   * GetWorkstationOrdersStatus
   */
  'GetWorkstationOrdersStatus'(
    parameters?: Parameters<Paths.GetWorkstationOrdersStatus.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationOrdersStatus.Responses.$200>
  /**
   * PostWorkstationOrdersList
   */
  'PostWorkstationOrdersList'(
    parameters?: Parameters<Paths.PostWorkstationOrdersList.QueryParameters> | null,
    data?: Paths.PostWorkstationOrdersList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationOrdersList.Responses.$200>
  /**
   * PostWorkstationOrdersListExport
   */
  'PostWorkstationOrdersListExport'(
    parameters?: Parameters<Paths.PostWorkstationOrdersListExport.QueryParameters> | null,
    data?: Paths.PostWorkstationOrdersListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationOrdersListExport.Responses.$200>
  /**
   * PostWorkstationOrdersDetailsPicksList
   */
  'PostWorkstationOrdersDetailsPicksList'(
    parameters?: Parameters<Paths.PostWorkstationOrdersDetailsPicksList.QueryParameters & Paths.PostWorkstationOrdersDetailsPicksList.PathParameters> | null,
    data?: Paths.PostWorkstationOrdersDetailsPicksList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationOrdersDetailsPicksList.Responses.$200>
  /**
   * PostWorkstationOrdersDetailsPicksListExport
   */
  'PostWorkstationOrdersDetailsPicksListExport'(
    parameters?: Parameters<Paths.PostWorkstationOrdersDetailsPicksListExport.QueryParameters & Paths.PostWorkstationOrdersDetailsPicksListExport.PathParameters> | null,
    data?: Paths.PostWorkstationOrdersDetailsPicksListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationOrdersDetailsPicksListExport.Responses.$200>
  /**
   * GetWorkstationSummary
   */
  'GetWorkstationSummary'(
    parameters?: Parameters<Paths.GetWorkstationSummary.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationSummary.Responses.$200>
  /**
   * GetWorkstationOperatorSummary - 
   * returns {Promise<WorkstationOperatorStats>} contract
   */
  'GetWorkstationOperatorSummary'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationOperatorSummary.Responses.$200>
  /**
   * GetWorkstationPerformanceSummary - 
   * returns {Promise<WorkstationPerformanceStats>} contract
   */
  'GetWorkstationPerformanceSummary'(
    parameters?: Parameters<Paths.GetWorkstationPerformanceSummary.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationPerformanceSummary.Responses.$200>
  /**
   * GetWorkstationHealthSummary - 
   * returns {Promise<WorkstationHealthStats>} contract
   */
  'GetWorkstationHealthSummary'(
    parameters?: Parameters<Paths.GetWorkstationHealthSummary.QueryParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationHealthSummary.Responses.$200>
  /**
   * GetWorkstationBasicHealthCheck
   */
  'GetWorkstationBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationBasicHealthCheck.Responses.$200>
  /**
   * GetWorkstationFullHealthCheck
   */
  'GetWorkstationFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationFullHealthCheck.Responses.$200>
  /**
   * PostWorkstationDailyPerformanceList
   */
  'PostWorkstationDailyPerformanceList'(
    parameters?: Parameters<Paths.PostWorkstationDailyPerformanceList.QueryParameters> | null,
    data?: Paths.PostWorkstationDailyPerformanceList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationDailyPerformanceList.Responses.$200>
  /**
   * GetWorkstationList
   */
  'GetWorkstationList'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetWorkstationList.Responses.$200>
  /**
   * PostWorkstationListExport
   */
  'PostWorkstationListExport'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: Paths.PostWorkstationListExport.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationListExport.Responses.$200>
  /**
   * PostWorkstationContainersList
   */
  'PostWorkstationContainersList'(
    parameters?: Parameters<Paths.PostWorkstationContainersList.QueryParameters> | null,
    data?: Paths.PostWorkstationContainersList.RequestBody,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.PostWorkstationContainersList.Responses.$200>
  /**
   * GetSimulationOutputByJobId
   */
  'GetSimulationOutputByJobId'(
    parameters?: Parameters<Paths.GetSimulationOutputByJobId.PathParameters> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetSimulationOutputByJobId.Responses.$200>
  /**
   * GetSimulationJobs
   */
  'GetSimulationJobs'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetSimulationJobs.Responses.$200>
  /**
   * GetSimulationBasicHealthCheck
   */
  'GetSimulationBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetSimulationBasicHealthCheck.Responses.$200>
  /**
   * GetSimulationFullHealthCheck
   */
  'GetSimulationFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetSimulationFullHealthCheck.Responses.$200>
  /**
   * GetTrustedTicket
   */
  'GetTrustedTicket'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetTrustedTicket.Responses.$200>
  /**
   * GetAdminBasicHealthCheck
   */
  'GetAdminBasicHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetAdminBasicHealthCheck.Responses.$200>
  /**
   * GetAdminFullHealthCheck
   */
  'GetAdminFullHealthCheck'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetAdminFullHealthCheck.Responses.$200>
  /**
   * ResendEmailVerification
   */
  'ResendEmailVerification'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.ResendEmailVerification.Responses.$200>
  /**
   * GetAssignableRoles
   */
  'GetAssignableRoles'(
    parameters?: Parameters<UnknownParamsObject> | null,
    data?: any,
    config?: AxiosRequestConfig  
  ): OperationResponse<Paths.GetAssignableRoles.Responses.$200>
}

export interface PathsDictionary {
  ['/ai/healthcheck']: {
    /**
     * GetAiBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetAiBasicHealthCheck.Responses.$200>
  }
  ['/ai/healthcheck/full']: {
    /**
     * GetAiFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetAiFullHealthCheck.Responses.$200>
  }
  ['/ai/enterprise-search']: {
    /**
     * GetAiEnterpriseSearch
     */
    'get'(
      parameters?: Parameters<Paths.GetAiEnterpriseSearch.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetAiEnterpriseSearch.Responses.$200>
  }
  ['/config/user-writable']: {
    /**
     * PutUserWritable - Updates a user writable setting. This endpoint is specifically for user-level settings.
     * Any authenticated user can update their own settings, and configurators can update any user's settings.
     */
    'put'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PutUserWritable.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PutUserWritable.Responses.$200 | Paths.PutUserWritable.Responses.$201>
  }
  ['/config/process-flow/metric-configs']: {
    /**
     * GetMetricConfigs - Get metric configurations based on filter criteria.
     * Without any query params, returns all metric configs that are not soft_deleted.
     */
    'get'(
      parameters?: Parameters<Paths.GetMetricConfigs.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetMetricConfigs.Responses.$200>
  }
  ['/config/healthcheck']: {
    /**
     * GetConfigBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigBasicHealthCheck.Responses.$200>
  }
  ['/config/healthcheck/full']: {
    /**
     * GetConfigFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigFullHealthCheck.Responses.$200>
  }
  ['/config/settings']: {
    /**
     * GetConfigSettings - Retrieves all settings and their values or a single setting based on id and/or name
     */
    'get'(
      parameters?: Parameters<Paths.GetConfigSettings.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigSettings.Responses.$200>
    /**
     * PutConfigSettings - Updates the specified setting with the properties in settingInfo.  Creates a new setting if
     * the setting isn't found, and the required information is provided.
     */
    'put'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PutConfigSettings.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PutConfigSettings.Responses.$200 | Paths.PutConfigSettings.Responses.$201>
  }
  ['/config/insert-default-config']: {
    /**
     * PostConfigDefaultConfig - Inserts the default settings to seed a new tenant's database
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostConfigDefaultConfig.Responses.$200>
  }
  ['/config/settings/{settingId}']: {
    /**
     * DeleteConfigSetting
     */
    'delete'(
      parameters?: Parameters<Paths.DeleteConfigSetting.QueryParameters & Paths.DeleteConfigSetting.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.DeleteConfigSetting.Responses.$200>
  }
  ['/config/setting-logs']: {
    /**
     * GetConfigSettingLogs - Retrieves logs for a setting
     */
    'get'(
      parameters?: Parameters<Paths.GetConfigSettingLogs.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigSettingLogs.Responses.$200>
  }
  ['/config/facility-config']: {
    /**
     * GetConfigFacilityConfig
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigFacilityConfig.Responses.$200>
  }
  ['/config/curated-data']: {
    /**
     * GetConfigCuratedData - Handle a request for the top 100 rows of a table.
     */
    'get'(
      parameters?: Parameters<Paths.GetConfigCuratedData.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigCuratedData.Responses.$200>
  }
  ['/config/v2/curated-data']: {
    /**
     * GetConfigCuratedDataV2 - Handle a request for the top 100 rows of a table.
     */
    'get'(
      parameters?: Parameters<Paths.GetConfigCuratedDataV2.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigCuratedDataV2.Responses.$200>
  }
  ['/config/curated-tables/list']: {
    /**
     * GetConfigCuratedTablesList - Endpoint for getting a list of tables available in the curated dataset.
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigCuratedTablesList.Responses.$200>
  }
  ['/config/app-config']: {
    /**
     * GetConfigAppConfig
     */
    'get'(
      parameters?: Parameters<Paths.GetConfigAppConfig.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetConfigAppConfig.Responses.$200>
  }
  ['/data-explorer/search']: {
    /**
     * GetDataExplorerSearch
     */
    'get'(
      parameters?: Parameters<Paths.GetDataExplorerSearch.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerSearch.Responses.$200>
  }
  ['/data-explorer/results']: {
    /**
     * GetDataExplorerResults
     */
    'get'(
      parameters?: Parameters<Paths.GetDataExplorerResults.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerResults.Responses.$200>
  }
  ['/data-explorer/results/{resultId}']: {
    /**
     * GetDataExplorerResult
     */
    'get'(
      parameters?: Parameters<Paths.GetDataExplorerResult.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerResult.Responses.$200>
    /**
     * DeleteDataExplorerResult
     */
    'delete'(
      parameters?: Parameters<Paths.DeleteDataExplorerResult.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.DeleteDataExplorerResult.Responses.$200>
    /**
     * PutDataExplorerResult
     */
    'put'(
      parameters?: Parameters<Paths.PutDataExplorerResult.PathParameters> | null,
      data?: Paths.PutDataExplorerResult.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PutDataExplorerResult.Responses.$200>
  }
  ['/data-explorer/recommendations']: {
    /**
     * GetDataExplorerRecommendations
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerRecommendations.Responses.$200>
  }
  ['/data-explorer/healthcheck']: {
    /**
     * GetDataExplorerBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerBasicHealthCheck.Responses.$200>
  }
  ['/data-explorer/healthcheck/full']: {
    /**
     * GetDataExplorerFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerFullHealthCheck.Responses.$200>
  }
  ['/data-explorer/agentsearch']: {
    /**
     * GetDataExplorerAgentSearch - Retrieves an example data explorer result.
     */
    'get'(
      parameters?: Parameters<Paths.GetDataExplorerAgentSearch.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataExplorerAgentSearch.Responses.$200>
  }
  ['/diagnostics/infrastructure/list']: {
    /**
     * GetDiagnosticsInfrastructureList
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDiagnosticsInfrastructureList.Responses.$200>
  }
  ['/diagnostics/healthcheck']: {
    /**
     * GetDiagnosticsBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDiagnosticsBasicHealthCheck.Responses.$200>
  }
  ['/diagnostics/healthcheck/full']: {
    /**
     * GetDiagnosticsFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDiagnosticsFullHealthCheck.Responses.$200>
  }
  ['/inventory/upload/known-demand']: {
    /**
     * PostInventoryUploadKnownDemand - Receives two xls files and parses them into multiline JSON to forward to the EDP PubSub Topic
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventoryUploadKnownDemand.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryUploadKnownDemand.Responses.$200>
  }
  ['/inventory/upload/recent-activity']: {
    /**
     * GetInventoryUploadRecentActivity
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryUploadRecentActivity.Responses.$200>
  }
  ['/inventory/storage/utilization']: {
    /**
     * GetInventoryStorageUtilization
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryStorageUtilization.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryStorageUtilization.Responses.$200>
  }
  ['/inventory/stock/distribution/under/percentage/series']: {
    /**
     * GetInventoryStockDistributionUnderPercentageSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryStockDistributionUnderPercentageSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryStockDistributionUnderPercentageSeries.Responses.$200>
  }
  ['/inventory/stock/distribution/over/percentage/series']: {
    /**
     * GetInventoryStockDistributionOverPercentageSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryStockDistributionOverPercentageSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryStockDistributionOverPercentageSeries.Responses.$200>
  }
  ['/inventory/stock/distribution/no/percentage/series']: {
    /**
     * GetInventoryStockDistributionNoPercentageSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryStockDistributionNoPercentageSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryStockDistributionNoPercentageSeries.Responses.$200>
  }
  ['/inventory/stock/distribution/at/percentage/series']: {
    /**
     * GetInventoryStockDistributionAtPercentageSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryStockDistributionAtPercentageSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryStockDistributionAtPercentageSeries.Responses.$200>
  }
  ['/inventory/skus/list']: {
    /**
     * PostInventorySkusList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventorySkusList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventorySkusList.Responses.$200>
  }
  ['/inventory/skus/list/export']: {
    /**
     * PostInventorySkusListExport
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventorySkusListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventorySkusListExport.Responses.$200>
  }
  ['/inventory/replenishment/task-type-series']: {
    /**
     * GetInventoryReplenishmentTaskTypeSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryReplenishmentTaskTypeSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryReplenishmentTaskTypeSeries.Responses.$200>
  }
  ['/inventory/replenishment/details']: {
    /**
     * GetInventoryReplenishmentDetails
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryReplenishmentDetails.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryReplenishmentDetails.Responses.$200>
  }
  ['/inventory/process-flow/areas']: {
    /**
     * GetInventoryProcessFlowAreas
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryProcessFlowAreas.Responses.$200>
  }
  ['/inventory/process-flow/area/{areaId}']: {
    /**
     * GetInventoryProcessFlowAreaById
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryProcessFlowAreaById.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryProcessFlowAreaById.Responses.$200>
  }
  ['/inventory/process-flow/details/{type}/{elementId}']: {
    /**
     * GetInventoryProcessFlowGraphDetails
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryProcessFlowGraphDetails.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryProcessFlowGraphDetails.Responses.$200>
  }
  ['/inventory/process-flow/areas/{areaId}']: {
    /**
     * UpdateInventoryProcessFlowArea
     */
    'put'(
      parameters?: Parameters<Paths.UpdateInventoryProcessFlowArea.QueryParameters & Paths.UpdateInventoryProcessFlowArea.PathParameters> | null,
      data?: Paths.UpdateInventoryProcessFlowArea.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.UpdateInventoryProcessFlowArea.Responses.$200>
  }
  ['/inventory/process-flow/edges/{edgeId}']: {
    /**
     * UpdateInventoryProcessFlowEdge
     */
    'put'(
      parameters?: Parameters<Paths.UpdateInventoryProcessFlowEdge.PathParameters> | null,
      data?: Paths.UpdateInventoryProcessFlowEdge.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.UpdateInventoryProcessFlowEdge.Responses.$200>
  }
  ['/inventory/process-flow/metrics/{metricId}']: {
    /**
     * UpdateInventoryProcessFlowMetric
     */
    'put'(
      parameters?: Parameters<Paths.UpdateInventoryProcessFlowMetric.PathParameters> | null,
      data?: Paths.UpdateInventoryProcessFlowMetric.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.UpdateInventoryProcessFlowMetric.Responses.$200>
  }
  ['/inventory/process-flow/clear-cache']: {
    /**
     * ClearInventoryProcessFlowCache
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.ClearInventoryProcessFlowCache.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.ClearInventoryProcessFlowCache.Responses.$200>
  }
  ['/inventory/performance/series']: {
    /**
     * GetInventoryPerformanceSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryPerformanceSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryPerformanceSeries.Responses.$200>
  }
  ['/inventory/sku/high-impact/list']: {
    /**
     * PostInventorySkuHighImpactList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventorySkuHighImpactList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventorySkuHighImpactList.Responses.$200>
  }
  ['/inventory/healthcheck']: {
    /**
     * GetInventoryBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryBasicHealthCheck.Responses.$200>
  }
  ['/inventory/healthcheck/full']: {
    /**
     * GetInventoryFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryFullHealthCheck.Responses.$200>
  }
  ['/inventory/handling-units/trayed']: {
    /**
     * GetInventoryHandlingUnitsTrayed
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryHandlingUnitsTrayed.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryHandlingUnitsTrayed.Responses.$200>
  }
  ['/inventory/forecast/data-analysis-timestamp']: {
    /**
     * GetDataAnalysisTimestampData
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetDataAnalysisTimestampData.Responses.$200>
  }
  ['/inventory/forecast/list']: {
    /**
     * PostInventoryForecastList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventoryForecastList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryForecastList.Responses.$200>
  }
  ['/inventory/forecast/list/export']: {
    /**
     * PostInventoryForecastListExport - Creates an excel export for inventory forecast data.
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventoryForecastListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryForecastListExport.Responses.$200>
  }
  ['/inventory/forecast/{skuId}/locations']: {
    /**
     * GetInventoryForecastSkuLocations
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryForecastSkuLocations.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryForecastSkuLocations.Responses.$200>
  }
  ['/inventory/forecast/{skuId}/orders']: {
    /**
     * GetInventoryForecastSkuOrders
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryForecastSkuOrders.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryForecastSkuOrders.Responses.$200>
  }
  ['/inventory/forecast/{skuId}']: {
    /**
     * GetInventorySkuForecast
     */
    'get'(
      parameters?: Parameters<Paths.GetInventorySkuForecast.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventorySkuForecast.Responses.$200>
  }
  ['/inventory/filter']: {
    /**
     * GetInventoryFilter
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryFilter.Responses.$200>
  }
  ['/inventory/containers/list']: {
    /**
     * PostInventoryContainersList - Post inventory container data with filters
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventoryContainersList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryContainersList.Responses.$200>
  }
  ['/inventory/containers/list/export']: {
    /**
     * PostInventoryContainersListExport
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostInventoryContainersListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryContainersListExport.Responses.$200>
  }
  ['/inventory/container-events/list/{containerId}']: {
    /**
     * PostInventoryContainerEventsList - Post inventory container events data with filters and sorting.
     */
    'post'(
      parameters?: Parameters<Paths.PostInventoryContainerEventsList.PathParameters> | null,
      data?: Paths.PostInventoryContainerEventsList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryContainerEventsList.Responses.$200>
  }
  ['/inventory/container-events/list/export/{containerId}']: {
    /**
     * PostInventoryContainerEventsListExport - Creates an excel export for inventory container events.
     */
    'post'(
      parameters?: Parameters<Paths.PostInventoryContainerEventsListExport.PathParameters> | null,
      data?: Paths.PostInventoryContainerEventsListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostInventoryContainerEventsListExport.Responses.$200>
  }
  ['/inventory/bin-locations']: {
    /**
     * GetInventoryBinLocations - Endpoint for retrieving bin locations for a given DMS aisle and level.
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryBinLocations.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryBinLocations.Responses.$200>
  }
  ['/inventory/advices/outstanding']: {
    /**
     * GetInventoryAdvicesOutstanding
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAdvicesOutstanding.Responses.$200>
  }
  ['/inventory/advices/list']: {
    /**
     * GetInventoryAdvicesList
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAdvicesList.Responses.$200>
  }
  ['/inventory/advices/in-progress']: {
    /**
     * GetInventoryAdvicesInProgress
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAdvicesInProgress.Responses.$200>
  }
  ['/inventory/advices/finished']: {
    /**
     * GetInventoryAdvicesFinished
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAdvicesFinished.Responses.$200>
  }
  ['/inventory/advices/{adviceId}/details']: {
    /**
     * GetInventoryAdvicesDetails
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryAdvicesDetails.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAdvicesDetails.Responses.$200>
  }
  ['/inventory/advices/cycle-time']: {
    /**
     * GetInventoryAdvicesCycleTime
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAdvicesCycleTime.Responses.$200>
  }
  ['/inventory/accuracy']: {
    /**
     * GetInventoryAccuracy
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryAccuracy.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryAccuracy.Responses.$200>
  }
  ['/inventory/container-events/{containerId}']: {
    /**
     * GetInventoryContainerEventsDetails - Get container KPI metrics for a specific container ID.
     */
    'get'(
      parameters?: Parameters<Paths.GetInventoryContainerEventsDetails.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetInventoryContainerEventsDetails.Responses.$200>
  }
  ['/equipment/workstation/{workstationId}/starved-blocked-time/series']: {
    /**
     * GetEquipmentWorkstationStarvedBlockedTimeSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentWorkstationStarvedBlockedTimeSeries.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentWorkstationStarvedBlockedTimeSeries.Responses.$200>
  }
  ['/equipment/workstation/{workstationId}/operator/activity']: {
    /**
     * GetEquipmentWorkstationOperatorActivity
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentWorkstationOperatorActivity.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentWorkstationOperatorActivity.Responses.$200>
  }
  ['/equipment/workstation/{workstationId}/line-rates/series']: {
    /**
     * GetEquipmentWorkstationLineRatesSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentWorkstationLineRatesSeries.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentWorkstationLineRatesSeries.Responses.$200>
  }
  ['/equipment/workstation/{workstationId}/movements/detail']: {
    /**
     * GetEquipmentWorkstationMovementsDetail
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentWorkstationMovementsDetail.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentWorkstationMovementsDetail.Responses.$200>
  }
  ['/equipment/workstation/{workstationId}/aisle/active-faults']: {
    /**
     * GetEquipmentWorkstationAisleActiveFaults
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentWorkstationAisleActiveFaults.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentWorkstationAisleActiveFaults.Responses.$200>
  }
  ['/equipment/summary/workstations/{workstationId}']: {
    /**
     * GetEquipmentSummaryWorkstations
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentSummaryWorkstations.QueryParameters & Paths.GetEquipmentSummaryWorkstations.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentSummaryWorkstations.Responses.$200>
  }
  ['/equipment/summary/areas']: {
    /**
     * GetEquipmentSummaryAreas
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentSummaryAreas.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentSummaryAreas.Responses.$200>
  }
  ['/equipment/summary/aisles/{aisleId}']: {
    /**
     * GetEquipmentSummaryAisle
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentSummaryAisle.QueryParameters & Paths.GetEquipmentSummaryAisle.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentSummaryAisle.Responses.$200>
  }
  ['/equipment/events/list']: {
    /**
     * PostEquipmentEventsList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentEventsList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentEventsList.Responses.$200>
  }
  ['/equipment/outbound-rate']: {
    /**
     * GetEquipmentOutboundRate
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentOutboundRate.Responses.$200>
  }
  ['/equipment/healthcheck']: {
    /**
     * GetEquipmentBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentBasicHealthCheck.Responses.$200>
  }
  ['/equipment/healthcheck/full']: {
    /**
     * GetEquipmentFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentFullHealthCheck.Responses.$200>
  }
  ['/equipment/faults/status/list']: {
    /**
     * PostEquipmentFaultsStatusList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsStatusList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsStatusList.Responses.$200>
  }
  ['/equipment/faults/series']: {
    /**
     * GetEquipmentFaultsSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentFaultsSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentFaultsSeries.Responses.$200>
  }
  ['/equipment/faults/events']: {
    /**
     * GetEquipmentFaultsEvents
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentFaultsEvents.Responses.$200>
  }
  ['/equipment/faults/movements/series']: {
    /**
     * PostEquipmentFaultsMovementsSeries
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsMovementsSeries.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsMovementsSeries.Responses.$200>
  }
  ['/equipment/faults/list']: {
    /**
     * PostEquipmentFaultsList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsList.Responses.$200>
  }
  ['/equipment/faults/level/list']: {
    /**
     * PostEquipmentFaultsLevelList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsLevelList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsLevelList.Responses.$200>
  }
  ['/equipment/faults/grouped/count/series']: {
    /**
     * getFaultGroupedByCounts
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.GetFaultGroupedByCounts.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetFaultGroupedByCounts.Responses.$200>
  }
  ['/equipment/faults/device-id/list']: {
    /**
     * PostEquipmentFaultsDeviceIdsList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsDeviceIdsList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsDeviceIdsList.Responses.$200>
  }
  ['/equipment/faults/device-type/list']: {
    /**
     * PostEquipmentFaultsDeviceTypeList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsDeviceTypeList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsDeviceTypeList.Responses.$200>
  }
  ['/equipment/faults']: {
    /**
     * GetEquipmentFaults
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentFaults.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentFaults.Responses.$200>
  }
  ['/equipment/faults/average/duration/status/series']: {
    /**
     * getFaultAvgDurationByStatus - Endpoint to get the average duration of faults by status for a given date range.
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.GetFaultAvgDurationByStatus.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetFaultAvgDurationByStatus.Responses.$200>
  }
  ['/equipment/faults/area']: {
    /**
     * GetEquipmentFaultsArea
     */
    'get'(
      parameters?: Parameters<Paths.GetEquipmentFaultsArea.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentFaultsArea.Responses.$200>
  }
  ['/equipment/faults/aisle/list']: {
    /**
     * PostEquipmentFaultsAisleList
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostEquipmentFaultsAisleList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostEquipmentFaultsAisleList.Responses.$200>
  }
  ['/equipment/faults/active/list']: {
    /**
     * GetEquipmentFaultsActiveList
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetEquipmentFaultsActiveList.Responses.$200>
  }
  ['/operators/healthcheck']: {
    /**
     * GetOperatorsBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOperatorsBasicHealthCheck.Responses.$200>
  }
  ['/operators/healthcheck/full']: {
    /**
     * GetOperatorsFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOperatorsFullHealthCheck.Responses.$200>
  }
  ['/operators/areas']: {
    /**
     * PutOperatorsAreas
     */
    'put'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PutOperatorsAreas.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PutOperatorsAreas.Responses.$200>
  }
  ['/operators/active/series']: {
    /**
     * GetOperatorsActiveSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOperatorsActiveSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOperatorsActiveSeries.Responses.$200>
  }
  ['/operators/active']: {
    /**
     * GetOperatorsActive
     */
    'get'(
      parameters?: Parameters<Paths.GetOperatorsActive.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOperatorsActive.Responses.$200>
  }
  ['/operators/active/areas']: {
    /**
     * GetOperatorsActiveAreas
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOperatorsActiveAreas.Responses.$200>
  }
  ['/orders/shipped']: {
    /**
     * GetPickOrdersShipped
     */
    'get'(
      parameters?: Parameters<Paths.GetPickOrdersShipped.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetPickOrdersShipped.Responses.$200>
  }
  ['/orders/pick/cycle-count']: {
    /**
     * GetOrdersPickCycleCount
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersPickCycleCount.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersPickCycleCount.Responses.$200>
  }
  ['/orders/facility/shipped']: {
    /**
     * GetOrdersFacilityShipped - Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
     * the total number of orders open between start_date and end_date
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersFacilityShipped.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersFacilityShipped.Responses.$200>
  }
  ['/orders/facility/progress']: {
    /**
     * GetOrdersFacilityProgress
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersFacilityProgress.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersFacilityProgress.Responses.$200>
  }
  ['/orders/customer/shipped']: {
    /**
     * GetOrdersCustomerShipped - Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
     * the total number of orders open between start_date and end_date
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerShipped.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerShipped.Responses.$200>
  }
  ['/orders/throughput']: {
    /**
     * GetOrdersThroughput
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersThroughput.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersThroughput.Responses.$200>
  }
  ['/orders/throughput/series']: {
    /**
     * GetOrdersThroughputSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersThroughputSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersThroughputSeries.Responses.$200>
  }
  ['/orders/remaining']: {
    /**
     * GetOrdersRemaining
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersRemaining.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersRemaining.Responses.$200>
  }
  ['/orders/fulfillment']: {
    /**
     * GetOrdersFulfillment
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersFulfillment.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersFulfillment.Responses.$200>
  }
  ['/orders/pick/progress/series']: {
    /**
     * GetOrdersProgressSeries - Gets a series of order progress percentage, averaging by each hour incrementing.
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersProgressSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersProgressSeries.Responses.$200>
  }
  ['/orders/pick/line/throughput/series']: {
    /**
     * GetOrdersPickLineThroughputSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersPickLineThroughputSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersPickLineThroughputSeries.Responses.$200>
  }
  ['/orders/performance/fulfillment']: {
    /**
     * GetOrdersPerformanceFulfillment
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersPerformanceFulfillment.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersPerformanceFulfillment.Responses.$200>
  }
  ['/orders/outstanding']: {
    /**
     * GetOrdersOutstanding
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersOutstanding.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersOutstanding.Responses.$200>
  }
  ['/orders/line/throughput']: {
    /**
     * GetOrdersLineThroughput
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersLineThroughput.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersLineThroughput.Responses.$200>
  }
  ['/orders/lineprogress']: {
    /**
     * GetOrdersLineProgress
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersLineProgress.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersLineProgress.Responses.$200>
  }
  ['/orders/lineprogress/series']: {
    /**
     * GetOrdersLineProgressSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersLineProgressSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersLineProgressSeries.Responses.$200>
  }
  ['/orders/healthcheck']: {
    /**
     * GetOrdersBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersBasicHealthCheck.Responses.$200>
  }
  ['/orders/healthcheck/full']: {
    /**
     * GetOrdersFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersFullHealthCheck.Responses.$200>
  }
  ['/orders/pick/cycletime']: {
    /**
     * GetOrdersPickCycleTime
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersPickCycleTime.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersPickCycleTime.Responses.$200>
  }
  ['/orders/cycletime/series']: {
    /**
     * GetOrdersCycleTimeSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCycleTimeSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCycleTimeSeries.Responses.$200>
  }
  ['/orders/progress']: {
    /**
     * GetOrdersProgress
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersProgress.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersProgress.Responses.$200>
  }
  ['/orders/customer/line/throughput']: {
    /**
     * GetOrdersCustomerLineThroughput
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerLineThroughput.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerLineThroughput.Responses.$200>
  }
  ['/orders/throughput/areas']: {
    /**
     * GetOrdersThroughputAreas
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersThroughputAreas.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersThroughputAreas.Responses.$200>
  }
  ['/orders/lineprogress/areas']: {
    /**
     * GetOrdersLineProgressAreas
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersLineProgressAreas.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersLineProgressAreas.Responses.$200>
  }
  ['/orders/cycletime/areas']: {
    /**
     * GetOrdersCycleTime
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCycleTime.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCycleTime.Responses.$200>
  }
  ['/orders/completion']: {
    /**
     * GetOrdersCompletion
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCompletion.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCompletion.Responses.$200>
  }
  ['/orders/customer/throughput']: {
    /**
     * GetOrdersCustomerThroughput - Calculates the number of orders per hour that were shipped in the given time frame.
     * If an area param is used, then the matching status is used in the calculation instead of 'shipped'.
     * If the end_date is in the future, then the current datetime is used instead.
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerThroughput.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerThroughput.Responses.$200>
  }
  ['/orders/customer/throughput/series']: {
    /**
     * GetOrdersCustomerThroughputSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerThroughputSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerThroughputSeries.Responses.$200>
  }
  ['/orders/customer/progress/series']: {
    /**
     * GetOrdersCustomerProgressSeries - Gets a series of order progress percentage, averaging by each hour incrementing.
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerProgressSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerProgressSeries.Responses.$200>
  }
  ['/orders/customer/progress']: {
    /**
     * GetOrdersCustomerProgress
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerProgress.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerProgress.Responses.$200>
  }
  ['/orders/customer/line/throughput/series']: {
    /**
     * GetOrdersCustomerLineThroughputSeries
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerLineThroughputSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerLineThroughputSeries.Responses.$200>
  }
  ['/orders/customer/line/progress/series']: {
    /**
     * GetOrderLinesCustomerProgressSeries - Gets a series of order progress percentage, averaging by each hour incrementing.
     */
    'get'(
      parameters?: Parameters<Paths.GetOrderLinesCustomerProgressSeries.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrderLinesCustomerProgressSeries.Responses.$200>
  }
  ['/orders/customer/line/progress']: {
    /**
     * GetOrdersCustomerLineProgress
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerLineProgress.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerLineProgress.Responses.$200>
  }
  ['/orders/customer/completion']: {
    /**
     * GetOrdersCustomerEstimatedCompletion
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerEstimatedCompletion.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerEstimatedCompletion.Responses.$200>
  }
  ['/orders/customer/cycletime']: {
    /**
     * GetOrdersCustomerCycleTime
     */
    'get'(
      parameters?: Parameters<Paths.GetOrdersCustomerCycleTime.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetOrdersCustomerCycleTime.Responses.$200>
  }
  ['/workstation/workstations']: {
    /**
     * GetWorkstations
     */
    'get'(
      parameters?: Parameters<Paths.GetWorkstations.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstations.Responses.$200>
  }
  ['/workstation/series']: {
    /**
     * GetWorkstationSeriesData
     */
    'get'(
      parameters?: Parameters<Paths.GetWorkstationSeriesData.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationSeriesData.Responses.$200>
  }
  ['/workstation/orders/status']: {
    /**
     * GetWorkstationOrdersStatus
     */
    'get'(
      parameters?: Parameters<Paths.GetWorkstationOrdersStatus.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationOrdersStatus.Responses.$200>
  }
  ['/workstation/orders/list']: {
    /**
     * PostWorkstationOrdersList
     */
    'post'(
      parameters?: Parameters<Paths.PostWorkstationOrdersList.QueryParameters> | null,
      data?: Paths.PostWorkstationOrdersList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationOrdersList.Responses.$200>
  }
  ['/workstation/orders/list/export']: {
    /**
     * PostWorkstationOrdersListExport
     */
    'post'(
      parameters?: Parameters<Paths.PostWorkstationOrdersListExport.QueryParameters> | null,
      data?: Paths.PostWorkstationOrdersListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationOrdersListExport.Responses.$200>
  }
  ['/workstation/orders/{orderId}/picks/list']: {
    /**
     * PostWorkstationOrdersDetailsPicksList
     */
    'post'(
      parameters?: Parameters<Paths.PostWorkstationOrdersDetailsPicksList.QueryParameters & Paths.PostWorkstationOrdersDetailsPicksList.PathParameters> | null,
      data?: Paths.PostWorkstationOrdersDetailsPicksList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationOrdersDetailsPicksList.Responses.$200>
  }
  ['/workstation/orders/{orderId}/picks/list/export']: {
    /**
     * PostWorkstationOrdersDetailsPicksListExport
     */
    'post'(
      parameters?: Parameters<Paths.PostWorkstationOrdersDetailsPicksListExport.QueryParameters & Paths.PostWorkstationOrdersDetailsPicksListExport.PathParameters> | null,
      data?: Paths.PostWorkstationOrdersDetailsPicksListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationOrdersDetailsPicksListExport.Responses.$200>
  }
  ['/workstation/metrics/summary']: {
    /**
     * GetWorkstationSummary
     */
    'get'(
      parameters?: Parameters<Paths.GetWorkstationSummary.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationSummary.Responses.$200>
  }
  ['/workstation/metrics/operators']: {
    /**
     * GetWorkstationOperatorSummary - 
     * returns {Promise<WorkstationOperatorStats>} contract
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationOperatorSummary.Responses.$200>
  }
  ['/workstation/metrics/performance']: {
    /**
     * GetWorkstationPerformanceSummary - 
     * returns {Promise<WorkstationPerformanceStats>} contract
     */
    'get'(
      parameters?: Parameters<Paths.GetWorkstationPerformanceSummary.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationPerformanceSummary.Responses.$200>
  }
  ['/workstation/metrics/health']: {
    /**
     * GetWorkstationHealthSummary - 
     * returns {Promise<WorkstationHealthStats>} contract
     */
    'get'(
      parameters?: Parameters<Paths.GetWorkstationHealthSummary.QueryParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationHealthSummary.Responses.$200>
  }
  ['/workstation/healthcheck']: {
    /**
     * GetWorkstationBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationBasicHealthCheck.Responses.$200>
  }
  ['/workstation/healthcheck/full']: {
    /**
     * GetWorkstationFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationFullHealthCheck.Responses.$200>
  }
  ['/workstation/daily-performance/list']: {
    /**
     * PostWorkstationDailyPerformanceList
     */
    'post'(
      parameters?: Parameters<Paths.PostWorkstationDailyPerformanceList.QueryParameters> | null,
      data?: Paths.PostWorkstationDailyPerformanceList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationDailyPerformanceList.Responses.$200>
  }
  ['/workstation/list']: {
    /**
     * GetWorkstationList
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetWorkstationList.Responses.$200>
  }
  ['/workstation/list/export']: {
    /**
     * PostWorkstationListExport
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: Paths.PostWorkstationListExport.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationListExport.Responses.$200>
  }
  ['/workstation/containers/list']: {
    /**
     * PostWorkstationContainersList
     */
    'post'(
      parameters?: Parameters<Paths.PostWorkstationContainersList.QueryParameters> | null,
      data?: Paths.PostWorkstationContainersList.RequestBody,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.PostWorkstationContainersList.Responses.$200>
  }
  ['/simulation/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output']: {
    /**
     * GetSimulationOutputByJobId
     */
    'get'(
      parameters?: Parameters<Paths.GetSimulationOutputByJobId.PathParameters> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetSimulationOutputByJobId.Responses.$200>
  }
  ['/simulation/jobs/list']: {
    /**
     * GetSimulationJobs
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetSimulationJobs.Responses.$200>
  }
  ['/simulation/healthcheck']: {
    /**
     * GetSimulationBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetSimulationBasicHealthCheck.Responses.$200>
  }
  ['/simulation/healthcheck/full']: {
    /**
     * GetSimulationFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetSimulationFullHealthCheck.Responses.$200>
  }
  ['/management/auth/trusted']: {
    /**
     * GetTrustedTicket
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetTrustedTicket.Responses.$200>
  }
  ['/admin/healthcheck']: {
    /**
     * GetAdminBasicHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetAdminBasicHealthCheck.Responses.$200>
  }
  ['/admin/healthcheck/full']: {
    /**
     * GetAdminFullHealthCheck
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetAdminFullHealthCheck.Responses.$200>
  }
  ['/admin/auth/resend-verification']: {
    /**
     * ResendEmailVerification
     */
    'post'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.ResendEmailVerification.Responses.$200>
  }
  ['/admin/auth/roles']: {
    /**
     * GetAssignableRoles
     */
    'get'(
      parameters?: Parameters<UnknownParamsObject> | null,
      data?: any,
      config?: AxiosRequestConfig  
    ): OperationResponse<Paths.GetAssignableRoles.Responses.$200>
  }
}

export type Client = OpenAPIClient<OperationMethods, PathsDictionary>

