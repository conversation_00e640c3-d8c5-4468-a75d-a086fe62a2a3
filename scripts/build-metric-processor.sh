#!/bin/bash

echo "This script builds and pushes the metric-processor docker image to Google Cloud Artifact Repository."

set -ex

# Docker image name
DOCKER_IMAGE="metric-processor"

# Registry configuration
REGISTRY_URL="us-docker.pkg.dev/ict-o-common-services-kfqd/docker-images"

# Get git commit hash for tagging
TAG=$(git rev-parse HEAD)
printf "Preparing to build and tag image with $TAG\n"

# Docker authentication
gcloud auth list
gcloud auth configure-docker us-docker.pkg.dev
docker-credential-gcloud gcr-login

# Get repo root and navigate to metric-processor directory
REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT/services/metric-processor"

echo "Building Docker image for ${DOCKER_IMAGE} tagged ${TAG}"
echo "Working directory: $(pwd)"

echo "Building Docker image for ${DOCKER_IMAGE}"
docker build --platform linux/amd64 \
    -t ${REGISTRY_URL}/${DOCKER_IMAGE}:${TAG} \
    -f ./Dockerfile \
    --build-arg docker=${DOCKER_IMAGE} \
    .

# If RELEASE_TAG is set, also tag and push with that
if [[ -n "${RELEASE_TAG:-}" ]]; then
    docker tag ${REGISTRY_URL}/${DOCKER_IMAGE}:${TAG} ${REGISTRY_URL}/${DOCKER_IMAGE}:${RELEASE_TAG}
fi

docker push --all-tags ${REGISTRY_URL}/${DOCKER_IMAGE}

echo "Successfully built and pushed ${DOCKER_IMAGE} with tag ${TAG} ${RELEASE_TAG}"