resource "random_password" "main" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"

  keepers = {
    username = var.database_username
  }
}

module "postgresql" {
  source  = "GoogleCloudPlatform/sql-db/google//modules/postgresql"
  version = "~> 25.2.0"

  project_id           = var.project_id
  region               = var.region
  zone                 = var.zone
  name                 = "primary-instance"
  random_instance_name = true

  user_labels      = var.common_resource_labels
  disk_autoresize  = true
  database_version = "POSTGRES_15"
  tier             = "db-custom-2-7680"

  deletion_protection      = false
  database_deletion_policy = "ABANDON"
  user_deletion_policy     = "ABANDON"

  additional_databases = [
    for db in var.databases : {
      name      = db
      charset   = ""
      collation = ""
    }
  ]

  additional_users = [
    {
      name            = random_password.main.keepers.username
      password        = random_password.main.result
      random_password = false
    }
  ]

  ip_configuration = {
    ipv4_enabled        = false
    private_network     = var.network_config.network_self_link
    require_ssl         = true
    authorized_networks = []

    enable_private_path_for_google_cloud_services = true
  }
}

resource "google_secret_manager_secret" "database_config" {
  project   = var.project_id
  secret_id = "${local.root_name}-database-config"

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "database_config" {
  secret = google_secret_manager_secret.database_config.id

  secret_data = jsonencode({
    host     = "localhost"
    port     = 5432
    username = var.database_username
    password = random_password.main.result
  })
}
