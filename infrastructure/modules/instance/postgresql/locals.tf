locals {
  # Resource naming
  resource_namespace        = "postgresql"
  unique_root_name          = "${var.instance_prefix}-${local.resource_namespace}"
  globally_unique_root_name = "${var.naming_root}-${local.unique_root_name}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  migrations_root_name = "api-migration"

  # IAM roles
  artifact_registry_roles = [
    "roles/artifactregistry.reader"
  ]

  migration_service_account_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/compute.networkUser",
    "roles/vpcaccess.user",
    "roles/cloudsql.client",
  ]

  # Environment variable groups
  secret_manager_secret_regex = var.secret_manager_secret_regex

  # Cloud Run config - base environment variables
  _base_cloud_run_env_vars = merge(
    var.environment_variables,
    {
      CLOUD_SQL_CONN_NAME      = module.postgresql.instance_connection_name
      CREATED_BY               = "terraform"
      ENVIRONMENT              = var.environment
      GCP_POSTGRES_SECRET_NAME = google_secret_manager_secret_version.database_config.name
      PROJECT_ID               = var.project_id
      TENANTS                  = jsonencode(var.databases)
    }
  )

  # Pre-process all environment variables to identify secrets
  _env_var_analysis = {
    for key, value in local._base_cloud_run_env_vars :
    key => {
      value     = value
      is_secret = can(regex(local.secret_manager_secret_regex, value))

      regex_match = (
        can(regex(local.secret_manager_secret_regex, value)) ?
        regex(local.secret_manager_secret_regex, value) : null
      )
    }
  }

  # if the value matches the secret version regex, it is a secret environment variable
  secret_env_variables = merge(
    {
      for key, analysis in local._env_var_analysis :
      key => {
        secret_id      = analysis.regex_match["secret_id"]
        secret_version = coalesce(analysis.regex_match["version"], "latest")
      }
      if analysis.is_secret
    },
    var.cloud_run_env_secret_vars
  )

  # if the value doesn't match the secret regex, it is a regular string environment variable
  string_env_variables = {
    for key, analysis in local._env_var_analysis :
    key => analysis.value
    if !analysis.is_secret
  }
}
