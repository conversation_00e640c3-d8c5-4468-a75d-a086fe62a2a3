# SERVICEPROJECT_CRAFTED_NETWORK
gitlab_project_shortname      = "api"
zone                          = "us-east1-b"
region                        = "us-east1"
env                           = "dev"
project_name                  = "ict" #TODO: project_team, team, project_tag, ict_project_name?
service_name                  = "api"
project_id                    = "ict-d-api"
host_project_id               = "ict-d-api"
edp_bigquery_project_id       = "edp-d-us-east2-etl"
edp_pubsub_topic_id           = "adi-data"
etl_project_id                = "ict-d-etl"
network_name                  = "dev-ict-d-api-private" # To be provided by DEVOPS
subnet_01_pub                 = "***********/24"
subnet_02_priv                = "***********/24"
vpc_serverless_connector_cidr = "***********/28" # This will create a /28 for association.. <<< MOVED >>> To Infra
# And... hodling this one for now, but we can't just make a /28, we can make a /24-/26 external, and Google Can Make Us A /28
# PLAN  = "192.168.{16,32}.0/20"    # ROOT + Prefix . 
# This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL
external_allocation_range = "***********" # ROOT + Prefix . This is for NON_REAL Google IP's which will be used for things like REDIS/CloudSQL

enterprise_search_secrets_name = "projects/ict-d-api/secrets/gen-ai-api-key/versions/latest"       # Configuration needed for ai api enterprise search key
enterprise_search_url          = "https://genai-gateway-6fdllguu.uc.gateway.dev/enterprise_search" # Configuration needed for ai api enterprise search url.

auth_domain   = "dev-zmogq9vd.us.auth0.com"
auth_audience = "https://dev.api.ict.dematic.dev"

dns_zone_name            = "ict-preprod"
dns_domain_name          = "ict.dematic.dev."
dns_project_id           = "ict-ops-management"
public_domain_name       = "dev.api.ict.dematic.dev" // SEE https://wiki.dematic.com/pages/viewpage.action?spaceKey=ICTI&title=Environment+Mapping+with+Public+URLs
green_public_domain_name = "dev.green.api.ict.dematic.dev"
blue_public_domain_name  = "dev.blue.api.ict.dematic.dev"

tenants = ["dematic_software", "ace_hardware", "dematic", "superior_uniform", "ict_development", "qa_automation", "drt_automation", "tti", "acehardware"]

# CloudRun Scaling
default_cloudrun_min = 0
default_cloudrun_max = 12

# aiml-data-explorer tagging variable
aiml_data_explorer_image_tag = "dev"

#dev aiml container url
aiml_container_endpoint_url = "https://opendataqnadev-v1-833546053256.us-central1.run.app"

# blue/green config
cloudrun_revision_tag          = "main"
cloudrun_revision_send_traffic = true

# Metric Processor Config
metric_processor_metrics_api_url = "https://dev-ict-etl-metric-processor-802648865058.us-east1.run.app"

cors_origin         = "*"
default_cors_origin = "https://dev.ict.dematic.dev"

# "CT DevOps Notifications" team channel for alert
alert_devops_teams_channel = "projects/ict-d-api/notificationChannels/8881514305768619915"

# Simulation 
simulation_api_url = "https://canvas-gateway-6uhnuu6m.uc.gateway.dev"
simulation_api_key = "projects/ict-d-api/secrets/ict-dev-simulation-api-key/versions/latest"

# AIML Data Explorer
aiml_data_explorer_url = "https://opendataqnadev-v1-ai.ops.dematic.dev"

# Agent AI
agent_ai_api_url = "https://weather-time-agent-833546053256.us-central1.run.app"

# Admin 
admin_api_url = "https://dev-zmogq9vd.us.auth0.com"
admin_api_key = "projects/ict-d-api/secrets/ict-dev-auth0-management-client-id/versions/latest"
