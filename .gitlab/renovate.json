{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:best-practices", ":dependencyDashboard", ":semanticCommits"], "prConcurrentLimit": 2, "platformAutomerge": true, "automergeType": "pr", "automergeStrategy": "merge-commit", "schedule": ["0 22-23 * * 1-5", "0 0-4 * * 1-5", "* * * * 0,6"], "separateMinorPatch": false, "separateMultipleMajor": false, "separateMajorMinor": false, "followTag": "latest", "minimumReleaseAge": "7 days", "packageRules": [{"groupName": "Security patches (immediate auto-merge when tests pass)", "matchUpdateTypes": ["patch"], "matchDepTypes": ["dependencies"], "automerge": true, "minimumReleaseAge": "0 days", "schedule": ["at any time"], "prNotPendingHours": 2, "labels": ["security"]}, {"groupName": "Safe auto-merge updates (when tests pass)", "matchUpdateTypes": ["patch"], "matchDatasources": ["npm"], "automerge": true, "minimumReleaseAge": "3 days", "schedule": ["at any time"], "excludePackagePatterns": ["^react", "^@react", "^typescript", "^@types/react", "^node"]}, {"groupName": "Minor updates (auto-merge when tests pass)", "matchUpdateTypes": ["minor"], "matchDatasources": ["npm"], "automerge": true, "minimumReleaseAge": "7 days", "schedule": ["* 0-5 * * 1"], "excludePackagePatterns": ["^react", "^@react", "^typescript", "^@types/react", "^node", "^vite", "^@vitejs", "^nx", "^@nx"]}, {"groupName": "Development tools (auto-merge when tests pass)", "matchPackagePatterns": ["^eslint", "^@eslint", "^prettier", "^@types/", "^jest", "^@jest", "^vitest", "^@vitest"], "matchUpdateTypes": ["minor", "patch"], "matchDatasources": ["npm"], "automerge": true, "minimumReleaseAge": "5 days", "schedule": ["* 0-5 * * 1"]}, {"groupName": "Major updates (manual review required)", "matchUpdateTypes": ["major"], "matchDatasources": ["npm"], "automerge": false, "minimumReleaseAge": "21 days", "schedule": ["* 0-5 1 * *"]}, {"groupName": "Critical framework updates (manual review)", "matchPackagePatterns": ["^react", "^@react", "^typescript", "^@types/react", "^node", "^vite", "^@vitejs", "^nx", "^@nx"], "matchDatasources": ["npm"], "automerge": false, "minimumReleaseAge": "14 days", "schedule": ["* 0-5 1 * *"]}, {"groupName": "Base Docker images", "matchDatasources": ["docker"], "matchPackageNames": ["node", "gcr.io/distroless/nodejs22-debian12"], "schedule": ["* 0-5 1 * *"], "minimumReleaseAge": "21 days", "automerge": true}, {"groupName": "Custom Docker images", "matchDatasources": ["docker"], "matchPackagePatterns": ["ict-.*"], "schedule": ["* 0-5 15 * *"], "minimumReleaseAge": "14 days", "automerge": true}], "ignoreDeps": ["opentofu", "@vaadin/grid", "@vaadin/virtual-list", "echarts", "chai", "@ict/sdk-foundations", "openapi-typescript", "openapicmd"], "rebaseWhen": "behind-base-branch", "updateNotScheduled": false, "prHourlyLimit": 2, "branchConcurrentLimit": 3, "vulnerabilityAlerts": {"enabled": true, "schedule": ["at any time"], "minimumReleaseAge": "0 days", "automerge": true}, "requiredStatusChecks": null, "prNotPendingHours": 24, "stabilityDays": 0, "stopUpdatingLabel": "no-auto-merge", "dependencyDashboardAutoclose": true, "commitMessagePrefix": "deps:", "commitMessageAction": "update", "commitMessageTopic": "{{depName}}", "commitMessageExtra": "to {{newVersion}}"}