stages:
  - validate
  - build
  - test
  - generate
  - pre-deploy
  - deploy
  - verify
  - scan

default:
  image: $GITLAB_RUNNER_IMAGE
  interruptible: true # allows the use of the "auto cancel redundant pipelines" feature
  timeout: 60m
  tags:
    - gke-runners-dematic
  retry:
    max: 2
    when: runner_system_failure
    exit_codes: 137

.if-protected-branch-pipeline: &if-protected-branch-pipeline
  if: '$CI_COMMIT_REF_PROTECTED == "true" || $CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED == "true"'

.if-contains-ui-changes: &if-contains-ui-changes
  <<: *if-protected-branch-pipeline
  changes:
    paths:
      - services/ui/**/*
      - infrastructure/configs/ui/**/*

.if-contains-api-changes: &if-contains-api-changes
  <<: *if-protected-branch-pipeline
  changes:
    paths:
      - services/api/**/*
      - infrastructure/configs/api/**/*

.if-contains-metric-processor-changes: &if-contains-metric-processor-changes
  <<: *if-protected-branch-pipeline
  changes:
    paths:
      - services/metric-processor/**/*
      - infrastructure/modules/instance/metric-processor/**/*

.if-contains-environment-changes: &if-contains-environment-changes
  <<: *if-protected-branch-pipeline
  changes:
    paths:
      - environments/**/*
      - services/api/**/*
      - services/ui/**/*

.if-contains-terraform-module-changes: &if-contains-terraform-module-changes
  <<: *if-protected-branch-pipeline
  changes:
    paths:
      - infrastructure/**/*

.if-contains-ict-sdk-changes: &if-contains-ict-sdk-changes
  changes:
    paths:
      - libs/shared/ict-sdk/**/*

.if-dev-commit-pipeline: &if-dev-commit-pipeline
  if: "$CI_COMMIT_BRANCH == $DEV_BRANCH"

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'

.if-dev-mr-contains-ui-changes: &if-dev-mr-contains-ui-changes
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
  changes:
    paths:
      - services/ui/**/*

.if-contains-ui-e2e-changes: &if-contains-ui-e2e-changes
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
  changes:
    paths:
      - services/ui-e2e/**/*

.if-stage-commit-pipeline: &if-stage-commit-pipeline
  if: "$CI_COMMIT_BRANCH == $STAGE_BRANCH"

.if-stage-mr-pipeline: &if-stage-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $STAGE_BRANCH'

.if-prod-commit-pipeline: &if-prod-commit-pipeline
  if: "$CI_COMMIT_BRANCH == $PROD_BRANCH"

.if-prod-mr-pipeline: &if-prod-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $PROD_BRANCH'

.if-protected-commit-pipeline: &if-protected-commit-pipeline
  if: "$CI_COMMIT_BRANCH != null && $CI_COMMIT_REF_PROTECTED"

.if-protected-mr-pipeline: &if-protected-mr-pipeline
  if: "$CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED"

.if-scheduled-regression-test: &if-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_TEST_ENV'

.if-web-triggered-test: &if-web-triggered-test
  if: '$CI_PIPELINE_SOURCE == "web" && $SUITE'

.prevent-auto-cancel: &prevent-auto-cancel
  auto_cancel:
    on_new_commit: none
    on_job_failure: none

workflow:
  auto_cancel:
    on_new_commit: conservative
  rules:    
    # ---------------------------------------------------------------------------------------------
    #  Run on merge request events targeting the dev, stage, and prod (main) branches.
    # ---------------------------------------------------------------------------------------------
    - <<: *if-dev-mr-pipeline
      variables:
        TARGET_BRANCH: "$DEV_BRANCH"
        TARGET_ENVIRONMENT: "dev"
        TARGET_LANDING_ZONE: "nonprod"
        TEST_SUITE_UI_URL: "$REVIEW_APP_URL"
        TEST_SUITE_DOWNSTREAM_UI_URL: "$DEV_UI_URL"
        # Once we have a preview env for API set the following env to the preview url.
        TEST_API_ENV: 'dev'
        TEST_API_DOWNSTREAM_ENV: 'dev'
        TEST_SUITE_API_URL: "$DEV_API_URL"
    - <<: *if-stage-mr-pipeline
      variables:
        TARGET_BRANCH: "$STAGE_BRANCH"
        TARGET_ENVIRONMENT: "stage"
        TARGET_LANDING_ZONE: "nonprod"
        TEST_SUITE_UI_URL: "$DEV_UI_URL"
        TEST_SUITE_DOWNSTREAM_UI_URL: "$STAGE_UI_URL"
        TEST_API_ENV: 'dev'
        TEST_API_DOWNSTREAM_ENV: 'stage'
        TEST_SUITE_API_URL: "$DEV_API_URL"
        TEST_SUITE_BASE_URL: "$DEV_API_URL"
    - <<: *if-prod-mr-pipeline
      variables:
        TARGET_BRANCH: "$PROD_BRANCH"
        TARGET_ENVIRONMENT: "prod"
        TARGET_LANDING_ZONE: "prod"
        TEST_SUITE_UI_URL: $STAGE_UI_URL
        TEST_SUITE_DOWNSTREAM_UI_URL: "$PROD_UI_URL"
        TEST_API_ENV: 'stage'
        TEST_API_DOWNSTREAM_ENV: 'prod'
        TEST_SUITE_API_URL: "$DEV_API_URL"
        TEST_SUITE_BASE_URL: "$STAGE_API_URL"
    # ---------------------------------------------------------------------------------------------
    #  Run for commits to the dev, stage, and main branches.
    # ---------------------------------------------------------------------------------------------
    - <<: [*if-dev-commit-pipeline, *prevent-auto-cancel]
      variables:
        DOWNSTREAM_BRANCH: "$STAGE_BRANCH"
        RELEASE_NOTES_SOURCE_BRANCH: "dev"
        RELEASE_NOTES_TARGET_BRANCH: "stage"
        TARGET_BRANCH: "$DEV_BRANCH"
        TARGET_ENVIRONMENT: "dev"
        TARGET_LANDING_ZONE: "nonprod"
    - <<: [*if-stage-commit-pipeline, *prevent-auto-cancel]
      variables:
        DOWNSTREAM_BRANCH: "$PROD_BRANCH"
        RELEASE_NOTES_SOURCE_BRANCH: "$STAGE_BRANCH"
        RELEASE_NOTES_TARGET_BRANCH: "$PROD_BRANCH"
        TARGET_BRANCH: "$STAGE_BRANCH"
        TARGET_ENVIRONMENT: "stage"
        TARGET_LANDING_ZONE: "nonprod"
    - <<: [*if-prod-commit-pipeline, *prevent-auto-cancel]
      variables:
        TARGET_BRANCH: "$PROD_BRANCH"
        TARGET_ENVIRONMENT: "prod"
        TARGET_LANDING_ZONE: "prod"
    # ---------------------------------------------------------------------------------------------
    # Allow triggered regression tests.
    # ---------------------------------------------------------------------------------------------
    - <<: *if-scheduled-regression-test
      variables:
        #PIPELINE_NAME: "Scheduled Regression Test Pipeline"
        TARGET_ENVIRONMENT: "$ICT_TEST_ENV"
    - <<: *if-web-triggered-test
      variables:
        #PIPELINE_NAME: "Web Triggered Test Pipeline"
        TARGET_ENVIRONMENT: "$ICT_TEST_ENV"
    # ---------------------------------------------------------------------------------------------
    # For any other branches, stop commit pipelines when there is an open merge request.
    # ---------------------------------------------------------------------------------------------
    - if: "$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS"
      when: never
    # ---------------------------------------------------------------------------------------------
    # Only run under the conditions listed above.
    # ---------------------------------------------------------------------------------------------
    - when: never

variables:
  GITLAB_RUNNER_IMAGE: "us-docker.pkg.dev/ict-o-registry/ict-o-registry/gitlab-runner:latest"
  DOCKER_DRIVER: "overlay2"
  DOCKER_TLS_CERTDIR: ""

  TFVAR_ROOT: $CI_PROJECT_DIR/infrastructure/configs
  ARTIFACTORY_AUTH_TOKEN: "$DEV_BLD_ARTIFACTORY_TOKEN"
  TF_STATE_NAME: "default"

  ARTIFACT_REGISTRY_LOCATION: "us"
  ARTIFACT_REGISTRY_PROJECT_ID: "ict-o-common-services-kfqd"
  API_PROJECT_ROOT: "$CI_PROJECT_DIR/services/api"
  API_E2E_PROJECT_ROOT: "$CI_PROJECT_DIR/services/api-e2e"
  UI_PROJECT_ROOT: "$CI_PROJECT_DIR/services/ui"
  UI_E2E_PROJECT_ROOT: "$CI_PROJECT_DIR/services/ui-e2e"
  UI_ARTIFACT_REGISTRY: "generic-artifacts"
  API_ARTIFACT_REGISTRY: "docker-images"

  DEV_BRANCH: "dev"
  STAGE_BRANCH: "stage"
  PROD_BRANCH: "main"

  DEV_API_URL: "https://dev.api.ict.dematic.dev" # dev API URL for UI project
  DEV_UI_URL: "https://dev.ict.dematic.dev/"
  STAGE_API_URL: "https://stage.api.ict.dematic.dev"
  STAGE_UI_URL: "https://stage-us-east1.ict.dematic.dev/"
  PROD_UI_URL: "https://ict.dematic.cloud/"
  PROD_API_URL: "https://api.ict.dematic.cloud"

  TEST_API_URL: "https://run-ict-d-api.ict.dematic.dev" # dev API URL for the API project
  REVIEW_APP_URL: "https://review-app-${CI_MERGE_REQUEST_ID}.ict.dematic.dev"

include:
  - local: .gitlab/ci/mr-labels.gitlab-ci.yml
  - local: .gitlab/ci/debug-jobs.gitlab-ci.yml
  - local: .gitlab/ci/static-analysis.gitlab-ci.yml
    rules:
      - <<: *if-dev-mr-pipeline
  - local: .gitlab/ci/service-release.gitlab-ci.yml
    rules:
      - <<: *if-protected-commit-pipeline
  - local: .gitlab/ci/api/*.gitlab-ci.yml
    rules:
      - <<: *if-contains-api-changes
  - local: .gitlab/ci/ui/*.gitlab-ci.yml
    rules:
      - <<: *if-contains-ui-changes
  - local: .gitlab/ci/api/api-build.gitlab-ci.yml
    rules:
      - <<: *if-contains-ict-sdk-changes
  - local: .gitlab/ci/ui/ui-build.gitlab-ci.yml
    rules:
      - <<: *if-contains-ict-sdk-changes
  # - local: .gitlab/ci/review-apps.gitlab-ci.yml
  #   rules:
  #     - <<: *if-dev-mr-contains-ui-changes
  #     - <<: *if-contains-ui-e2e-changes
  - local: .gitlab/ci/tests/e2e-ui.gitlab-ci.yml
    rules:
      - <<: [*if-stage-mr-pipeline, *if-prod-mr-pipeline]
  - local: .gitlab/ci/tests/e2e-api.gitlab-ci.yml
    rules:
      - <<: [*if-dev-mr-pipeline, *if-stage-mr-pipeline, *if-prod-mr-pipeline]
  - local: .gitlab/ci/infrastructure/terraform-module-publishing.gitlab-ci.yml
    rules:
      - <<: [ *if-contains-terraform-module-changes, *if-contains-metric-processor-changes ]
  - local: .gitlab/ci/infrastructure/environments-infrastructure.gitlab-ci.yml
    rules:
      - <<: *if-contains-environment-changes
