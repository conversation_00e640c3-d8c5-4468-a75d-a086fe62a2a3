# =====================================
# Stage Environment Variables
# =====================================

locals {
  lz_config = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  environment_folder_id     = "*************" # DematicNonProd/ControlTower/stage
  environment_folder_name   = "stage"
  environment_friendly_name = "Stage"
  environment_name          = "stage"

  auth0_config = {
    domain   = "control-tower-stage.us.auth0.com"
    audience = "https://stage.api.ict.dematic.dev"
  }

  edp_config = {
    project_id = "edp-s-us-east1-etl"
    pubsub_topic_id     = "adi-data"
  }

  ignition_upstream_url = "stage-dematic-software.ignition.ict.dematic.dev"

  metric_processor_url = "https://stage-ict-etl-metric-processor-************.us-east1.run.app"

  postgres_databases = [
    "dematic",
    "superior_uniform",
    "qa_manual",
    "ict_development",
    "qa_automation",
    "drt_automation",
    "stark_industries",
    "qa_manual_1",
    "verification_and_validation",
    "integration_test",
    "tti",
    "acehardware"
  ]
}
