locals {
  admin_api_url = "https://${var.auth0_config.domain}/api/v2"

  api_backend_common_config = {
    environment_variables = merge(
      local.cloud_run_environment_variables,
      {
        AUTH_DOMAIN         = var.auth0_config.domain
        AUTH_AUDIENCE       = var.auth0_config.audience
        CORS_ORIGIN         = var.lz_cors_origin
        DEFAULT_CORS_ORIGIN = module.ui_load_balancer.instance_base_url
        ENABLE_OTEL         = false
        ENVIRONMENT         = var.environment_name
        INSTANCE            = local.instance_prefix
        PROJECT_ID          = local.project_id
      },
    )

    security_roles = concat(
      var.global_api_security_roles,
      var.lz_api_security_roles,
      var.api_security_roles
    )
  }

  api_backend_default_config = {
    image_tag = "staging"
    region    = var.global_default_region

    basic_health_check_path = "healthcheck"
    full_health_check_path  = "healthcheck/full"

    artifact_registry_repository = data.terraform_remote_state.common.outputs.docker_repository

    backend_service_config = {
      protocol  = "HTTP"
      port_name = "http"
      timeout   = 30

      log_config = {
        enable = false
      }
    }

    bucket_config = {
      location                = "US"
      access_logs_bucket_name = module.scaffolding.access_logs_bucket_name
      storage_class           = "STANDARD"
    }

    cloud_run_config = var.lz_default_cloud_run_config
  }

  # Environment variable groups

  api_edp_environment_variables = {
    EDP_BIGQUERY_PROJECT_ID = var.edp_config.bigquery_project_id
    EDP_PUBSUB_TOPIC_ID     = var.edp_config.pubsub_topic_id
  }

  api_metric_processor_environment_variables = {
    METRIC_PROCESSOR_METRICS_API_URL = var.metric_processor_url
  }

  api_postgresql_environment_variables = {
    CLOUD_SQL_CONN_NAME      = module.postgresql.master_instance.connection_name
    GCP_POSTGRES_SECRET_NAME = module.postgresql.connection_string_secret_version
  }

  api_redis_environment_variables = {
    GCP_REDIS_SECRET_NAME = module.redis-cache.auth_string_secret_name
  }
}
