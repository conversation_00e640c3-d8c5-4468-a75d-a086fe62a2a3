# GCP Secret Manager SDK Usage Analysis - TypeScript Code

This document contains all instances where the Google Cloud Platform (GCP) Secret Manager SDK is being used in TypeScript code across the repository.

## Summary

Found **9 TypeScript files** that use GCP Secret Manager SDK:

### Core Implementation Files:

1. `services/api/libs/api/ict-api-foundations/src/secrets/gcp-secret-manager.ts` - Main implementation
2. `services/api/libs/api/ict-api-foundations/src/test/secrets/gcp-secret-manager.spec.ts` - Unit tests

### Database Connection Files:

3. `services/api/libs/api/ict-api-schema/src/datasources/data-explorer/cloud-migration-data-explorer-datasource.ts`
4. `services/api/libs/api/ict-api-schema/src/datasources/process-flow/cloud-migration-process-flow-datasource.ts`
5. `services/api/libs/api/ict-api-schema/src/datasources/config/cloud-migration-config-datasource.ts`

### Service Usage Files:

6. `services/api/apps/api/ict-ai-api/src/services/enterprise-search-service.ts`
7. `services/api/apps/api/ict-dataexplorer-api/src/services/ai-service.ts`

### Database Factory Files:

8. `services/api/libs/api/ict-api-foundations/src/db/neo4j-factory.ts`
9. `services/api/libs/api/ict-api-foundations/src/db/pg-postgres.ts`
10. `services/api/libs/api/ict-api-foundations/src/db/postgres.ts`

### Supporting Files:

- `services/api/libs/api/ict-api-foundations/src/db/database.ts` - Helper methods
- `services/api/libs/api/ict-api-foundations/src/secrets/secret-manager.ts` - Abstract base class

## Package Dependencies

The GCP Secret Manager SDK is included in:

- `services/api/package.json`: `"@google-cloud/secret-manager": "^6.0.0"`

---

## Detailed File Analysis

### 1. Core Implementation: `gcp-secret-manager.ts`

**Location**: `services/api/libs/api/ict-api-foundations/src/secrets/gcp-secret-manager.ts`

**Usage Pattern**:

- Imports `SecretManagerServiceClient` from `@google-cloud/secret-manager`
- Implements `GCPSecretManager` class extending abstract `SecretManager`
- Uses `accessSecretVersion()` method to retrieve secrets
- Creates client with optional projectId

**Key Methods**:

- `get(query: SecretManagerQuery): Promise<string>` - Main secret retrieval method
- `client(projectId?: string)` - Creates SecretManagerServiceClient instance

### 2. Unit Tests: `gcp-secret-manager.spec.ts`

**Location**: `services/api/libs/api/ict-api-foundations/src/test/secrets/gcp-secret-manager.spec.ts`

**Usage Pattern**:

- Imports `SecretManagerServiceClient` for mocking
- Uses Sinon to stub the client and `accessSecretVersion` method
- Tests secret retrieval functionality

### 3. Database Datasources (3 files)

**Locations**:

- `services/api/libs/api/ict-api-schema/src/datasources/data-explorer/cloud-migration-data-explorer-datasource.ts`
- `services/api/libs/api/ict-api-schema/src/datasources/process-flow/cloud-migration-process-flow-datasource.ts`
- `services/api/libs/api/ict-api-schema/src/datasources/config/cloud-migration-config-datasource.ts`

**Usage Pattern** (identical across all 3 files):

```typescript
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";

const client = new SecretManagerServiceClient();
const [version] = await client.accessSecretVersion({
  name: secretName,
});
const secretString = version.payload.data.toString() ?? "";
const options = JSON.parse(secretString) as DataSourceOptions;
```

**Purpose**: Retrieve PostgreSQL connection configuration from GCP Secret Manager

### 4. Enterprise Search Service: `enterprise-search-service.ts`

**Location**: `services/api/apps/api/ict-ai-api/src/services/enterprise-search-service.ts`

**Usage Pattern**:

- Uses the `GCPSecretManager` class (not direct SDK)
- Retrieves GenAI API key from secrets
- Method: `setGenAiApiKey()`

### 5. AI Service: `ai-service.ts`

**Location**: `services/api/apps/api/ict-dataexplorer-api/src/services/ai-service.ts`

**Usage Pattern**:

- Uses the `GCPSecretManager` class via dependency injection
- Retrieves AIML invocation URL from secrets
- Method: `search()` - conditionally uses secret manager for non-local environments

### 6. Database Factories (3 files)

**Locations**:

- `services/api/libs/api/ict-api-foundations/src/db/neo4j-factory.ts`
- `services/api/libs/api/ict-api-foundations/src/db/pg-postgres.ts`
- `services/api/libs/api/ict-api-foundations/src/db/postgres.ts`

**Usage Pattern**:

- All use the `GCPSecretManager` class (not direct SDK)
- Retrieve database connection configurations
- Use dependency injection: `Container.get(GCPSecretManager)`

---

## Import Patterns Summary

### Direct SDK Imports:

```typescript
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
```

**Files**: 4 files (3 datasource files + 1 test file)

### Wrapper Class Imports:

```typescript
import { GCPSecretManager, GCPSecretManagerQuery } from "ict-api-foundations";
```

**Files**: 6 files (services and database factories)

## Usage Patterns Summary

1. **Direct SDK Usage**: 4 files create `SecretManagerServiceClient` directly
2. **Wrapper Class Usage**: 6 files use the `GCPSecretManager` wrapper class
3. **Dependency Injection**: Most wrapper usage employs DI pattern
4. **Environment Conditional**: Several files only use secrets in non-local environments

---

## Complete Code Snippets

### 1. Core Implementation: `gcp-secret-manager.ts`

```typescript
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { SecretManager, SecretManagerQuery } from "./secret-manager.ts";
import { DiService } from "../di/type-di.ts";

export class GCPSecretManagerQuery extends SecretManagerQuery {
  /**
   * Name of the version of secret to get.
   */
  name: string = "";
  projectId?: string;
}

@DiService()
export class GCPSecretManager extends SecretManager {
  async get(query: SecretManagerQuery): Promise<string> {
    const gcpQuery = query as GCPSecretManagerQuery;
    if (!gcpQuery) {
      throw new Error("Invalid Secret query.");
    }

    const [version] = await this.client(gcpQuery.projectId).accessSecretVersion(
      {
        name: gcpQuery.name,
      }
    );
    if (version.payload && version.payload.data) {
      return version.payload.data.toString();
    }

    throw new Error("Invalid Secret.");
  }

  client(projectId?: string) {
    return new SecretManagerServiceClient({ projectId });
  }
}
```

### 2. Unit Tests: `gcp-secret-manager.spec.ts`

```typescript
import { assert } from "chai";
import sinon from "sinon";
import { GCPSecretManager } from "../../secrets/gcp-secret-manager.ts";
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";

describe("GCPSecretManager", () => {
  const testSecretManager = new GCPSecretManager();

  before(() => {
    // replace the accessSecretVersion in the returned object from client() with a spy
    sinon.stub(testSecretManager, "client").callsFake(() => {
      const clientStub = sinon.createStubInstance(SecretManagerServiceClient);
      clientStub.accessSecretVersion.resolves([
        {
          payload: {
            data: Buffer.from("testSecret"),
          },
        },
      ]);
      return clientStub;
    });
  });
  after(() => {
    sinon.restore();
  });

  it("should return expected secret", async () => {
    const testSecret = await testSecretManager.get({});
    assert.equal(testSecret, "testSecret");
  });
});
```

### 3. Database Datasource Pattern (3 identical files)

**Files**:

- `cloud-migration-data-explorer-datasource.ts`
- `cloud-migration-process-flow-datasource.ts`
- `cloud-migration-config-datasource.ts`

```typescript
import { DataSource, DataSourceOptions } from "typeorm";
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { DataSourceUtils, EntityTypes } from "../../utils/datasource-utils.ts";

const buildDataSource = async () => {
  // get the postgres connection info from the secret manager
  const secretName = process.env.GCP_POSTGRES_SECRET_NAME;

  const client = new SecretManagerServiceClient();
  const [version] = await client.accessSecretVersion({
    name: secretName,
  });
  if (!version.payload || !version.payload.data) {
    throw new Error("Invalid Secret.");
  }
  const secretString = version.payload.data.toString() ?? "";

  const options = JSON.parse(secretString) as DataSourceOptions;
  if (!options) {
    throw new Error("Unable to parse DataSourceOptions from secret.");
  }
  // ... rest of datasource configuration
};
```

### 4. Enterprise Search Service: `enterprise-search-service.ts`

```typescript
import { GCPSecretManager } from "ict-api-foundations";

@DiService()
export class EnterpriseSearchService {
  constructor(private envService: EnvironmentService) {}

  private genAiApiKey: string | undefined;

  /**
   * Asynchrnously fetch and set the genai api key.
   */
  async setGenAiApiKey() {
    if (this.genAiApiKey) return;
    const gcpSecrets = new GCPSecretManager();
    this.genAiApiKey = await gcpSecrets.get({
      name: this.envService.enterpriseSearch.secretsName,
    }); // TODO: This needs to be variable based on environment.
  }
  // ... rest of service
}
```

### 5. AI Service: `ai-service.ts`

```typescript
import {
  Container,
  GCPSecretManager,
  GCPSecretManagerQuery,
  Environment,
} from "ict-api-foundations";

@DiService()
export class AIService {
  public async search(
    request: DataExplorerSearchAIRequest
  ): Promise<DataExplorerSearchAIResponse> {
    let aimlUrlBase: string;
    const nodeEnv = process.env.ENVIRONMENT;

    if (!nodeEnv || nodeEnv !== Environment.local) {
      // use secret manager here for non-dev environments
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        throw new Error(
          "A secret manager is required for accessing the AIML Invocation url."
        );
      }
      const query = AIService.generateAIMLInvocationUrl();
      aimlUrlBase = await secretManager.get(query);
    } else {
      aimlUrlBase = process.env.AIML_INVOCATION_URL || "";
    }
    // ... rest of method
  }

  private static generateAIMLInvocationUrl(): GCPSecretManagerQuery {
    const secretName = process.env.AIML_INVOCATION_URL || "";
    const query: GCPSecretManagerQuery = {
      name: secretName,
    };
    return query;
  }
}
```

### 6. Database Factory Files

#### Neo4J Factory: `neo4j-factory.ts`

```typescript
import {
  GCPSecretManager,
  GCPSecretManagerQuery,
} from "../secrets/gcp-secret-manager.ts";

export class Neo4JFactory {
  constructor(
    private secretManager: GCPSecretManager,
    private envService: EnvironmentService,
    private logger: WinstonLogger
  ) {}

  async createDriver(datasetId: string): Promise<Driver> {
    // ... local environment handling

    // Retrieve the Neo4J secrets from GCP Secret Manager
    const secretQuery = new GCPSecretManagerQuery();
    secretQuery.name = `projects/${this.envService.apiProject.projectId}/secrets/${datasetId}_AuraDB/versions/latest`;
    this.logger.info("Retrieving secret for Neo4J", { secretQuery, datasetId });
    const secretValue = await this.secretManager.get(secretQuery);
    neo4jConfig = JSON.parse(secretValue);

    // ... rest of method
  }
}
```

#### PostgreSQL Factories: `pg-postgres.ts` & `postgres.ts`

```typescript
import { Container } from "../di/type-di.ts";
import { GCPSecretManager } from "../secrets/gcp-secret-manager.ts";
import { Database } from "./database.ts";

export class Database {
  static async create(/* parameters */): Promise<DataSource> {
    const nodeEnv = envService.app.env;
    if (!nodeEnv || nodeEnv !== Environment.local) {
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        throw new Error(
          "A secret manager is required for connecting to a postgres db in this environment."
        );
      }

      const secretName = envService.postgresDb.gcpSecretName || "";
      const query = Database.generateDefaultSecretQuery(secretName);
      const secretString = await secretManager.get(query);
      const options = JSON.parse(secretString) as DataSourceOptions;
      // ... rest of configuration
    }
  }

  protected static generateDefaultSecretQuery(
    secretName: string,
    projectId?: string
  ): SecretManagerQuery {
    const query: GCPSecretManagerQuery = {
      name: secretName,
      projectId,
    };
    return query;
  }
}
```

---

## Key Findings

1. **Two Usage Patterns**: Direct SDK usage (4 files) vs. wrapper class usage (6 files)
2. **Centralized Implementation**: All wrapper usage goes through the `GCPSecretManager` class
3. **Environment Awareness**: Most implementations only use secrets in non-local environments
4. **Common Use Cases**: Database connections (PostgreSQL, Neo4J) and API keys (GenAI, AIML)
5. **Error Handling**: Consistent error handling patterns across implementations
6. **Dependency Injection**: Modern files use DI pattern with `Container.get(GCPSecretManager)`
